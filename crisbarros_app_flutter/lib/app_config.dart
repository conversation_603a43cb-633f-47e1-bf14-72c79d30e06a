import 'package:azzas_app_commons/azzas_app_commons.dart';

class CrisBarrosAppConfig {
  static const brand = Brand.crisBarros;

  static const posSomaStaging =
      "NTEzMWQyYjMtMjA1NS00MmM4LTlkZTEtYmNhYmMxYzgxNDg3";

  //TODO: voltar apontamentos para prod!
  static const cmsProductionUrl = "https://cms-cb-bf.somalabs.com.br/api";
  static const cmsProductionToken =
      "b265f06ff399ee825b11393350c2a6f21355838482fd9013e54425930af332a767bfb45abfd621ad44dd36c09d99f0ded1c0aee3c9c9a7eec20ab7849b46d3f3a16d712b7002018f2e14ff4e7552f41a19cd3d08416028e4e52058c251a45d9f3584be803eba3b4fff7e5b0237079934271af3d87683127fe58c611acb7077b0";

  static const cmsStagingUrl = "https://cms-cb-homolog.somalabs.com.br/api";
  static const cmsStagingToken = "ef2701a1b0ecb54b968ed2e6e1ba5906d134d305d9ea355eea592a09d9a25e552ba8bf4c0bcfe2b56eb3e95d14be24a87e390ff0a3daa74e0f773e6e0fb3a1240fec8a77cc31cfb4d660c16c76bdf5f727bf5ea3d17ddf0208caadeb1b16b2be060a8b3988070f2c3d32f1f2d27a0ebddbaf048309a832ba4519b14196bf041d";
  static Whatsapp whatsapp = Whatsapp(phone: "5511963860233");
  static Whatsapp whatsappLyna = Whatsapp(phone: "55***********");
  static Whatsapp whatsappLidia = Whatsapp(phone: "*************");
  static Whatsapp whatsappAnaLuiza = Whatsapp(phone: "*************");
  static Whatsapp whatsappAndre = Whatsapp(phone: "*************");

  static const faqUrl = "https://www.crisbarros.com.br/lp/trocas-e-devolucoes";
  static const privacyPolicyUrl =
      "https://www.crisbarros.com.br/institucional/politica-privacidade";
  static const storePage = "https://www.crisbarros.com.br/institucional/lojas";
  static const deleteAccountUrl = "https://azzas2154-privacy.my.onetrust.com/webform/************************************/4ffebccb-78a3-459d-9ed8-d17d34b3ef12";
 
 
  static final queryKeyOrderBy = "O";
  static final REGEX_PDP = RegExp(r'(\d+[A-Za-z]+\d+[A-Za-z\d]+)-(\d+)(?:-(\d+))?/p');

  static final deeplinkConfig = DeeplinkConfig(
  REGEX_PDP: CrisBarrosAppConfig.REGEX_PDP,
    queryKeyOrderBy: CrisBarrosAppConfig.queryKeyOrderBy,
    pdpMatchIdentifier: pdpMatchIdentifier,
  );
  static String? pdpMatchIdentifier(Uri uri){
   final match = REGEX_PDP.firstMatch(uri.path);
      return match != null
          ? '${match.group(1)?.toUpperCase()}_${match.group(2)}'
          : null;
}
      
}
