import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:crisbarros_app_flutter/app_theme/filter_management_theme.dart';
import 'package:crisbarros_app_flutter/app_theme/highlight_item_theme.dart';
import 'package:crisbarros_app_flutter/app_theme/suggestions_product_card_theme.dart';

import 'app_theme/app_theme_exp.dart';

class AppTheme {
  static AzzasTheme theme = AzzasTheme(
    button: ButtonTheme.value,
    spinner: SpinnerTheme.value,
    titleSection: TitleSectionTheme.value,
    subtitleSection: SubtitleSectionTheme.value,
    skuSelector: SkuSelectorTheme.value,
    line: LineTheme.value,
    feedbackCard: FeedbackCardTheme.value,
    toggle: ToggleTheme.value,
    productImage: ProductImageTheme.value,
    shimmer: ShimmerTheme.value,
    favoriteButton: FavoriteButtonTheme.value,
    checkbox: CheckboxTheme.value,
    radioButton: RadioButtonTheme.value,
    productInfo: ProductInfoTheme.value,
    checkoutProductCard: CheckoutProductCardTheme.value,
    wishlistProductCard: WishlistProductCardTheme.value,
    orderReviewProductCard: OrderReviewProductCardTheme.value,
    input: InputTheme.value,
    tags: TagTheme.value,
    filterButton: FilterButtonTheme.value,
    cardContent: CardContentTheme.value,
    checkoutInstallments: CheckoutInstallmentsTheme.value,
    checkoutResume: CheckoutResumeTheme.value,
    cardAddress: CardAddressTheme.value,
    filterManagement: FilterManagementTheme.value,
    tab: TabTheme.value,
    appBar: AppBarTheme.value,
    bottomBarTheme: BottomBarTheme.value,
    controllerCarrousel: ControllerCarrouselTheme.value,
    spotProduct: SpotProductTheme.value,
    plusButton: PlusButtonTheme.value,
    bottomSheetTheme: BottomSheetTheme.value,
    snackBar: SnackBarTheme.value,
    accordionMenu: AccordionMenuTheme.value,
    picker: PickerTheme.value,
    cepButton: CepButtonTheme.value,
    checkoutStep: CheckoutStepTheme.value,
    addressInfo: AddressInfoTheme.value,
    slider: SliderTheme.value,
    orderStatus: OrderStatusTheme.value,
    listItem: ListItemTheme.value,
    orderSummaryStatus: OrderSummaryTheme.value,
    cardOrderTheme: CardOrderTheme.value,
    orderCompleted: OrderCompletedTheme.value,
    colorSelector: ColorSelectorTheme.value,
    productInformation: ProductInformationTheme.value,
    productBox: ProductBoxTheme.value,
    pdpCarrouselTheme: PdpCarrouselTheme.value,
    highlightItemTheme: HighlightItemTheme.value,
    selectLoginMethodBottomSheet: SelectLoginBottomSheetTheme.value,
    videoContentTheme: VideoContentTheme.value,
    clocks: ClocksTheme.value,
    brandHeaderTheme: BrandHeaderTheme.value,
    badgeTheme: BadgeTheme.value,
    suggestionsProductCard: SuggestionsProductCardTheme.value,
  );
}
