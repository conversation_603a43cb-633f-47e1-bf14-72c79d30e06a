import 'package:azzas_analytics/azzas_analytics.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/firebase_ab_testing_service.dart';
import 'package:crisbarros_app_flutter/config/services/messaging/firebase_messaging.dart';
import 'package:crisbarros_app_flutter/config/services/remote_config/firebase_remote_config_service.dart';
import 'package:crisbarros_app_flutter/config/services/remote_config/remote_config_manager.dart';
import 'package:crisbarros_app_flutter/firebase_options.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class AppInitializer {
  static Future<void> initialize() async {
    await _initializeFirebase();

    await Future.wait([
      _initializeNotification(),
      _initializeRemoteConfig(),
      _initializeCrashlytics(),
      _initializeAppsflyer(),
      GetStorage.init(),
    ]);

    AzzasAnalytics.initialize(FirebaseAnalytics.instance);
    await _setAppsFlyerUIDinDefaultEventParameters();
    await _setNewCheckoutEventParameter();
  }

  static Future<void> _initializeAppsflyer() async {
    AzzasAppsflyer.initializeAppsFlyer(
      afDevKey: 'PvjnuM5UzkDTcvQpssdnQ4',
      appId: '6737728105',
    );
  }

  static Future<void> _initializeFirebase() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  static Future<void> _initializeRemoteConfig() async {
    try {
      final firebaseRemoteConfig = FirebaseRemoteConfigService();

      final remoteConfigManager = RemoteConfigManager(firebaseRemoteConfig);
      await remoteConfigManager.initialize();

      // Inicializar A/B Testing DEPOIS do Remote Config
      await _initializeABTesting(firebaseRemoteConfig);
    } catch (e) {
      debugPrint('❌ Erro na inicialização do Remote Config: $e');
    }
  }

  static Future<void> _initializeNotification() async {
    final notificationService = FirebaseNotificationService();
    await notificationService.initialize();
  }

  static Future<void> _initializeCrashlytics() async {
    if (kIsWeb) return;
    await Future.wait([
      FirebaseMethods.setupFirebaseCrashlytics(),
      FirebaseMethods.setupFirebasePerformance(),
    ]);
    FirebaseMethods.reportIsolateErrorsToCrashlytics();
    FlutterError.onError = FirebaseMethods.onFlutterError;
  }

  static Future<void> _setAppsFlyerUIDinDefaultEventParameters() async {
    await AzzasAppsflyer.appsflyerSdk
        .getAppsFlyerUID()
        .then((appsFlyerId) async {
      await FirebaseAnalytics.instance.setUserProperty(
        name: 'af_id',
        value: appsFlyerId,
      );
      await FirebaseAnalytics.instance.setDefaultEventParameters(
        {'af_id': appsFlyerId},
      );

      debugPrint('Success set AppsFlyer ID:');
    }).onError((error, stackTrace) {
      debugPrint('Error set AppsFlyer ID: $error');
    });
  }

  static Future<void> _initializeABTesting(
      FirebaseRemoteConfigService remoteConfig) async {
    try {
      // Cria a instância singleton do service!
      FirebaseABTestingService(remoteConfig: remoteConfig);
    } catch (e) {
      debugPrint('❌ Erro na inicialização do A/B Testing: $e');
    }
  }

  static Future<void> _setNewCheckoutEventParameter() async {
    try {
      final isNewCheckout = FirebaseABTestingService.instance
          .isExperimentEnabled(ABTestingConfig.experimentCheckoutFlow);

      await FirebaseAnalytics.instance.setDefaultEventParameters(
        {'new_checkout': '$isNewCheckout'},
      );
      debugPrint('✅ Parâmetro de evento do checkout definido com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao definir o parâmetro de evento do checkout: $e');
    }
  }
}
