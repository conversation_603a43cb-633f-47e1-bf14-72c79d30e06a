import 'package:azzas_app_commons/azzas_app_commons.dart';

/// Implementação do serviço de A/B Testing usando Insider
/// Esta é uma implementação placeholder que pode ser completada
/// quando a integração com Insider for necessária
class InsiderABTestingService implements ABTestingService {
  bool _isInitialized = false;

  // TODO: Adicionar dependências do Insider SDK quando disponível
  // late final InsiderSDK _insiderSDK;


  @override
  bool isExperimentEnabled(String experimentKey) {
    // TODO: implement isExperimentEnabled
    throw UnimplementedError();
  }

  @override
  String? getExperimentVariation(String experimentKey) {
    // TODO: implement getExperimentVariation
    throw UnimplementedError();
  }
}
