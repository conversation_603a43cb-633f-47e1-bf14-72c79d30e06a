import 'package:crisbarros_app_flutter/modules/bag/bag_page.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:flutter/material.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/firebase_ab_testing_service.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/backpack_page.dart';
import 'package:vertapps_checkout_whitelabel/tokens/brands_enum.dart';

/// Gerenciador centralizado para navegações de checkout com A/B Testing
class CheckoutNavigationManager {
  static const String _experimentKey = ABTestingConfig.experimentCheckoutFlow;

  static Future<void> goToBag({
    required CdsBackPackArgs newCheckoutArgs,
    required Object? legacyCheckoutArgs,
  }) async {
    try {
      final route = isNewCheckout ? '/checkout/backpack' : '/bag';
      final args = isNewCheckout ? newCheckoutArgs : legacyCheckoutArgs;

      Modular.to.pushNamed(route, arguments: args);
    } catch (error) {
      debugPrint('❌ Erro na navegação do checkout: $error');
    }
  }

  static Widget get bagTag {
    if (CheckoutNavigationManager.isNewCheckout) {
      return Scaffold(
        body: BackpackPage(
          brand: BrandEnum.crisbarros,
          backPackArgs: CdsBackPackArgs(
            hasCloseButton: false,
            paymentArgs: CdsPaymentArgs(
              goToBackPack: (bool? showAddressCard) {
                Modular.to.popUntil(ModalRoute.withName('/'));
                final backPackCubit = Modular.get<BagPageCubit>();
                backPackCubit.setShowAddressCard(showAddressCard);
                MainPage.goBag();
              },
              onTapViewBalance: () {
                Modular.to.popUntil((route) {
                  return route.settings.name == '/';
                });
                NavigatorDynamic.call('/perfil');
                Modular.to.pushNamed('/checking');
              },
            ),
          ),
        ),
      );
    }

    return const BagPage();
  }

  static Future<void> goToLogin({
    required Object? legacyCheckoutArgs,
  }) async {
    try {
      final route = isNewCheckout ? '/checkout/login' : '/account/login_email';
      final args = isNewCheckout ? null : legacyCheckoutArgs;

      Modular.to.pushNamed(route, arguments: args);
    } catch (error) {
      debugPrint('❌ Erro na navegação do checkout login: $error');
    }
  }

  static bool get isNewCheckout {
    try {
      final abTestingService = _getABTestingService();

      if (abTestingService == null) {
        debugPrint(
            '⚠️ ABTestingService não disponível, usando checkout padrão');
        return false;
      }

      final isEnabled = abTestingService.isExperimentEnabled(_experimentKey);
      debugPrint(
          '🛒 CheckoutNavigationManager: Experimento $_experimentKey ${isEnabled ? 'HABILITADO' : 'DESABILITADO'}');
      debugPrint('🎯 Usando ${isEnabled ? 'NOVO' : 'ANTIGO'} checkout');

      return isEnabled;
    } catch (e) {
      debugPrint('❌ Erro ao verificar experimento de checkout: $e');
      debugPrint('🔄 Usando checkout padrão por segurança');
      return false;
    }
  }

  static FirebaseABTestingService? _getABTestingService() {
    try {
      final service = FirebaseABTestingService.instance;
      debugPrint('✅ FirebaseABTestingService obtido com sucesso');
      return service;
    } catch (e) {
      debugPrint('⚠️ FirebaseABTestingService não disponível: $e');
      return null;
    }
  }
}
