import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/remote_config/firebase_remote_config_service.dart';
import 'package:flutter/foundation.dart';

/// Implementação singleton do serviço de A/B Testing usando Firebase Remote Config
///
/// Esta implementação utiliza o Firebase Remote Config para:
/// - Gerenciar experimentos A/B
/// - Controlar feature flags
class FirebaseABTestingService implements ABTestingService {
  FirebaseABTestingService._({
    required FirebaseRemoteConfigService remoteConfig,
  }) : _remoteConfig = remoteConfig;

  final FirebaseRemoteConfigService _remoteConfig;
  static FirebaseABTestingService? _instance;

  /// Cria ou retorna a instância singleton
  factory FirebaseABTestingService({
    required FirebaseRemoteConfigService remoteConfig,
  }) {
    _instance ??= FirebaseABTestingService._(remoteConfig: remoteConfig);
    return _instance!;
  }

  /// Getter para acessar a instância singleton
  static FirebaseABTestingService get instance {
    if (_instance == null) {
      throw Exception('⚠️ FirebaseABTestingService não foi inicializado!');
    }
    return _instance!;
  }

  @override
  String? getExperimentVariation(String experimentKey) {
    try {
      final variation = _remoteConfig.getString(experimentKey);

      if (variation.isNotEmpty) {
        debugPrint('✅ Variação encontrada para $experimentKey: $variation');
        return variation;
      }

      debugPrint(
          '⚠️ Nenhuma variação encontrada para o experimento $experimentKey');
      return null;
    } catch (e) {
      debugPrint('❌ Erro ao obter variação do experimento $experimentKey: $e');
      return null;
    }
  }

  @override
  bool isExperimentEnabled(String experimentKey) {
    try {
      final isEnabled = _remoteConfig.getBool(experimentKey);
      debugPrint(
          '🔍 Experimento $experimentKey está ${isEnabled ? 'habilitado' : 'desabilitado'}');
      return isEnabled;
    } catch (e) {
      debugPrint(
          '❌ Erro ao verificar se o experimento $experimentKey está habilitado: $e');
      return false;
    }
  }
}
