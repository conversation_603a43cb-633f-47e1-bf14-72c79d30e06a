import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';

/// Ajustar token quando implmentado na marca
class CmsProductCarouselImageTheme {
  static AzzasProductCarouselImageTheme get value {
    return AzzasProductCarouselImageTheme(
      titleStyle: Tokens.typography.headings.medium.mediumMedium
          .copyWith(color: Tokens.colors.typography.pure),
      subtitleStyle: Tokens.typography.body.small.smallRegular
          .copyWith(color: Tokens.colors.typography.pure),
      callToActionStyle: Tokens.typography.body.small.smallRegular
          .copyWith(color: Tokens.colors.typography.pure),
      spotHeight: SpotHeight.large,
      defaultTextColor: Tokens.colors.typography.pure,
      defaultBackgroundColor: Tokens.colors.neutral.pure,
    );
  }
}
