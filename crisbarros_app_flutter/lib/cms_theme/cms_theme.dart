import 'package:azzas_cms/azzas_cms_theme/azzas_cms_theme.dart';
import 'package:crisbarros_app_flutter/cms_theme/cms_list_links_with_icons_theme.dart';
import 'cms_theme_exp.dart';

class CmsTheme {
  static AzzasCmsTheme theme = AzzasCmsTheme(
    media: CmsMediaListTheme.value,
    categories: CmsCategoriesMosaicTheme.value,
    videoBanner: CmsVideoBannerTheme.value,
    contentArea: CmsContentAreaTheme.value,
    signUp: CmsSignUpTheme.value,
    showcaseBannerCarrousel: CmsShowcaseBannerCarrouselTheme.value,
    personalShowcase: CmsPersonalShowcaseTheme.value,
    videoGalleryTheme: CmsVideoGalleryTheme.value,
    showcaseListTheme: CmsShowcaseListTheme.value,
    couponBanner: CmsCouponBannerTheme.value,
    stories: CmsStoriesTheme.value,
    //TODO: tornar esse parametro opcional, nao é usado em CB
    spotProductClock: null,
    tipBar: CmsTipbarTheme.value,
    notificationCenter: CmsNotificationCenterTheme.value,
    gridCategoryListingTheme: CmsGridCategoryListingTheme.value,
    brandSloganTheme: CmsBrandSlogan.value,
    productsGridFilterTheme: CmsProductsGridFilterTheme.value,
    mediaContentTheme: CmsMediaContentTheme.value,
    bannerContentTextTheme: CmsBannerContentTextTheme.value,
    imageCompositionTheme: CmsImageCompositionTheme.value,
    listLinksWithIconsTheme: CmsListLinksWithIconsTheme.value,
    productCarouselImageTheme: CmsProductCarouselImageTheme.value,
  );
}
