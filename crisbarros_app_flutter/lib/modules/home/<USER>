import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/firebase_ab_testing_service.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:diacritic/diacritic.dart';
import 'package:flutter/material.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final homeCubit = Modular.get<HomeCubit>();
  final orderFormCubit = Modular.get<OrderFormCubit>();

  @override
  void initState() {
    super.initState();
    _setNewCheckoutUTMI();
    NavigationEvents.logPageView(local: 'home');
  }

  Future<void> _setNewCheckoutUTMI() async {
    try {
      if (orderFormCubit.orderForm.marketingData?.marketingTags?.isNotEmpty ==
          true) {
        return;
      }

      final isNewCheckout = FirebaseABTestingService.instance
          .isExperimentEnabled(ABTestingConfig.experimentCheckoutFlow);

      final utmiCampaignHelper =
          await Modular.get<UtmiCampaignHelper>().getDeviceTags();

      final marketingTags = [
        ...utmiCampaignHelper,
        'new_checkout:$isNewCheckout'
      ];
      final marketingData =
          OrderFormMarketingData(marketingTags: marketingTags);

      await Modular.get<OrderCheckoutHandler>().addMarketingTags(
        marketingData: marketingData,
      );
      debugPrint('✅ UTMI de novo checkout definido com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao definir o UTMI de novo checkout: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<HomeCubit, HomeState>(
        bloc: homeCubit,
        builder: (context, state) {
          return CustomScrollView(
            slivers: [
              // SliverAppBar(
              //   floating: true,
              //   surfaceTintColor: Tokens.colors.neutral.pure,
              //   elevation: 0,
              //   flexibleSpace: GestureDetector(
              //     onTap: () {
              //       Modular.to.pushNamed('/components');
              //     },
              //     child: const HomeHeader(
              //       imageHeight: 12,
              //       showRigthIcon: false,
              //     ),
              //   ),
              // ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final component = state.components[index];
                    return buildComponentWidget(component, index);
                  },
                  childCount: state.components.length,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _onSearchTap() async {
    MainPage.goMenu();
  }

  Widget buildComponentWidget(dynamic component, int index) {
    switch (component.runtimeType) {
      // ====================================================
      // Categories Mosaic Component
      // ====================================================
      case CategoriesMosaicCmsComponent:
        final categoriesMosaicComponent =
            component as CategoriesMosaicCmsComponent;
        return CategoriesMosaicCmsWidget.fromCategoriesMosaicCmsComponent(
          categoriesMosaicComponent,
          onItemSelected: (categoryItem, itemIndex) async {
            AzzasAnalyticsEvents.logSelectContent(
                contentType:
                    'home:categorias-em-destaque:${categoryItem.bannerNameGA4?.creativeName ?? ''}');
          },
        );

      // ====================================================
      // Spot Product Component
      // ====================================================
      case SpotProductCmsComponent:
        final spotComponent = component as SpotProductCmsComponent;

        return SpotProductCmsWidget.fromSpotProductCmsComponent(
          spotComponent,
        );

      // ====================================================
      // Content Area With Sign Component
      // ====================================================
      case ContentAreaWithSignCmsComponent:
        final contentAreaWithSignComponent =
            component as ContentAreaWithSignCmsComponent;
        return ContentAreaCompleteCmsWidget.fromComponent(
          contentAreaWithSignComponent,
          CheckoutNavigationManager.isNewCheckout,
        );

      // ====================================================
      // Product Mosaic Component
      // ====================================================
      case ProductMosaicCmsComponent:
        final productMosaicComponent = component as ProductMosaicCmsComponent;
        return ProductsMosaicCmsWidget.fromComponent(productMosaicComponent);

      // ====================================================
      // Media List Banner Component
      // ====================================================
      case MediaListBannerComponent:
        final mediaListBannerComponent = component as MediaListBannerComponent;
        const label = 'home-media-list-banner';

        return MediaListBannerCompleteCmsWidget.fromComponent(
          mediaListBannerComponent,
          onSearchTap: _onSearchTap,
          onVisible: (mediaItem, bannerIndex) async {
            AzzasAnalyticsEvents.logViewPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName: mediaItem.bannerNameGA4?.creativeName ?? '',
                promotionName: mediaItem.bannerNameGA4?.promotionName ?? '',
                creativeSlot:
                    '$label:${index.toString()}:${bannerIndex.toString()}',
                promotionId: mediaItem.navigateTo ?? '',
              ),
            );
          },
          onItemSelected: (mediaItem, bannerIndex) async {
            AzzasAnalyticsEvents.logSelectPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName: mediaItem.bannerNameGA4?.creativeName ?? '',
                promotionName: mediaItem.bannerNameGA4?.promotionName ?? '',
                creativeSlot:
                    '$label:${index.toString()}:${bannerIndex.toString()}',
                promotionId: mediaItem.navigateTo ?? '',
              ),
            );
          },
        );

      // ====================================================
      // Personal Showcase Component
      // ====================================================
      case PersonalShowcaseCmsComponent:
        final personalShowcaseComponent =
            component as PersonalShowcaseCmsComponent;
        return PersonalShowcaseCompleteCmsWidget.fromComponent(
          personalShowcaseComponent,
        );

      // ====================================================
      // Showcase Banner Carrousel Component
      // ====================================================
      case ShowcaseBannerCarrouselCmsComponent:
        final showcaseBannerCarrouselComponent =
            component as ShowcaseBannerCarrouselCmsComponent;
        const label = 'home-showcase-banner-carrousel';

        return ShowcaseBannerListCmsWidget.fromComponent(
          showcaseBannerCarrouselComponent,
          onVisible: (bannerItem, itemIndex) async {
            AzzasAnalyticsEvents.logViewPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName: bannerItem.bannerNameGA4?.creativeName ?? '',
                promotionName: bannerItem.bannerNameGA4?.promotionName ?? '',
                creativeSlot:
                    '$label:${index.toString()}:${itemIndex.toString()}',
                promotionId: bannerItem.navigateTo,
              ),
            );
          },
          onItemSelected: (bannerItem, itemIndex) async {
            AzzasAnalyticsEvents.logSelectPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName: bannerItem.bannerNameGA4?.creativeName ?? '',
                promotionName: bannerItem.bannerNameGA4?.promotionName ?? '',
                creativeSlot:
                    '$label:${index.toString()}:${itemIndex.toString()}',
                promotionId: bannerItem.navigateTo,
              ),
            );
          },
        );

      // ====================================================
      // Video Banner Component
      // ====================================================
      case VideoBannerComponent:
        final videoBannerComponent = component as VideoBannerComponent;
        return VideoBannerCompleteCmsWidget.fromComponent(
          videoBannerComponent,
          onVisible: () async {
            AzzasAnalyticsEvents.logViewPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName:
                    videoBannerComponent.bannerNameGA4?.creativeName ?? '',
                promotionName:
                    videoBannerComponent.bannerNameGA4?.promotionName ?? '',
                creativeSlot: 'home-video-banner:${index.toString()}',
                promotionId: videoBannerComponent.filterCategoryOrCluster ?? '',
              ),
            );
          },
          onCallToActionCallback: () async {
            AzzasAnalyticsEvents.logSelectPromotion(
              promotion: AzzasAnalyticsPromotion(
                creativeName:
                    videoBannerComponent.bannerNameGA4?.creativeName ?? '',
                promotionName:
                    videoBannerComponent.bannerNameGA4?.promotionName ?? '',
                creativeSlot: 'home-video-banner:${index.toString()}',
                promotionId: videoBannerComponent.filterCategoryOrCluster ?? '',
              ),
            );
          },
        );

      default:
        return const SizedBox.shrink();
    }
  }

  String sanitize(String input) {
    final withoutDiacritics = removeDiacritics(input);
    return withoutDiacritics
        .toUpperCase()
        .replaceAll(RegExp(r'[^A-Z0-9\s]'), '')
        .trim()
        .toLowerCase();
  }
}
