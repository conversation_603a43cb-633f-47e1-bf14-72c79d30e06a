import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class TapumePage extends StatefulWidget {
  const TapumePage({Key? key}) : super(key: key);

  @override
  State<TapumePage> createState() => _TapumePageState();
}

class _TapumePageState extends State<TapumePage> {
  final tapumeController = Modular.get<TapumeCubit>();
  final redirectUtils = Modular.get<RedirectUtils>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'tapume');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final tapume = tapumeController.state.tapume;

    if (tapume == null) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      body: SizedBox(
        height: height,
        width: width,
        child: GestureDetector(
            onTap: () async {
              if (tapume.isCountdown) {
                return;
              }

              if (tapume.urlRedirect != null &&
                  await redirectUtils.canLaunch(tapume.urlRedirect ?? '')) {
                await redirectUtils.launchUri(
                  Uri.parse(tapume.urlRedirect ?? ''),
                );
              }
            },
            child: Stack(
              fit: StackFit.expand,
              children: [
                AzzasCachedNetworkingImage(
                  width: width,
                  height: height,
                  imageUrl: tapume.image.attributes.url,
                  fit: BoxFit.cover,
                ),
                if (tapume.isCountdown) ...[
                  Center(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CountdownWidgetWrapping(
                          startTime: tapume.startTime ?? DateTime.now(),
                          endTime: tapume.endTime ?? DateTime.now(),
                          onFinish: () {
                            Modular.to.pushNamed('/');
                          },
                          builder: (context, remaining) {
                            return _CountdownWidget(
                              theme: tapume.countdownTheme,
                              hours: remaining.hours,
                              minutes: remaining.minutes,
                              seconds: remaining.seconds,
                            );
                          },
                        ),
                        SizedBox(height: 215),
                      ],
                    ),
                  ),
                ]
              ],
            )),
      ),
    );
  }
}

class _CountdownWidget extends StatelessWidget {
  final CountdownTheme theme;
  final String hours;
  final String minutes;
  final String seconds;

  const _CountdownWidget({
    required this.theme,
    required this.hours,
    required this.minutes,
    required this.seconds,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme == CountdownTheme.light
            ? Colors.transparent
            : Tokens.colors.neutral.pure,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _TimeBox(value: hours, label: 'HORAS', theme: theme),
          _Separator(theme: theme),
          _TimeBox(value: minutes, label: 'MINS', theme: theme),
          _Separator(theme: theme),
          _TimeBox(value: seconds, label: 'SEGS', theme: theme),
        ],
      ),
    );
  }
}

class _TimeBox extends StatelessWidget {
  final String value;
  final String label;
  final CountdownTheme theme;

  const _TimeBox({
    required this.value,
    required this.label,
    required this.theme,
  });

  TextStyle _getLabelStyle() {
    switch (theme) {
      case CountdownTheme.light:
        return Tokens.typography.body.large.largeRegular.copyWith(
          color: Tokens.colors.typography.pure,
        );
      case CountdownTheme.dark:
        return Tokens.typography.body.medium.mediumRegular.copyWith(
          color: Tokens.colors.typography.light,
        );
    }
  }

  TextStyle _getValueStyle() {
    switch (theme) {
      case CountdownTheme.light:
        return Tokens.typography.headings.medium.mediumRegular.copyWith(
          color: Tokens.colors.typography.pure,
        );
      case CountdownTheme.dark:
        return Tokens.typography.body.large.largeRegular.copyWith(
          color: Tokens.colors.typography.light,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(value, style: _getValueStyle()),
        Text(label, style: _getLabelStyle()),
      ],
    );
  }
}

class _Separator extends StatelessWidget {
  final CountdownTheme theme;

  const _Separator({required this.theme});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        height: 8,
        width: 1,
        color: theme == CountdownTheme.light
            ? Tokens.colors.neutral.pure
            : Tokens.colors.typography.light,
      ),
    );
  }
}
