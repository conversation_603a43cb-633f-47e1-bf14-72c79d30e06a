import 'dart:math';

import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/pdc/pdc_state.dart';
import 'package:crisbar<PERSON>_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class FilterManagementPage extends StatefulWidget {
  const FilterManagementPage({super.key, required this.pdcCubit});

  final PdcCubit pdcCubit;

  @override
  State<FilterManagementPage> createState() => _FilterManagementPageState();
}

class _FilterManagementPageState extends State<FilterManagementPage> {
  @override
  void initState() {
    NavigationEvents.logPageView(local: 'pdc_filter_management');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<PdcCubit, PdcState>(
          bloc: widget.pdcCubit,
          builder: (context, state) {
            return SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(
                            height: 56,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      right: 4.0, left: 8.0),
                                  child: IconButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    icon: Icon(
                                      Tokens.icons.navigation.left,
                                      size: 32.0,
                                    ),
                                  ),
                                ),
                                Text(
                                  'Gerenciar filtros',
                                  style: Tokens
                                      .typography.body.medium.mediumRegular,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Tokens.spacing.spacingMedium,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: Tokens.spacing.spacingXLarge,
                                ),
                                AzzasFilterManagement(
                                  local: "pageManagement",
                                  title: "Categoria",
                                  filterOptions: {
                                    "Categoria":
                                        widget.pdcCubit.getFiltersValuesByType(
                                      FilterType.categories,
                                    ),
                                  },
                                  filterType: FilterTypeUi.categories,
                                  selectedFilters: widget.pdcCubit
                                      .getSelectedFiltersValuesByType(
                                    FilterType.categories,
                                  ),
                                  onFilterChanged: (filterKey, dynamic value) {
                                    final filter =
                                        widget.pdcCubit.getFilterByName(
                                      FilterType.categories,
                                      value,
                                    );

                                    if (filter == null) return;

                                    widget.pdcCubit.toggleFilter(
                                      filter: filter,
                                      type: FilterType.categories,
                                    );
                                  },
                                ),
                                SizedBox(
                                  height: Tokens.spacing.spacingXLarge,
                                ),
                                AzzasFilterManagement(
                                  local: "pageManagement",
                                  title: "Tamanho",
                                  filterOptions: {
                                    "Tamanho":
                                        widget.pdcCubit.getFiltersValuesByType(
                                      FilterType.sizes,
                                    ),
                                  },
                                  filterType: FilterTypeUi.sizes,
                                  selectedFilters: widget.pdcCubit
                                      .getSelectedFiltersValuesByType(
                                    FilterType.sizes,
                                  ),
                                  onFilterChanged: (filterKey, dynamic value) {
                                    final filter =
                                        widget.pdcCubit.getFilterByName(
                                      FilterType.sizes,
                                      value,
                                    );

                                    if (filter == null) return;

                                    widget.pdcCubit.toggleFilter(
                                      filter: filter,
                                      type: FilterType.sizes,
                                    );
                                  },
                                ),
                                SizedBox(
                                  height: Tokens.spacing.spacingXLarge,
                                ),
                                Builder(builder: (context) {
                                  double selectedMinValue =
                                      state.filterPriceSelected.minValue == 0
                                          ? state.filterPrice.minValue
                                          : state.filterPriceSelected.minValue;

                                  double selectedMaxValue =
                                      state.filterPriceSelected.maxValue == 0
                                          ? state.filterPrice.maxValue
                                          : state.filterPriceSelected.maxValue;

                                  final filterMaxValue =
                                      state.filterPrice.maxValue;
                                  final filterMinValue =
                                      state.filterPrice.minValue;

                                  final currentMinValue = max(
                                      min(selectedMinValue, selectedMaxValue),
                                      filterMinValue);
                                  final currentMaxValue = min(
                                      max(selectedMinValue, selectedMaxValue),
                                      filterMaxValue);

                                  return AzzasFilterManagement(
                                    local: "pageManagement",
                                    title: "Preço",
                                    filterType: FilterTypeUi.price,
                                    leftSupportingText:
                                        "R\$ ${currentMinValue.toStringAsFixed(0)}",
                                    rightSupportingText:
                                        "R\$ ${currentMaxValue.toStringAsFixed(0)}",
                                    sliderMin: filterMinValue,
                                    sliderMax: filterMaxValue,
                                    currentRangeValues: RangeValues(
                                        currentMinValue, currentMaxValue),
                                    onFilterChangeEnd: (_, dynamic value) {
                                      value as Map<String, dynamic>;
                                      final filterPrice = FilterPrice(
                                        minValue: value['min'],
                                        maxValue: value['max'],
                                      );
                                      widget.pdcCubit.changePrice(filterPrice);
                                      widget.pdcCubit.newIntelligentSearch();
                                    },
                                    onFilterChanged: (_, dynamic value) {
                                      value as Map<String, dynamic>;
                                      final filterPrice = FilterPrice(
                                        minValue: value['min'],
                                        maxValue: value['max'],
                                      );
                                      widget.pdcCubit.changePrice(filterPrice);
                                    },
                                  );
                                }),
                                SizedBox(
                                  height: Tokens.spacing.spacingXLarge,
                                ),
                                AzzasFilterManagement(
                                  local: "bottomSheet",
                                  title: "Cores",
                                  filterOptions: {
                                    "Cores":
                                        widget.pdcCubit.getFiltersValuesByType(
                                      FilterType.colors,
                                    )
                                  },
                                  filterType: FilterTypeUi.colors,
                                  selectedFilters: widget.pdcCubit
                                      .getSelectedFiltersValuesByType(
                                    FilterType.colors,
                                  ),
                                  onFilterChanged: (filterKey, dynamic value) {
                                    final filter =
                                        widget.pdcCubit.getFilterByName(
                                      FilterType.colors,
                                      value,
                                    );

                                    if (filter == null) return;

                                    widget.pdcCubit.toggleFilter(
                                      filter: filter,
                                      type: FilterType.colors,
                                    );
                                  },
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: AzzasPrimaryButton(
                      expanded: true,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      isLoading: state.isLoading || state.isLoadingMore,
                      trailing: Icon(
                        Tokens.icons.navigation.right,
                        size: 24,
                      ),
                      child: Text('${state.pagination.total} Resultados'),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}
