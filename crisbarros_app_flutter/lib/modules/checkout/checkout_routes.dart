import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/modules/checkout/order_completed_page.dart';
import 'package:crisbarros_app_flutter/modules/payment/select_payment_page.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/credit_card_payment_info/card_payment_info.dart';

class CheckoutRoutes {
  static routes(RouteManager r) {
    r.child(
      '/order_completed',
      child: (context) => OrderCompletedPage(
        orderId: r.args.data,
      ),
    );
    r.child('/select-payment', child: (context) => const SelectPaymentPage());
    r.child('/card-payment-info', child: (context) => const CardPaymentInfoPage());
  }
}
