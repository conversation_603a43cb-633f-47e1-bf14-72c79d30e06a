import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class OrderReviewDeliveryPackages extends StatefulWidget {
  final OrderFormCubit orderFormCubit;
  const OrderReviewDeliveryPackages({super.key, required this.orderFormCubit});

  @override
  State<OrderReviewDeliveryPackages> createState() =>
      _OrderReviewDeliveryPackagesState();
}

class _OrderReviewDeliveryPackagesState
    extends State<OrderReviewDeliveryPackages> with TickerProviderStateMixin {
  void bottomSheetUpdateDeliveryType(
    BuildContext context, {
    required PackageModel pacote,
    required Future Function(AzzasDeliveryOption) selectDeliveryOption,
    required AzzasDeliveryOption? selectedDeliveryOption,
  }) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return AzzasUpdateDeliveryTypeBottomSheet(
          onCloseTap: () => Navigator.pop(context),
          selectedOption: pacote.deliveryOptionSelected.toSlaUi(),
          selectDeliveryOption: selectDeliveryOption,
          deliveryOptions: pacote.deliveryOptions
              .map((element) => element.toSlaUi())
              .toList(),
          selectedDeliveryOption: selectedDeliveryOption,
          titleStyle: Tokens.typography.headings.small.smallRegular,
          formatPrice: (price) {
            return CurrencyHelper.format(amount: price, dividedBy100: true);
          },
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    final titleSectionStyle =
        AzzasThemeProvider.of(context).titleSection.textStyle.copyWith(
              fontSize: 16,
            );
    final selectedAddress =
        widget.orderFormCubit.orderForm.shippingData?.firstSelectedAddress;

    return BlocBuilder<OrderFormCubit, OrderFormState>(
        bloc: widget.orderFormCubit,
        builder: (context, state) {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 10),
            itemCount: state.packages?.length ?? 0,
            itemBuilder: (context, index) {
              final package = state.packages?[index];
              final isLast = index == (state.packages?.length ?? 0) - 1;
              final products = state.orderForm?.items
                  ?.where((e) =>
                      package?.items.contains(e.id) ??
                      false) // Verifica se 'items' não é null e se contém o id
                  .toList() as List<OrderFormProduct>;
              String getShippingType() {
                if (package?.deliveryOptionSelected.name?.toLowerCase() ==
                    'normal') {
                  return 'Entrega Econômica';
                } else {
                  return 'Entrega Expressa';
                }
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: Tokens.spacing.spacingLarge),
                  Text(
                    'Entrega ${index + 1}: ${package?.deliveryOptionSelected.shippingEstimatedFormatted}',
                    style: titleSectionStyle,
                  ),
                  SizedBox(height: Tokens.spacing.spacingSmall),
                  for (final product in (products))
                    AzzasOrderReviewProductCard(
                      padding: EdgeInsets.symmetric(
                          vertical: Tokens.spacing.spacingXXSmall),
                      product: AzzasProductCardProductParams(
                        productId: product.productId ?? '',
                        imageUrl: product.getImagedeliveryDetail,
                        name: product.getNameWithoutSize ?? '',
                        currentPrice: product.price == 0
                            ? 'Grátis'
                            : CurrencyHelper.format(
                                amount: product.price, dividedBy100: true),
                        quantity: product.quantity,
                        size: product.getSize,
                        availability: product.availability,
                      ),
                    ),
                  SizedBox(height: Tokens.spacing.spacingXLarge),
                  AzzasCheckoutStepAddress(
                    addressParams: AzzasCheckoutStepAddressFilledParams(
                      neighborhood: selectedAddress?.neighborhood ?? '',
                      number: selectedAddress?.number ?? '',
                      postalCode: selectedAddress?.postalCode ?? '',
                      responsibleName: selectedAddress?.receiverName ?? '',
                      streetName: selectedAddress?.street ?? '',
                      labelCta: 'Alterar endereço',
                      onTapCta: () {
                        final cubit = Modular.get<MainPageCubit>();
                        if (cubit.state.mainPageTab == MainPageTab.bag) {
                          Modular.to.popUntil(ModalRoute.withName('/'));
                          widget.orderFormCubit.updateScrollToAddress(true);
                          Modular.to.pushNamed('/', arguments: true);
                        } else {
                          Modular.to.popUntil(ModalRoute.withName('/'));
                          Modular.to.pushNamed('/bag', arguments: true);
                        }
                      },
                      disableCta: index != 0,
                    ),
                  ),
                  SizedBox(height: Tokens.spacing.spacingMedium),
                  const AzzasLine(),
                  SizedBox(height: Tokens.spacing.spacingMedium),
                  AzzasCheckoutStepShipping(
                    shippingParams: AzzasCheckoutStepShippingFilledParams(
                      labelCta: 'Alterar',
                      onTapCta: () {
                        bottomSheetUpdateDeliveryType(
                          context,
                          pacote: package!,
                          selectDeliveryOption: (azzasDeliveryOption) async {
                            await widget.orderFormCubit
                                .updateDeliveryOption(azzasDeliveryOption);
                          },
                          selectedDeliveryOption: state.selectedDeliveryOption,
                        );
                      },
                      disableCta: (index == 0
                              ? ((state.packages?.length ?? 0) == 1 &&
                                  package?.deliveryOptions.length == 1)
                              : true) ||
                          (state.packages?.length ?? 0) > 1,
                      shippingPrice: package?.deliveryOptionSelected.price == 0
                          ? 'Grátis'
                          : CurrencyHelper.format(
                              amount: package?.deliveryOptionSelected.price,
                              dividedBy100: true),
                      shippingEstimatedTime: package?.deliveryOptionSelected
                              .shippingEstimatedFormatted
                              ?.replaceFirst('Receba em', 'Chega em') ??
                          '',
                      shippingType: getShippingType(),
                    ),
                  ),
                  if (!isLast) ...[
                    SizedBox(height: Tokens.spacing.spacingMedium),
                    const AzzasLine(),
                  ]
                ],
              );
            },
          );
        });
  }
}
