import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class OrderReviewPayment extends StatefulWidget {
  const OrderReviewPayment({super.key});

  @override
  State<OrderReviewPayment> createState() => OrderReviewPaymentState();
}

class OrderReviewPaymentState extends State<OrderReviewPayment>
    with TickerProviderStateMixin {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _paymentCubit = Modular.get<PaymentCubit>();
  InstallmentOrderForm? selectedInstallment;

  void _showAzzasSnackbar({required String message, required bool isError}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  Future<void> _selectNewInstallment() async {
    try {
      final creditCardInfo = _paymentCubit.state.creditCardInfo;
      await _paymentCubit.selectCreditCardPayment(
        creditCardInfo: creditCardInfo,
        installment: selectedInstallment!,
      );

      AzzasAnalyticsEvents.logSelectContent(
        contentType: 'pagamento:parcelamento:${selectedInstallment?.count}',
      );

      Navigator.of(context).pop();
    } catch (e) {
      _showAzzasSnackbar(
        message: "Ocorreu um erro ao selecionar o parcelamento",
        isError: true,
      );
    }
  }

  void showInstallmentsBottomSheet(BuildContext context) {
    showAzzasBottomSheet(
      context: context,
      vsync: this,
      onDismiss: () {
        setState(() {
          selectedInstallment =
              _paymentCubit.state.creditCardInfo.selectedInstallment;
        });
      },
      builder: (context) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, state) {
            final paymentSystemId =
                state.orderForm?.firstSelectedPayment?.paymentSystem;
            final availableInstallments = state.orderForm
                ?.getInstallmentOptionsByPaymentSystemId(paymentSystemId ?? '');
            final isLoadingOrderForm = state.isLoading;

            return StatefulBuilder(builder: (context, setState) {
              final hasInstallmentSelected = selectedInstallment != null;

              selectedInstallment ??=
                  _paymentCubit.state.creditCardInfo.selectedInstallment;

              return AzzasSelectInstallmentsBottomSheet(
                onCloseTap: () {
                  Navigator.of(context).pop();
                },
                showDragBar: true,
                borderRadius: BorderRadius.all(
                  Radius.circular(Tokens.spacing.spacingMedium),
                ),
                installmentsSubtitleTextStyle: Tokens
                    .typography.body.extraSmall.extraSmallRegular
                    .copyWith(
                  color: Tokens.colors.typography.medium,
                ),
                titleStyle: Tokens.typography.body.extraLarge.extraLargeRegular,
                isLoading: isLoadingOrderForm,
                isDisabled: !hasInstallmentSelected,
                onSelectInstallment: (installment) {
                  final selectedInstallment =
                      availableInstallments?.firstWhereOrNull(
                    (element) =>
                        element.count == installment.count &&
                        element.value == installment.value,
                  );

                  if (selectedInstallment == null) {
                    return _showAzzasSnackbar(
                      message: "Parcelamento não encontrado",
                      isError: true,
                    );
                  }

                  setState(() {
                    this.selectedInstallment = selectedInstallment;
                  });
                },
                onButtonPressed: () async {
                  await _selectNewInstallment();
                },
                installments: availableInstallments
                        ?.map(
                          (installment) => AzzasInstallmentInfoParams(
                            installmentText:
                                installment.label(showMultiplication: true),
                            description: installment.description,
                            isChecked: selectedInstallment?.label() ==
                                installment.label(),
                            count: installment.count ?? 0,
                            value: installment.value ?? 0,
                          ),
                        )
                        .toList() ??
                    [],
              );
            });
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderFormCubit, OrderFormState>(
      bloc: _orderFormCubit,
      builder: (context, state) {
        final isPixSelected = state.orderForm?.isPixSelected == true;
        final isCreditCardSelected =
            state.orderForm?.isCreditCardSelected == true;
        final isVoucher = _paymentCubit.isGiftCardSelected == true;
        final installmentsText = state.orderForm?.getCurrentInstallment;
        final paymentSystemId =
            state.orderForm?.firstSelectedPayment?.paymentSystem;

        final cardName =
            CreditCardHelper.getNameByPaymentSystem(paymentSystemId ?? '');

        return BlocBuilder<PaymentCubit, PaymentState>(
          bloc: _paymentCubit,
          builder: (context, paymentState) {
            final lastDigits = paymentState.creditCardInfo.cardNumber
                    ?.substring(
                        paymentState.creditCardInfo.cardNumber!.length - 4) ??
                '';

            final creditCardInfo = CreditCardInfo(
              changePaymentMethodLabel: "Alterar pagamento",
              onPaymentMethodChange: () async {
                await AnalyticsService.trackEvent('select_content', {
                  'content_type ':
                      'pagamento:pagamento-com-cartao:alterar-pagamento',
                });
                Modular.to.pushNamed('/checkout/select-payment');
              },
              onInstallmentsChange: () {
                showInstallmentsBottomSheet(context);
              },
              lastDigits: lastDigits,
              creditCardType: AzzasCreditCardType.fromString(cardName),
              installmentsLabel: installmentsText ?? '',
            );

            final pixInfo = PixInfo(
                changePaymentMethodLabel: "Alterar pagamento",
                onPaymentMethodChange: () async {
                  await AnalyticsService.trackEvent('select_content', {
                    'content_type ':
                        'pagamento:pagamento-com-pix:alterar-pagamento',
                  });
                  Modular.to.pushNamed('/checkout/select-payment');
                });
            return AzzasCheckoutStepPayment(
              paymentParams: AzzasCheckoutStepPaymentFilledParams(
                hasError: paymentState.creditCardErrorInfo != null,
                isOrderCompleted: false,
                voucherInfo: isVoucher == true
                    ? VoucherInfo(
                        additionalCreditCard:
                            isCreditCardSelected ? creditCardInfo : null,
                        additionalPix: isPixSelected ? pixInfo : null,
                        voucherCode: '',
                        voucherValue: CurrencyHelper.format(
                          amount: _orderFormCubit.orderForm.getGiftCardValues(),
                          dividedBy100: true,
                        ),
                        voucherName: '',
                        onPaymentMethodChange: () async {
                          await AnalyticsService.trackEvent('select_content', {
                            'content_type ':
                                'pagamento:pagamento-com-pix:alterar-pagamento',
                          });
                          Modular.to.pushNamed('/checkout/select-payment');
                        },
                      )
                    : null,
                pixInfo: isPixSelected ? pixInfo : null,
                creditCardInfo: isCreditCardSelected ? creditCardInfo : null,
              ),
            );
          },
        );
      },
    );
  }
}
