import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_add_to_bag_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_carousel_full_look.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_color_selector.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_delivery_preview.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_full_look_product_list.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_measurement_table_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_overview_description.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_size_selector.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_sold_out_bottom_sheet.dart';
import 'package:flutter/material.dart';

class PdpPage extends StatefulWidget {
  const PdpPage({super.key, required this.product});

  final Product product;

  @override
  State<PdpPage> createState() => _PdpPageState();
}

class _PdpPageState extends State<PdpPage> with TickerProviderStateMixin {
  ColorSelectorItem? colorSelectorItem;
  bool compositionExpanded = false;
  bool devolutionExpanded = false;
  bool partCareExpanded = false;
  bool _showBottomBar = false;
  final ScrollController _scrollController = ScrollController();
  final _pdpCubit = Modular.get<PdpCubit>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();

  @override
  void initState() {
    super.initState();
    _pdpCubit.fetchSelectedProduct(widget.product);
    _scrollController.addListener(_handleScroll);
    _loadImages(widget.product);
    _pdpCubit
        .getProductLookSuggestions(widget.product.productId ?? '')
        .then((_) => _logViewItemEvent());
  }

  void _navigateToBag() {
    CheckoutNavigationManager.goToBag(
      legacyCheckoutArgs: {
        'cameFromPdp': true,
      },
      newCheckoutArgs: CdsBackPackArgs(
        hasCloseButton: true,
        paymentArgs: CdsPaymentArgs(
          goToBackPack: (bool? showAddressCard) async {
            Modular.to.popUntil(ModalRoute.withName('/'));
            MainPage.goBag();
            final backPackCubit = Modular.get<BagPageCubit>();
            backPackCubit.setShowAddressCard(showAddressCard);
          },
          onTapViewBalance: () {
            Modular.to.popUntil((route) {
              return route.settings.name == '/';
            });
            NavigatorDynamic.call('/perfil');
            Modular.to.pushNamed('/checking');
          },
        ),
      ),
    ).then((_) {
      _logViewItemEvent();
    });
  }

  void _logViewItemEvent() {
    Modular.get<EventDispatcher>().logViewItem(
      availableGrid: widget.product.listSize,
      product: widget.product,
      itemCategory3: (_pdpCubit.state.similarProductsState
                  .suggestionsProductsIds?.isNotEmpty ??
              false)
          .toString(),
      index: 0,
    );
  }

  void _loadImages(Product product) async {
    final List<String> listImages =
        product.images.map((image) => image.imageUrl!).toList();
    for (String url in listImages) {
      CachedNetworkImageProvider(url).resolve(const ImageConfiguration());
    }
  }

  List<AzzasPdpCarouselMediaItem> _getMediaItems(Product product) {
    final images = product.images
        .map((image) {
          /// Remove a imagem da lista que é só da cor do produto
          if (image.imageLabel == '10') return null;
          return AzzasPdpCarouselMediaItem(
            mediaUrl: image.imageUrl ?? '',
            mediaType: CarouselMediaItemType.image,
          );
        })
        .whereType<AzzasPdpCarouselMediaItem>()
        .toList();

    return [
      ...images,
      if (product.video != null)
        AzzasPdpCarouselMediaItem(
          mediaUrl: product.video as String,
          mediaType: CarouselMediaItemType.video,
        ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContent.provideForDocument<AzzasCmsProductPdpDocument>(
      'product-pdp-crisbarros',
      documentParser: AzzasCmsProductPdpDocument.fromJson,
      child: BlocBuilder<PdpCubit, PdpState>(
        bloc: _pdpCubit,
        builder: (context, state) {
          final images = _getMediaItems(state.productSelected);
          final showSuggestionsLook =
              state.similarProductsState.suggestionsProductsIds?.isNotEmpty ==
                  true;

          if (showSuggestionsLook) {
            images.add(
              AzzasPdpCarouselMediaItem(
                mediaType: CarouselMediaItemType.otherLookProducts,
                mediaUrl: '',
                widget: PdpCarouselOtherLook(
                  productIdsSeparateByComma: state
                      .similarProductsState.suggestionsProductsIds
                      ?.join(','),
                  navigateToBag: () => _navigateToBag(),
                ),
              ),
            );
          }
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              body: Stack(
                children: [
                  CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      SliverToBoxAdapter(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AzzasPdpCarrousel(
                              onPrimaryButtonTap: () => _addToBagSizeSelection(
                                  state.productSelected, state.selectedSize),
                              onBagTap: () async {
                                _navigateToBag();
                              },
                              mediaItems: images,
                              product: AzzasSpotProductParams(
                                productTitle:
                                    state.productSelected.productName ?? '',
                                image: state.productSelected.imageSku,
                                currentPrice:
                                    state.productSelected.productFormattedPrice,
                                fullPrice: state.productSelected
                                            .productFormattedPrice !=
                                        state.productSelected
                                            .productFormattedListPrice
                                    ? state.productSelected
                                        .productFormattedListPrice
                                    : null,
                              ),
                              stampTags: AzzasProductsCmsStampTag(
                                product: state.productSelected,
                              ),
                            ),
                            SizedBox(
                              height: Tokens.spacing.spacingXLarge,
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: Tokens.spacing.spacingMedium,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                      bottom: Tokens.spacing.spacingXSmall,
                                    ),
                                    child: AzzasProductsCmsTags(
                                      product: state.productSelected,
                                      isPLP: false,
                                    ),
                                  ),
                                  AzzasProductInformation(
                                    title:
                                        state.productSelected.productName ?? '',
                                    ref: state.productSelected
                                            .productReferenceCode ??
                                        '',
                                    currentPrice: state
                                        .productSelected.productFormattedPrice,
                                    fullPrice: state.productSelected
                                        .productFormattedListPrice,
                                    installments: state
                                        .productSelected.bestInstallmentText,
                                    favoriteButton: AzzasFavoriteButton(
                                      index: 0,
                                      product: state.productSelected,
                                    ),
                                    shareIcon: Icon(Tokens.icons.action.share),
                                    onShareTap: () async {
                                      const storeHost = "www.crisbarros.com.br";
                                      final finalUrl = state.productSelected
                                          .detailUrl(storeHost);

                                      await Share.share(finalUrl ??
                                          state.productSelected.productName!);
                                    },
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: Tokens.spacing.spacingLarge,
                                    ),
                                    child: PdpColorSelector(
                                      selectedProduct: state.productSelected,
                                      products: state
                                          .similarProductsState.similarProducts,
                                      onSelected: (product) {
                                        _pdpCubit.onColorSelected(product);
                                        _scrollToTop();
                                        _loadImages(product);
                                      },
                                    ),
                                  ),
                                  const Divider(
                                    color: Color(0xFFE9E9E9),
                                    height: 0.5,
                                    thickness: 0.5,
                                  ),
                                  SizedBox(
                                    height: Tokens.spacing.spacingLarge,
                                  ),
                                  Text(
                                    'Tamanhos',
                                    style: Tokens
                                        .typography.body.medium.mediumRegular,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: Tokens.spacing.spacingMedium,
                                    ),
                                    child: PdpSizeSelector(
                                      onWarnMe: (_) {},
                                      product: state.productSelected,
                                      onSelectSize: _pdpCubit.setProductSize,
                                      selectedSize: state.selectedSize,
                                    ),
                                  ),
                                  Wrap(
                                    children: [
                                      ProductBoxButton(
                                        buttonText: 'Tabela de medidas',
                                        textStyle: Tokens.typography.body
                                            .extraSmall.extraSmallRegular,
                                        icon: Icon(
                                          Tokens.icons.navigation.right,
                                          size: 10,
                                          color: Tokens.colors.typography.light,
                                        ),
                                        onTap: () => _onMeasurementTableTap(
                                          state.productSelected,
                                        ),
                                      ),
                                      SizedBox(
                                        width: Tokens.spacing.spacingSmall,
                                      ),
                                      if (state
                                          .productSelected.hasSizeUnavailable)
                                        ProductBoxButton(
                                          buttonText: 'Avise-me',
                                          textStyle: Tokens.typography.body
                                              .extraSmall.extraSmallRegular,
                                          icon: Icon(
                                            Tokens.icons.navigation.right,
                                            size: 10,
                                            color:
                                                Tokens.colors.typography.light,
                                          ),
                                          onTap: () =>
                                              _showSoldOutProductBottomSheet(
                                                  ''),
                                        ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: Tokens.spacing.spacingXLarge,
                                  ),
                                  const Divider(
                                    color: Color(0xFFE9E9E9),
                                    height: 0.5,
                                    thickness: 0.5,
                                  ),
                                  SizedBox(
                                    height: Tokens.spacing.spacingXLarge,
                                  ),
                                  if (state.similarProductsState
                                          .suggestionsProductsIds?.isNotEmpty ==
                                      true)
                                    PdpFullLookProductList(
                                      productIdsSeparateByComma: state
                                          .similarProductsState
                                          .suggestionsProductsIds
                                          ?.join(','),
                                      navigateToBag: () {
                                        Navigator.of(context).pop();
                                        _navigateToBag();
                                      },
                                    ),
                                  SizedBox(
                                    height: Tokens.spacing.spacingXLarge,
                                  ),
                                  Text(
                                    'Cálculo de frete',
                                    style: Tokens
                                        .typography.body.medium.mediumRegular,
                                  ),
                                  SizedBox(
                                    height: Tokens.spacing.spacingLarge,
                                  ),
                                  PdpDeliveryPreview(controller: _pdpCubit),
                                  SizedBox(
                                    height: Tokens.spacing.spacingXLarge,
                                  ),
                                  const Divider(
                                    color: Color(0xFFE9E9E9),
                                    height: 0.5,
                                    thickness: 0.5,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: Tokens.spacing.spacingXLarge,
                                    ),
                                    child: PdpOverviewDescription(
                                      description:
                                          state.productSelected.description ??
                                              '',
                                    ),
                                  ),
                                  AzzasAccordionMenu(
                                    title: 'Composição',
                                    content: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: state.productSelected.composicao
                                              ?.map(
                                                (e) => Text(
                                                  e.toString(),
                                                  style: Tokens.typography.body
                                                      .small.smallRegular
                                                      .copyWith(
                                                    color: Tokens.colors
                                                        .typography.medium,
                                                  ),
                                                ),
                                              )
                                              .toList() ??
                                          [],
                                    ),
                                    expanded: compositionExpanded,
                                    onTap: () {
                                      setState(() {
                                        compositionExpanded =
                                            !compositionExpanded;
                                      });
                                    },
                                  ),
                                  AzzasAccordionMenu(
                                    title: 'Troca e devolução',
                                    content: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'A política de troca da Cris Barros é bastante flexível: aceitamos devoluções por qualquer motivo em até 07 (sete) dias corridos após o recebimento do seu pedido, e trocas até 30 dias. Além disso, o custo do frete da devolução de mercadoria é por nossa conta. \n\nOs produtos Cris Barros só poderão ser trocados em loja físicas ou devolvidos na opção "meus pedidos", clicando no botão "solicitar devolução”.\n\nSe ainda estiver com alguma dúvida, consulte nossas políticas.',
                                          style: Tokens.typography.body.small
                                              .smallRegular
                                              .copyWith(
                                            color:
                                                Tokens.colors.typography.medium,
                                          ),
                                        ),
                                        SizedBox(
                                            height:
                                                Tokens.spacing.spacingSmall),
                                        InkWell(
                                            onTap: () => NavigatorDynamic.call(
                                                  '/webview',
                                                  arguments: const WebViewParams(
                                                      url:
                                                          "https://www.crisbarros.com.br/lp/trocas-e-devolucoes"),
                                                ),
                                            child: Text(
                                                'Consultar políticas de trocas e devoluções',
                                                style: Tokens.typography.body
                                                    .small.smallRegular
                                                    .copyWith(
                                                  color: Tokens
                                                      .colors.typography.light,
                                                )))
                                      ],
                                    ),
                                    expanded: devolutionExpanded,
                                    onTap: () {
                                      setState(() {
                                        devolutionExpanded =
                                            !devolutionExpanded;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                            const _PdpDetailsCmsItems(),
                            const SizedBox(height: 150),
                          ],
                        ),
                      )
                    ],
                  ),
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                    left: 0,
                    right: 0,
                    bottom: _showBottomBar ? 0 : -150,
                    child: AzzasBottomBar(
                      title: state.productSelected.productName ?? '',
                      buttonText: 'Comprar',
                      onButtonTap: () => _addToBagSizeSelection(
                          state.productSelected, state.selectedSize),
                      content: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            children: [
                              if (state.productSelected
                                      .productFormattedListPrice !=
                                  state.productSelected
                                      .productFormattedPrice) ...[
                                Text(
                                  state.productSelected
                                      .productFormattedListPrice,
                                  style: Tokens.typography.body.extraSmall
                                      .extraSmallCaption
                                      .copyWith(
                                    color: const Color(0xFF777777),
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                              Text(
                                state.productSelected.productFormattedPrice,
                                style: Tokens.typography.body.extraSmall
                                    .extraSmallCaption,
                              ),
                            ],
                          ),
                          Text(
                            'em até ${state.productSelected.bestInstallment.numberOfInstallments}x',
                            style: Tokens
                                .typography.body.extraSmall.extraSmallRegular
                                .copyWith(
                              color: const Color(0xFF777777),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleScroll() {
    final currentOffset = _scrollController.offset;
    final maxScrollExtent = _scrollController.position.maxScrollExtent;

    final triggerOffset = maxScrollExtent / 4;

    if (currentOffset > triggerOffset && !_showBottomBar) {
      setState(() {
        _showBottomBar = true;
      });
    } else if (currentOffset <= triggerOffset && _showBottomBar) {
      setState(() {
        _showBottomBar = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 900),
      curve: Curves.linear,
    );
  }

  void _onMeasurementTableTap(Product product) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return PdpMeasurementTableBottomSheet(product: product);
      },
      vsync: this,
    );
  }

  void _addToBagSizeSelection(Product product, OrderFormItem? selectedSize) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return PdpAddToBagBottomSheet(
          item: product,
          controller: _pdpCubit,
          selectedSize: selectedSize,
          navigateToBag: () {
            Navigator.of(context).pop();
            _navigateToBag();
          },
        );
      },
      vsync: this,
    );
  }

  void _showSoldOutProductBottomSheet(String skuId) {
    showAzzasBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return PdpSoldOutBottomSheet(controller: _pdpCubit, skuId: skuId);
      },
      vsync: this,
    );
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }
}

class _PdpDetailsCmsItems extends StatelessWidget {
  const _PdpDetailsCmsItems();

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContent.provided<AzzasCmsProductPdpDocument>(
      selector: (document) => [
        document.attributes.highlightOptional,
        document.attributes.spotProduct,
      ],
    );
  }
}
