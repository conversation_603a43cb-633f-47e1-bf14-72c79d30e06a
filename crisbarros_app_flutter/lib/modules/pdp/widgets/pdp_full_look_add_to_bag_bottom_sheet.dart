import 'dart:async';

import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/cupertino.dart';

class PdpFullLookAddToBagBottomSheet extends StatefulWidget {
  final List<Product> products;
  final List<OrderFormItem> items;
  final PdpCubit pdpCubit;
  final void Function()? navigateToBag;

  const PdpFullLookAddToBagBottomSheet({
    super.key,
    required this.products,
    required this.items,
    required this.pdpCubit,
    required this.navigateToBag,
  });

  @override
  State<PdpFullLookAddToBagBottomSheet> createState() =>
      _PdpFullLookAddToBagBottomSheetState();
}

class _PdpFullLookAddToBagBottomSheetState
    extends State<PdpFullLookAddToBagBottomSheet>
    with TickerProviderStateMixin {
  final orderForm = Modular.get<OrderFormCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    addItemsToBag(widget.items);
  }

  @override
  void dispose() {
    super.dispose();
    widget.pdpCubit.updatePDPBagState(success: false);
  }

  Future<void> addItemsToBag(List<OrderFormItem> items) async {
    try {
      for (var item in items) {
        await widget.pdpCubit.addItem(orderAddItem: [
          OrderAddItem(
              id: item.itemId!,
              quantity: 1,
              seller: item.firstAvailableSeller?.sellerId ?? '')
        ]);
      }

      _eventDispatcher.logAddAllToCart(
        products: widget.products,
        items: items,
        azzasAnalyticsAddAllCart: AzzasAnalyticsAddAllCart(
          itemListPosition: 'corpo da pdp',
          itemListName: 'Complete o look',
        ),
      );
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PdpCubit, PdpState>(
      bloc: widget.pdpCubit,
      listener: (context, state) {
        if (state.pdpBagState.error) {
          AzzasSnackBar.show(
            context: context,
            message: 'ocorreu um erro inesperado tente novamente mais tarde!',
            status: SnackBarStatus.danger,
          );
        }
      },
      builder: (context, state) {
        return AzzasSkuSelectionBottomSheet(
          onCloseTap: () => Navigator.pop(context),
          title: widget.items.length > 1
              ? 'Produtos adicionados na sacola'
              : 'Produto adicionado na sacola',
          titleStyle: Tokens.typography.headings.small.smallRegular,
          action: AzzasPrimaryButton(
            isLoading: state.pdpBagState.purchaseNow,
            onPressed: widget.navigateToBag,
            expanded: true,
            child: const Text(
              'Ver sacola',
            ),
          ),
          content: Column(
            children: [
              SizedBox(
                height: widget.items.length > 1 ? 375.0 : 250.0,
                child: productCardList(),
              )
            ],
          ),
        );
      },
    );
  }

  Widget productCardList() {
    return ListView.builder(
      controller: ScrollController(),
      shrinkWrap: true,
      scrollDirection: Axis.vertical,
      itemCount: widget.items.length,
      itemBuilder: (BuildContext context, int index) {
        final product = widget.products[index];
        final item = widget.items[index];

        return AzzasCheckoutProductCard(
          imageHeight: 197.0,
          imageWidth: 130.0,
          product: AzzasProductCardProductParams(
            availability: product.availability,
            productId: product.productId ?? '',
            name: product.productName ?? '',
            currentPrice: product.productFormattedPrice,
            fullPrice: product.productFormattedPrice !=
                    product.productFormattedListPrice
                ? product.productFormattedListPrice
                : null,
            size: item.itemSize,
            quantity: 1,
            imageUrl: product.fullLookProductImage,
          ),
        );
      },
    );
  }
}

extension on Product {
  String get fullLookProductImage {
    final image = items?.first.images
        ?.firstWhereOrNull((image) => image.imageLabel == "11");

    return image?.imageUrl ?? items?.first.images?.first.imageUrl ?? '';
  }
}
