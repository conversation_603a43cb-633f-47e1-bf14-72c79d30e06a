import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_add_to_bag_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';

class PdpCarouselOtherLook extends StatefulWidget {
  final String? productIdsSeparateByComma;
  final VoidCallback navigateToBag;

  PdpCarouselOtherLook({
    this.productIdsSeparateByComma,
    required this.navigateToBag,
  });

  @override
  State<PdpCarouselOtherLook> createState() => _PdpCarouselOtherLookState();
}

class _PdpCarouselOtherLookState extends State<PdpCarouselOtherLook>
    with TickerProviderStateMixin {
  final _pdpCubit = Modular.get<PdpCubit>();

  void _addToBagSizeSelection(
      {required Product product,
      OrderFormItem? selectedSize,
      required BuildContext analyticsContext}) {
    final metadata = AnalyticsMetadataProvider.of(analyticsContext);
    showAzzasBottomSheet(
      context: analyticsContext,
      builder: (BuildContext context) {
        return AnalyticsMetadataProvider(
          metadata: metadata,
          child: PdpAddToBagBottomSheet(
            item: product,
            controller: _pdpCubit,
            selectedSize: selectedSize,
            useProductImageUrl: true,
            useItem: true,
            navigateToBag: () {
              Navigator.of(context).pop();
              widget.navigateToBag();
            },
          ),
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnalyticsMetadataProvider(
      metadata: const {
        'item_list_name': 'complete o look',
        'item_list_position': 'carrossel de fotos',
      },
      child: LayoutBuilder(builder: (context, constraints) {
        final maxHeight = constraints.maxHeight;
        return Container(
          padding: EdgeInsets.only(
            top: maxHeight * 0.15,
            left: Tokens.spacing.spacingSmall,
          ),
          color: Tokens.colors.neutral.light,
          child: AzzasSpotProductCarousel(
            hasFavoriteButton: true,
            useProductImageUrl: true,
            imageWidth: 187,
            useImageWidth: true,
            spotHeight: SpotHeight.small,
            containerHeight: 404,
            backgroundColor: Tokens.colors.neutral.pure,
            textSecondary: 'Complete o look',
            bottomPaddingHeader: Tokens.spacing.spacingXLarge,
            textLeftSpacing: Tokens.spacing.spacingSmall,
            textTopSpacing: Tokens.spacing.spacingSmall,
            textSecondaryStyle: Tokens.typography.headings.small.smallRegular
                .copyWith(color: Tokens.colors.typography.light),
            onTapProduct: (Product product, int index) {
              Modular.to.pushNamed('/pdp', arguments: product);
            },
            onTapProductToBag: (Product product, int index) {
              _addToBagSizeSelection(
                  product: product, analyticsContext: context);
            },
            textProductToBag: 'Adicionar à sacola',
            search: Search(
              productIdsSeparateByComma: widget.productIdsSeparateByComma,
            ),
            screenClass: 'PDPCarousel',
            isPDPCarousel: false,
            showControllerCarrousel: false,
            paddingTextToBag: EdgeInsets.only(
              left: Tokens.spacing.spacingSmall,
              top: Tokens.spacing.spacingMedium,
            ),
            textToBagStyle: Tokens.typography.body.extraSmall.extraSmallRegular,
            addToBagIcon: Tokens.icons.navigation.right,
          ),
        );
      }),
    );
  }
}
