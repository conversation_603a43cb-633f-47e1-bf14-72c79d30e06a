import 'dart:async';

import 'package:azzas_analytics/events/category/favorites_page_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_measurement_table_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_sold_out_bottom_sheet.dart';
import 'package:flutter/cupertino.dart';

class PdpAddToBagBottomSheet extends StatefulWidget {
  final Product item;
  final OrderFormProduct? itemCard;
  final OrderFormItem? selectedSize;
  final bool purchaseNow;
  final PdpCubit controller;
  final void Function()? navigateToBag;
  final int? productIndex;
  final bool useItem;
  final bool useProductImageUrl;

  const PdpAddToBagBottomSheet({
    super.key,
    required this.item,
    this.itemCard,
    this.selectedSize,
    this.purchaseNow = false,
    required this.controller,
    required this.navigateToBag,
    this.productIndex,
    this.useItem = false,
    this.useProductImageUrl = false,
  });

  @override
  State<PdpAddToBagBottomSheet> createState() => _PdpAddToBagBottomSheetState();
}

class _PdpAddToBagBottomSheetState extends State<PdpAddToBagBottomSheet>
    with TickerProviderStateMixin {
  late OrderFormItem activeItem;
  final orderForm = Modular.get<OrderFormCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  late Map<String, Object?> _analytics;
  bool _alreadyAdded = false;

  @override
  void initState() {
    super.initState();
    setActiveItem();
  }

  @override
  void dispose() {
    super.dispose();
    widget.controller.updatePDPBagState(success: false);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _analytics = AnalyticsMetadataProvider.of(context);

    if (!_alreadyAdded && widget.selectedSize?.isAvailable == true) {
      _alreadyAdded = true;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        addItemToBag(activeItem, true);
      });
    }
  }

  @override
  void didUpdateWidget(covariant PdpAddToBagBottomSheet oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedSize?.itemId != oldWidget.selectedSize?.itemId) {
      setState(() {
        setActiveItem();
      });
    }
  }

  void setActiveItem() {
    if (widget.selectedSize != null && widget.selectedSize!.isAvailable) {
      activeItem = widget.selectedSize!;
      //addItemToBag(activeItem, true);
    } else if (widget.selectedSize != null) {
      activeItem = widget.selectedSize!;
    } else if (widget.item.isAvailability) {
      activeItem = widget.item.items!.firstWhere((item) => item.isAvailable);
    } else {
      activeItem = widget.item.items![0];
    }
  }

  Future<void> addItemToBag(OrderFormItem item, bool purchaseNow) async {
    try {
      await widget.controller.addItem(orderAddItem: [
        OrderAddItem(
            id: item.itemId!,
            quantity: 1,
            seller: item.firstAvailableSeller?.sellerId ?? '')
      ], purchaseNow: purchaseNow);

      _eventDispatcher.logAddToCart(
        product: widget.item,
        index: widget.productIndex ?? 0,
        itemId: item.itemId!,
        itemListName: _analytics['item_list_name'] as String?,
        itemListPosition: _analytics['item_list_position'] as String?,
      );
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  void onTapTableSizes() {
    showAzzasBottomSheet(
        context: context,
        vsync: this,
        builder: (BuildContext context) {
          return PdpMeasurementTableBottomSheet(
            product: widget.item,
          );
        });
  }

  Future<void> onTapAction() async {
    if (!activeItem.isAvailable) {
      Navigator.of(context).pop();
      widget.controller.setProductSize(activeItem);
      warneMe(activeItem.itemId ?? '');
    } else {
      addItemToBag(activeItem, false);
    }
  }

  void warneMe(String skuId) {
    showAzzasBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return PdpSoldOutBottomSheet(
            controller: widget.controller, skuId: skuId);
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PdpCubit, PdpState>(
      bloc: widget.controller,
      listener: (context, state) {
        if (state.pdpBagState.error) {
          AzzasSnackBar.show(
            context: context,
            message: 'ocorreu um erro inesperado tente novamente mais tarde!',
            status: SnackBarStatus.danger,
          );
        }
      },
      builder: (context, state) {
        return state.pdpBagState.success
            ? AzzasSkuSelectionBottomSheet(
                onCloseTap: () => Navigator.pop(context),
                title: state.pdpBagState.purchaseNow
                    ? 'Adicionando produto a sacola'
                    : 'Produto adicionado na sacola',
                titleStyle: Tokens.typography.headings.small.smallRegular,
                action: AzzasPrimaryButton(
                  isLoading: state.pdpBagState.purchaseNow,
                  onPressed: widget.navigateToBag,
                  expanded: true,
                  child: const Text(
                    'Ver sacola',
                  ),
                ),
                content: state.pdpBagState.purchaseNow
                    ? const SizedBox.shrink()
                    : Column(
                        children: [
                          SizedBox(
                            height: 250.0,
                            child: AzzasCheckoutProductCard(
                              imageHeight: 197.0,
                              imageWidth: 130.0,
                              product: AzzasProductCardProductParams(
                                availability: widget.item.availability,
                                productId: widget.useItem
                                    ? (widget.item.productId ?? '')
                                    : state.productSelected.productId ?? '',
                                name: widget.useItem
                                    ? widget.item.productName ?? ''
                                    : state.productSelected.productName ?? '',
                                currentPrice: widget.useItem
                                    ? widget.item.productFormattedListPrice
                                    : state
                                        .productSelected.productFormattedPrice,
                                fullPrice: widget.useItem
                                    ? widget.item.productFormattedPrice !=
                                            widget
                                                .item.productFormattedListPrice
                                        ? widget.item.productFormattedListPrice
                                        : null
                                    : state.productSelected
                                                .productFormattedPrice !=
                                            state.productSelected
                                                .productFormattedListPrice
                                        ? state.productSelected
                                            .productFormattedListPrice
                                        : null,
                                size: activeItem.itemSize,
                                quantity: 1,
                                imageUrl: widget.useProductImageUrl
                                    ? widget.item.addToBagProductImage
                                    : widget.item.coverImage,
                              ),
                            ),
                          )
                        ],
                      ),
              )
            : AzzasSkuSelectionBottomSheet(
                showDragBar: true,
                onCloseTap: () => Navigator.of(context).pop(),
                title: 'Selecione o seu tamanho',
                titleStyle: Tokens.typography.headings.small.smallRegular,
                action: AzzasPrimaryButton(
                  isLoading: state.pdpBagState.loading,
                  onPressed: () => onTapAction(),
                  expanded: true,
                  child: Text(
                    activeItem.isAvailable ? 'Adicionar à sacola' : 'Avise-me',
                  ),
                ),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      children: [
                        ProductBoxButton(
                          buttonText: 'Tabela de medidas',
                          textStyle: Tokens
                              .typography.body.extraSmall.extraSmallRegular,
                          icon: Icon(
                            Tokens.icons.navigation.right,
                            size: 10,
                            color: Tokens.colors.typography.light,
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            onTapTableSizes();
                          },
                        ),
                        SizedBox(
                          width: Tokens.spacing.spacingSmall,
                        ),
                        if (widget.item.hasSizeUnavailable)
                          ProductBoxButton(
                            buttonText: 'Avise-me',
                            textStyle: Tokens
                                .typography.body.extraSmall.extraSmallRegular,
                            icon: Icon(
                              Tokens.icons.navigation.right,
                              size: 10,
                              color: Tokens.colors.typography.light,
                            ),
                            onTap: () async {
                              await FavoritesPageEvents.logNotifyMeOption(
                                local: 'pdc',
                              );
                              Navigator.of(context).pop();
                              warneMe('');
                            },
                          ),
                      ],
                    ),
                    SizedBox(
                      height: 250.0,
                      child: AzzasPicker(
                        initialItem:
                            widget.item.items?.indexOf(activeItem) ?? 0,
                        options: [
                          ...widget.item.items!.asMap().entries.map((entry) {
                            int idx = entry.key;
                            OrderFormItem val = entry.value;

                            String? textSuport;
                            if (val.hasOne) {
                              textSuport = 'Resta 1';
                            }
                            if (val.hasTwo) {
                              textSuport = 'Resta 2!';
                            }
                            if (!val.isAvailable) {
                              textSuport = 'Avise-me';
                            }

                            final size = val.tamanho?.elementAtOrNull(idx) ??
                                val.tamanho?.firstOrNull ??
                                'indefinido';

                            return AzzasPickerData(
                                title: size, description: textSuport ?? ' ');
                          })
                        ],
                        onSelectedItemChanged: (index) {
                          setState(() {
                            activeItem = widget.item.items![index];
                          });
                        },
                      ),
                    )
                  ],
                ),
              );
      },
    );
  }
}

extension on Product {
  String get addToBagProductImage {
    final image = items?.first.images
        ?.firstWhereOrNull((image) => image.imageLabel == "11");

    return image?.imageUrl ?? items?.first.images?.first.imageUrl ?? '';
  }
}
