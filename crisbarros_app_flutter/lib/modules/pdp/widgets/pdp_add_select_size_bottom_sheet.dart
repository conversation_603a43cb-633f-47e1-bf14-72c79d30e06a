import 'dart:async';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/cupertino.dart';

class PdpAddSelectSizeBottomSheet extends StatefulWidget {
  final Product item;
  final PdpCubit controller;

  const PdpAddSelectSizeBottomSheet({
    super.key,
    required this.item,
    required this.controller,
  });

  @override
  State<PdpAddSelectSizeBottomSheet> createState() =>
      _PdpAddSelectSizeBottomSheetState();
}

class _PdpAddSelectSizeBottomSheetState
    extends State<PdpAddSelectSizeBottomSheet> with TickerProviderStateMixin {
  late OrderFormItem activeItem;

  @override
  void initState() {
    super.initState();
    _onInit();
  }

  @override
  Widget build(BuildContext context) {
    return AzzasSkuSelectionBottomSheet(
      showDragBar: true,
      onCloseTap: () => Navigator.pop(context),
      title: 'Selecione o seu tamanho',
      titleStyle: Tokens.typography.headings.small.smallRegular,
      action: AzzasPrimaryButton(
        isDisabled: !activeItem.isAvailable,
        onPressed: _onTapAction,
        expanded: true,
        child: const Text('Selecionar tamanho'),
      ),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Para calcular o frete é necessário selecionar primeiramente um tamanho',
            style: Tokens.typography.body.medium.mediumRegular,
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250.0,
            child: AzzasPicker(
              initialItem: widget.item.items?.indexOf(activeItem) ?? 0,
              options: [
                ...widget.item.items!.asMap().entries.map((entry) {
                  int idx = entry.key;
                  OrderFormItem val = entry.value;

                  String? textSuport;
                  if (val.hasOne) {
                    textSuport = 'Resta 1';
                  }
                  if (val.hasTwo) {
                    textSuport = 'Resta 2!';
                  }
                  if (!val.isAvailable) {
                    textSuport = 'Sem estoque';
                  }

                  final size = val.tamanho?.elementAtOrNull(idx) ??
                      val.tamanho?.firstOrNull ??
                      'indefinido';

                  return AzzasPickerData(
                      title: size, description: textSuport ?? ' ');
                })
              ],
              onSelectedItemChanged: (index) {
                setState(() {
                  activeItem = widget.item.items![index];
                });
              },
            ),
          )
        ],
      ),
    );
  }

  void _onInit() {
    final items = widget.item.items ?? [];
    if (widget.item.isAvailability) {
      activeItem = items.firstWhere((item) => item.isAvailable);
    } else {
      activeItem = items.first;
    }
  }

  Future<void> _onTapAction() async {
    Navigator.of(context).pop();
    widget.controller.setProductSize(activeItem);
  }
}
