import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class PdpSoldOutBottomSheet extends StatefulWidget {
  const PdpSoldOutBottomSheet({
    required this.controller,
    required this.skuId,
    Key? key,
  }) : super(key: key);

  final PdpCubit controller;
  final String skuId;

  @override
  State<PdpSoldOutBottomSheet> createState() => _PdpSoldOutBottomSheetState();
}

class _PdpSoldOutBottomSheetState extends State<PdpSoldOutBottomSheet> {
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();
  bool isDisabled = true;
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    _emailController.text = authCubit.state.localUserInfo?.personEmail ?? '';
    _nameController.text = authCubit.state.localUserInfo?.personName ?? '';
    _validateForm();
    _emailController.addListener(() {
      _validateForm();
    });
    _nameController.addListener(() {
      _validateForm();
    });
    super.initState();
  }

  @override
  void dispose() {
    widget.controller.resertWarnMeStatus();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: BlocConsumer<PdpCubit, PdpState>(
        bloc: widget.controller,
        listener: (context, state) {
          if (state.warnMeState.error) {
            Modular.to.pop(context);
            _showErrorMessage();
            return;
          }
        },
        builder: (context, state) {
          return state.warnMeState.success
              ? const _FeedbackSucess()
              : AzzasSouldOutBottomSheet(
                  onCloseTap: () => Modular.to.pop(context),
                  title: 'Avise-me quando chegar',
                  description:
                      'Selecione o tamanho desejado e, assim  que estiver disponível novamente, enviaremos  um e-mail para informá-la.',
                  descriptionStyle: Tokens.typography.body.small.smallRegular,
                  titleStyle: Tokens.typography.headings.small.smallRegular,
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tamanhos',
                        style: Tokens.typography.body.medium.mediumRegular,
                      ),
                      SizedBox(height: Tokens.spacing.spacingSmall),
                      _SizeSelector(
                        onWarnMe: (_) {},
                        product: state.productSelected,
                        onSelectSize: widget.controller.setProductSize,
                        selectedSize: state.selectedSize,
                      ),
                      SizedBox(height: Tokens.spacing.spacingSmall),
                      Text(
                        'Digite seu nome e e-mail',
                        style: Tokens.typography.body.small.smallRegular,
                      ),
                      SizedBox(height: Tokens.spacing.spacingSmall),
                      AzzasInput(
                        textEditingController: _nameController,
                        hintText: 'Nome',
                        placeholder: 'Nome',
                      ),
                      SizedBox(height: Tokens.spacing.spacingSmall),
                      AzzasInput(
                        textEditingController: _emailController,
                        hintText: 'Email',
                        placeholder: 'Email',
                      ),
                      SizedBox(height: Tokens.spacing.spacingXLarge),
                      AzzasPrimaryButton(
                        expanded: true,
                        isLoading: state.warnMeState.loading,
                        isDisabled: isDisabled || state.selectedSize == null,
                        onPressed: _onWarnMe,
                        child: const Text('Avise-me quando chegar'),
                      ),
                    ],
                  ),
                );
        },
      ),
    );
  }

  void _onWarnMe() {
    final skuId = widget.skuId.isEmpty
        ? widget.controller.state.selectedSize?.itemId ?? ''
        : widget.skuId;
    widget.controller.warnMe(
      skuId: skuId,
      name: _nameController.text,
      email: _emailController.text,
    );
  }

  void _validateForm() {
    final isNameValid = _nameController.text.isNotEmpty;
    final isEmailValid = TextHelper.isEmail(_emailController.text);

    if (isNameValid && isEmailValid) {
      setState(() {
        isDisabled = false;
      });
    } else {
      setState(() {
        isDisabled = true;
      });
    }
  }

  void _showErrorMessage() {
    AzzasSnackBar.show(
      context: context,
      message: 'Ops, um erro inesperado!',
      status: SnackBarStatus.danger,
    );
  }
}

class _FeedbackSucess extends StatelessWidget {
  const _FeedbackSucess();

  @override
  Widget build(BuildContext context) {
    return AzzasSouldOutBottomSheet(
      onCloseTap: () => Modular.to.pop(context),
      title: 'Obrigada!',
      description:
          'Assim que estiver disponível novamente, enviaremos um e-mail para notificá-la imediatamente',
      descriptionStyle: Tokens.typography.body.small.smallRegular,
      titleStyle: Tokens.typography.headings.small.smallRegular,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AzzasPrimaryButton(
            expanded: true,
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }
}

class _SizeSelector extends StatelessWidget {
  const _SizeSelector({
    required this.product,
    required this.onSelectSize,
    required this.onWarnMe,
    this.selectedSize,
  });

  final Product product;
  final OrderFormItem? selectedSize;
  final ValueChanged<OrderFormItem> onSelectSize;
  final ValueChanged<String> onWarnMe;

  @override
  Widget build(BuildContext context) {
    final seenTamanhos = <String>{};

    final items = product.items?.where((size) {
          final tamanhoKey = size.tamanho?.join(',') ?? '';
          final isDuplicate = seenTamanhos.contains(tamanhoKey);
          final isUnavailable = size.firstAvailableSeller?.commertialOffer
                  ?.isAvailableAndWithoutError ==
              false;

          if (!isDuplicate && isUnavailable) {
            seenTamanhos.add(tamanhoKey);
            return true;
          }
          return false;
        }).toList() ??
        [];

    return Wrap(
      children: items.map((size) {
        final isSizeAvailable = size.firstAvailableSeller?.commertialOffer
                ?.isAvailableAndWithoutError ==
            false;

        return Padding(
          padding: EdgeInsets.only(right: Tokens.spacing.spacingXSmall),
          child: AzzasSkuSelector(
            text: size.tamanho?.firstOrNull?.toString() ?? '',
            isActive: selectedSize == size && isSizeAvailable,
            isCrossed: !isSizeAvailable,
            isDisabled: false,
            onTap: () {
              onSelectSize(size);
            },
            descriptionTextStyle:
                Tokens.typography.body.extraSmall.extraSmallCaption.copyWith(
              color: Tokens.colors.error.medium,
            ),
          ),
        );
      }).toList(),
    );
  }
}
