import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class LoginEmailPage extends StatefulWidget {
  final String? email;
  final bool? createPassword;
  final bool? isRegister;
  final Function()? overrideFinishFlow;

  const LoginEmailPage({
    super.key,
    this.email,
    this.createPassword = false,
    this.isRegister = false,
    this.overrideFinishFlow,
  });

  @override
  State<LoginEmailPage> createState() => _LoginEmailPageState();
}

class _LoginEmailPageState extends State<LoginEmailPage> {
  late TextEditingController emailController;
  final loginEmailCubit = Modular.get<LoginEmailCubit>();
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'login_email');
    emailController = TextEditingController(text: widget.email);
    if (emailController.text.isNotEmpty) {
      loginEmailCubit.validateEmail(emailController.text);
    }
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    emailController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: SizedBox(
                height: 56,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding:
                          EdgeInsets.only(left: Tokens.spacing.spacingSmall),
                      child: IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: Icon(Tokens.icons.action.close),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const AzzasLargeAppBar(
              title: 'Primeiro, digite seu email',
              pinned: false,
            ),
            SliverFillRemaining(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Tokens.spacing.spacingMedium,
                ),
                child: Column(
                  children: [
                    const AzzasSubtitleSection(
                      text:
                          "Vamos verificar se você já comprou na Cris Barros alguma vez",
                    ),
                    SizedBox(height: Tokens.spacing.spacingXLarge),
                    AzzasInput(
                        enabled: true,
                        keyboardType: TextInputType.emailAddress,
                        textEditingController: emailController,
                        hintText: '<EMAIL>',
                        placeholder: 'Email',
                        cursorHeight: 24,
                        validator: (_) {
                          return loginEmailCubit
                              .validateEmail(emailController.text);
                        }),
                    SizedBox(height: Tokens.spacing.spacingXLarge),
                    BlocBuilder<LoginEmailCubit, LoginEmailState>(
                      bloc: loginEmailCubit,
                      builder: (context, state) {
                        return AzzasPrimaryButton(
                          expanded: true,
                          isDisabled: !state.isEmailValid,
                          onPressed: () async {
                            await AccountEvents.logEmailVerification(
                                local: 'login_email', action: "continuar");
                            if (widget.createPassword == true) {
                              AccountEvents.logSignUpStep(
                                  local: 'login_email', step: "e-mail");
                              Modular.to.pushNamed(
                                '/account/create_password',
                                arguments: {
                                  'email': emailController.text,
                                },
                              );
                              return;
                            }

                            return await onTapContinue(
                              loginEmailCubit: loginEmailCubit,
                              authCubit: authCubit,
                            );
                          },
                          isLoading: state.isLoading,
                          child: const Text("Continuar"),
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> onTapContinue({
    required LoginEmailCubit loginEmailCubit,
    required AuthCubit authCubit,
  }) async {
    if (loginEmailCubit.state.isLoading) return;

    try {
      final result = await loginEmailCubit.sendLogin(emailController.text);
      if (result == null) return;

      goToLoginCode(token: result, isRegister: widget.isRegister ?? false);
    } catch (e) {
      if (!mounted) {
        return;
      }

      AzzasSnackBar.show(
        context: context,
        message: "Erro ao enviar email",
        backgroundColor: Tokens.colors.error.pure,
      );
    }
  }

  void goToLoginCode({
    required String token,
    required bool isRegister,
  }) {
    Modular.to.pushNamed(
      '/account/login_code',
      arguments: {
        'email': emailController.text,
        'token': token,
        'isRegister': isRegister,
        'overrideFinishFlow': widget.overrideFinishFlow,
      },
    );
  }
}
