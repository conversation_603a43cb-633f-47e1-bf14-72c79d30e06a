import 'dart:async';
import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/services/user_property_service.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/account/widgets/bottom_sheets/bottom_sheet_biometrics.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

class LoginCodePage extends StatefulWidget {
  const LoginCodePage({
    super.key,
    required this.email,
    required this.token,
    required this.newPassword,
    this.isRegister = false,
    this.overrideFinishFlow,
  });

  final String email;
  final String token;
  final String? newPassword;
  final bool? isRegister;
  final Function()? overrideFinishFlow;

  @override
  State<LoginCodePage> createState() => _LoginCodePageState();
}

class _LoginCodePageState extends State<LoginCodePage>
    with TickerProviderStateMixin {
  final _pinPutController = TextEditingController();
  final _pinPutFocusNode = FocusNode();
  final loginCodeCubit = Modular.get<LoginCodeCubit>();
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'login_code');
    _pinPutFocusNode.requestFocus();

    loginCodeCubit.startTimer();

    _pinPutController.addListener(() {
      _validatePinForm();
    });

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _pinPutController.dispose();
    _pinPutFocusNode.dispose();

    if (loginCodeCubit.state.timer != null) {
      loginCodeCubit.cancel();
    }
  }

  void showSnackBar({required BuildContext context, required String message}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor: Tokens.colors.success.pure,
    );
  }

  Future<void> resetPassword(String code) async {
    final email = widget.email;
    final password = widget.newPassword;

    try {
      loginCodeCubit.setIsLoading(true);
      final response = await loginCodeCubit.setPassword(
        code: code,
        email: email,
        password: password!,
        token: widget.token,
      );
      if (response != null) {
        await authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: email,
          loginType: LoginType.email,
        );
      }
      if (authCubit.state.isLoggedIn && mounted) {
        showSnackBar(context: context, message: 'Senha alterada com sucesso');
        if (authCubit.state.hasBiometrics) {
          Modular.to.navigate('/');
          return;
        }

        _showBottomSheet('/');
      }
    } catch (e) {
      loginCodeCubit.setError(true);
      debugPrint("Error in resetPassword: $e");
    } finally {
      loginCodeCubit.setIsLoading(false);
    }
  }

  Future<void> validateCode(String code) async {
    try {
      loginCodeCubit.setIsLoading(true);
      final response = await loginCodeCubit.validateCode(
        email: widget.email,
        code: code,
        token: widget.token,
      );
      if (response != null) {
        await authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: widget.email,
          loginType: LoginType.email,
        );
      }
      if (authCubit.state.isLoggedIn && mounted) {
        if (widget.overrideFinishFlow != null) {
          widget.overrideFinishFlow!();
          return;
        }

        showSnackBar(context: context, message: 'Login efetuado com sucesso');
        if (authCubit.state.localUserInfo?.personEmail.isNotNullOrEmpty ??
            false) {
          await UserPropertyService.setUserEmailProperty(
              authCubit.state.localUserInfo?.personEmail ?? '');
        }

        String route =
            widget.isRegister == true ? '/account/complete_user_data' : '/';

        if (widget.isRegister != true) {
          Modular.get<EventDispatcher>().logLogin();
        } else {
          AccountEvents.logSignUpMethod(
              local: 'login_code', method: 'cadastro_validate_code');
        }

        if (authCubit.state.hasBiometrics) {
          Modular.to.pushNamed(route, arguments: {
            'isRegister': widget.isRegister ?? false,
          });
          return;
        }

        _showBottomSheet(route);

        return;
      }
      return loginCodeCubit.setError(true);
    } catch (e) {
      loginCodeCubit.setError(true);
      debugPrint("Error in validateCode: $e");
    } finally {
      loginCodeCubit.setIsLoading(false);
    }
  }

  void _showBottomSheet(String? route) {
    showAzzasBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (BuildContext context) {
        return BottomSheetBiometrics(
          route: route,
          cubit: authCubit,
        );
      },
      vsync: this,
    );
  }

  void _validatePinForm() {
    final isPin = _pinPutController.text.isEmpty;

    if (isPin) {
      loginCodeCubit.setError(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LoginCodeCubit, LoginCodeState>(
      bloc: loginCodeCubit,
      builder: (context, state) {
        final pinTextStyle = state.hasError
            ? Tokens.typography.headings.medium.mediumRegular.copyWith(
                color: Tokens.colors.error.pure,
              )
            : Tokens.typography.headings.medium.mediumRegular;

        return Scaffold(
          body: SafeArea(
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 56,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: IconButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            icon: Icon(Tokens.icons.navigation.left),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const AzzasLargeAppBar(
                  title: 'Digite o código enviado por email',
                  pinned: false,
                  toolbarHeight: 110,
                ),
                SliverFillRemaining(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Tokens.spacing.spacingMedium,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AzzasSubtitleSection(
                          text:
                              "Para entrar na sua conta, digite o código enviado para ${widget.email}",
                        ),
                        SizedBox(height: Tokens.spacing.spacingXUltraLarge),
                        Pinput(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          separatorBuilder: (index) => SizedBox(
                            width: Tokens.spacing.spacingMedium,
                          ),
                          length: 6,
                          onCompleted: (code) async {
                            if (widget.newPassword != null &&
                                widget.newPassword!.isNotEmpty) {
                              await resetPassword(code);
                              return;
                            }

                            await validateCode(code);
                          },
                          focusNode: _pinPutFocusNode,
                          controller: _pinPutController,
                          keyboardType: TextInputType.number,
                          defaultPinTheme: PinTheme(
                            textStyle: pinTextStyle,
                            width: double.infinity,
                            height: 50.0,
                            decoration: const BoxDecoration(
                              color: Colors.transparent,
                            ),
                          ),
                          followingPinTheme: PinTheme(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  Tokens.colors.neutral.dark4,
                                  Tokens.colors.neutral.dark4,
                                  Colors.transparent,
                                  Colors.transparent,
                                ],
                                stops: const [0, 0.3, 0.3, 1],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: Tokens.spacing.spacingXLarge),
                        if (state.hasError) ...[
                          Text(
                            "Código inválido ou expirado",
                            style: Tokens
                                .typography.body.extraSmall.extraSmallRegular
                                .copyWith(
                              color: Tokens.colors.error.pure,
                            ),
                          ),
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                        ],
                        if (state.secondsRemaining > 0) ...[
                          Text(
                            "Precisa de outro código?",
                            style: Tokens
                                .typography.body.extraSmall.extraSmallRegular,
                          ),
                          Text(
                            "Você pode solicitar outro em ${state.secondsRemaining} segundos",
                            style: Tokens
                                .typography.body.extraSmall.extraSmallRegular,
                          ),
                          SizedBox(height: Tokens.spacing.spacingXSmall),
                        ],
                        if (state.secondsRemaining == 0)
                          InkWell(
                            onTap: () async {
                              await loginCodeCubit.resendCode(widget.email);
                              await AccountEvents.logEmailVerification(
                                  local: 'login_code',
                                  action: "codigo-reenviar");
                              loginCodeCubit.setError(false);
                              _pinPutController.text = "";
                            },
                            child: SizedBox(
                              height: 40,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Solicitar novo código",
                                    style: Tokens.typography.body.extraSmall
                                        .extraSmallRegular,
                                  ),
                                  SizedBox(
                                      height: Tokens.spacing.spacingXXSmall),
                                  Icon(
                                    Tokens.icons.navigation.right,
                                    size: 15,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        if (state.isLoading) ...[
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                          const Center(
                            child: AzzasSpinner(),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
