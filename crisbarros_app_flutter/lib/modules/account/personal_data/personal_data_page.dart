import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:flutter/material.dart';

class PersonalDataPage extends StatefulWidget {
  const PersonalDataPage({super.key});

  @override
  State<PersonalDataPage> createState() => _PersonalDataPageState();
}

class _PersonalDataPageState extends State<PersonalDataPage> {
  final personalDataModel = PersonalDataModel();
  final formKey = GlobalKey<FormState>();
  final personalDataCubit = Modular.get<PersonalDataCubit>();
  final authCubit = Modular.get<AuthCubit>();

  bool receiveSMS = false;
  bool receiveEmail = false;

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'personal_data');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onInit();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              SizedBox(
                height: 56,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 4.0, left: 8.0),
                      child: IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: Icon(
                          Tokens.icons.navigation.left,
                          size: 32.0,
                        ),
                      ),
                    ),
                    Text(
                      'Dados pessoais',
                      style: Tokens.typography.body.medium.mediumRegular,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Tokens.spacing.spacingMedium,
                ),
                child: BlocBuilder<PersonalDataCubit, PersonalDataState>(
                  bloc: personalDataCubit,
                  builder: (context, state) {
                    return Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 24.0,
                          ),
                          AzzasInput(
                            enabled: !state.isLoading,
                            keyboardType: TextInputType.emailAddress,
                            textEditingController:
                                personalDataModel.emailController,
                            hintText: 'Email',
                            placeholder: 'Email',
                            cursorHeight: 24.0,
                            validator: (value) => StringHelper.isEmail(value!)
                                ? null
                                : "Email inválido",
                            size: AzzasInputSize.large,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            onChanged: (value) {
                              _validateFields();
                            },
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasInput(
                            enabled: !state.isLoading,
                            textEditingController:
                                personalDataModel.nameController,
                            hintText: "Nome",
                            placeholder: "Nome",
                            cursorHeight: 24.0,
                            validator: (value) =>
                                value!.isEmpty ? "Nome inválido" : null,
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            size: AzzasInputSize.large,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            onChanged: (value) {
                              _validateFields();
                            },
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasInput(
                            enabled: !state.isLoading,
                            textEditingController:
                                personalDataModel.lastNameController,
                            hintText: "Sobrenome",
                            cursorHeight: 24.0,
                            placeholder: "Sobrenome",
                            validator: (value) =>
                                value!.isEmpty ? "Sobrenome inválido" : null,
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            size: AzzasInputSize.large,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            onChanged: (value) {
                              _validateFields();
                            },
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasInput(
                            enabled: !state.isLoading,
                            textEditingController:
                                personalDataModel.cpfController,
                            hintText: "CPF",
                            placeholder: "CPF",
                            cursorHeight: 24.0,
                            inputFormatter: AzzasInputMask.cpf.formatter,
                            onChanged: (value) {
                              _validateFields();
                            },
                            validator: (value) => StringHelper.isCpf(value!)
                                ? null
                                : "CPF inválido",
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            size: AzzasInputSize.large,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            supportedText: "Apenas números",
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasInput(
                            enabled: !state.isLoading,
                            textEditingController:
                                personalDataModel.birthDateController,
                            hintText: "Data de Nascimento",
                            placeholder: "Data de Nascimento",
                            validator: (value) => value!.isEmpty ||
                                    (value.length > 9
                                        ? !StringHelper.isValidBirthDate(value)
                                        : false)
                                ? "Data inválida"
                                : null,
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            size: AzzasInputSize.large,
                            cursorHeight: 24.0,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            supportedText: "Apenas números",
                            keyboardType:
                                const TextInputType.numberWithOptions(),
                            inputFormatter: AzzasInputMask.dateDDMMYY.formatter,
                            onChanged: (value) {
                              _validateFields();
                            },
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasInput(
                            enabled: !state.isLoading,
                            textEditingController:
                                personalDataModel.phoneController,
                            inputFormatter: AzzasInputMask.phone.formatter,
                            hintText: "Telefone com DDD",
                            placeholder: "Telefone com DDD",
                            cursorHeight: 24.0,
                            onChanged: (value) {
                              _validateFields();
                            },
                            validator: (value) => StringHelper.isPhoneNumber(
                                    value!
                                        .replaceAll(' ', '')
                                        .replaceAll('-', ''))
                                ? null
                                : "Telefone inválido",
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            size: AzzasInputSize.large,
                            contentPadding: EdgeInsets.all(
                              Tokens.spacing.spacingSmall,
                            ),
                            supportedText: "Apenas números",
                          ),
                          SizedBox(height: Tokens.spacing.spacingXXUltraLarge),
                          AzzasCheckbox(
                            isDisabled: state.isLoading,
                            labelText: "Aceito receber sms promocionais",
                            onTap: (value) {
                              receiveSMS = value;
                              _validateFields();
                            },
                            isChecked: state.receiveSMS ?? receiveSMS,
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          AzzasCheckbox(
                            isDisabled: state.isLoading,
                            isChecked: state.receiveEmail ?? receiveEmail,
                            labelText: "Aceito receber emails promocionais",
                            onTap: (value) {
                              receiveEmail = value;
                              _validateFields();
                            },
                          ),
                          SizedBox(height: Tokens.spacing.spacingXXLarge),
                          Padding(
                            padding: EdgeInsets.only(
                              bottom: Tokens.spacing.spacingMedium,
                            ),
                            child: AzzasButton.primary(
                              isLoading: state.isLoading,
                              expanded: true,
                              onPressed: _savePersonalData,
                              isDisabled: state.isSaveButtonDisabled,
                              child: const Text('Salvar alterações'),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _onInit() async {
    await Future.wait([
      personalDataCubit.getPersonalData(personalDataModel),
      personalDataCubit.loadValues(),
    ]);
  }

  void _validateFields() {
    if (personalDataModel.validate()) {
      personalDataCubit.enableSaveButton();
    } else {
      personalDataCubit.disableSaveButton();
    }

    personalDataCubit.saveValues(
        receiveEmail: receiveEmail, receiveSMS: receiveSMS);
  }

  Future<void> _savePersonalData() async {
    try {
      if (formKey.currentState!.validate()) {
        final newUserInfo = personalDataModel.getUserInfo();

        await personalDataCubit.updateUserInfo(
          newUserInfo,
          receiveEmail: receiveEmail,
          receiveSMS: receiveSMS,
        );

        await authCubit.getUserInfo();
        AzzasSnackBar.show(
          context: context,
          message: "Dados salvos com sucesso",
          status: SnackBarStatus.success,
          backgroundColor: Tokens.colors.success.pure,
        );
      }
    } catch (e) {
      if (mounted) {
        AzzasSnackBar.show(
          context: context,
          message:
              "Algo deu errado e infelizmente não conseguimos salvar seus dados",
          status: SnackBarStatus.danger,
          backgroundColor: Tokens.colors.error.pure,
        );
      }
    }
  }
}
