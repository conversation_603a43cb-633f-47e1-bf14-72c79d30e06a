import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:flutter/material.dart';

class OrdersListPage extends StatefulWidget {
  const OrdersListPage({super.key, required this.overridePop});

  final Function()? overridePop;

  @override
  State<OrdersListPage> createState() => _OrdersListPageState();
}

class _OrdersListPageState extends State<OrdersListPage> {
  final _ordersCubit = Modular.get<OrdersListCubit>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'order_list');
    getMyOrders();
    super.initState();
  }

  Future<void> getMyOrders() async {
    await _ordersCubit.getMyOrders();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        onBack();
      },
      child: Scaffold(
        body: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              SizedBox(
                height: 72,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 4.0, left: 8.0),
                      child: IconButton(
                        onPressed: onBack,
                        icon: Icon(
                          Tokens.icons.navigation.left,
                          size: 32.0,
                        ),
                      ),
                    ),
                    Text(
                      'Pedidos e devoluções',
                      style: Tokens.typography.body.medium.mediumRegular,
                    ),
                  ],
                ),
              ),
              BlocBuilder<OrdersListCubit, OrdersListState>(
                  bloc: _ordersCubit,
                  builder: (context, state) {
                    return Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(right: 22, left: 24),
                        child: SizedBox(
                          width: double.infinity,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Pedidos ativos',
                                style:
                                    Tokens.typography.body.medium.mediumRegular,
                              ),
                              SizedBox(height: Tokens.spacing.spacingSmall),
                              state.isLoading
                                  ? const Center(
                                      child: AzzasSpinner(),
                                    )
                                  : state.activeOrders.isEmpty
                                      ? Center(
                                          child: Text(
                                            'Sem pedidos ativos',
                                            style: Tokens.typography.body.small
                                                .smallRegular,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                              ...state.activeOrders
                                  .map((element) => Column(
                                        children: [
                                          AzzasOrderSummaryStatus(
                                            onTap: () => onTapOrder(element),
                                            title: element.orderId ?? '',
                                            orderDate:
                                                DateHelper.dayNumberMonthText(
                                                    element.creationDate ?? ''),
                                            amount: CurrencyHelper.format(
                                              amount: element.effectiveValue,
                                              dividedBy100: true,
                                            ),
                                            tagLabel:
                                                element.statusDescription ?? '',
                                            tagColor:
                                                Tokens.colors.brand.medium,
                                            productImages: element.items
                                                    ?.map((p) => p
                                                        .getImageProductOrdered)
                                                    .toList() ??
                                                [],
                                          ),
                                          SizedBox(
                                            height: Tokens.spacing.spacingLarge,
                                          ),
                                        ],
                                      ))
                                  .toList(),
                              SizedBox(
                                  height: Tokens.spacing.spacingUltraLarge),
                              Text(
                                'Pedidos concluídos',
                                style:
                                    Tokens.typography.body.medium.mediumRegular,
                              ),
                              SizedBox(height: Tokens.spacing.spacingSmall),
                              state.isLoading
                                  ? const Center(
                                      child: AzzasSpinner(),
                                    )
                                  : state.completedOrders.isEmpty
                                      ? Center(
                                          child: Text(
                                            'Sem pedidos concluídos',
                                            style: Tokens.typography.body.small
                                                .smallRegular,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                              ...state.completedOrders
                                  .map((element) => Column(children: [
                                        AzzasOrderSummaryStatus(
                                          onTap: () => onTapOrder(element),
                                          title: element.orderId ?? '',
                                          orderDate:
                                              DateHelper.dayNumberMonthText(
                                                  element.creationDate ?? ''),
                                          amount: CurrencyHelper.format(
                                            amount: element.effectiveValue,
                                            dividedBy100: true,
                                          ),
                                          tagLabel:
                                              element.statusDescription ?? '',
                                          tagColor: Tokens.colors.brand.medium,
                                          productImages: element.items
                                                  ?.map((p) =>
                                                      p.getImageProductOrdered)
                                                  .toList() ??
                                              [],
                                        ),
                                        SizedBox(
                                            height:
                                                Tokens.spacing.spacingLarge),
                                      ]))
                                  .toList(),
                              SizedBox(
                                  height: Tokens.spacing.spacingUltraLarge),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
            ],
          ),
        ),
      ),
    );
  }

  void onTapOrder(ListUserOrder order) {
    _ordersCubit.setOrderDetail(order);
    Modular.to.pushNamed(
      '/account/orders_detail',
      arguments: order.orderId,
    );

   // Mock to test the new order placed
   //  Modular.to.pushNamed(
   //    '/order_placed',
   //    arguments: order.orderId?.split('-').firstOrNull,
   //  );
  }

  void onBack() {
    if (widget.overridePop != null) {
      widget.overridePop!();
      return;
    }
    Navigator.pop(context);
  }
}
