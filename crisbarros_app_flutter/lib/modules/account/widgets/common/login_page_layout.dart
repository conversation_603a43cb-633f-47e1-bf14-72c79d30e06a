import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

/// Template das páginas que serão utilizadas no fluxo de login.
///
/// Este widget é responsável por renderizar a estrutura básica das páginas de login.
/// Talvez, futuramente seja recomendado movê-la para o pacote de azzas_ui.
class LoginPageLayout extends StatelessWidget {
  final String title;
  final Widget scrollableContent;

  const LoginPageLayout({
    Key? key,
    required this.title,
    required this.scrollableContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: <PERSON><PERSON><PERSON><PERSON>(
                      height: 56,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16),
                            child: IconButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              icon: Icon(Tokens.icons.navigation.left),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  AzzasLargeAppBar(
                    title: title,
                    pinned: false,
                  ),
                  SliverFillRemaining(
                    hasScrollBody: true,
                    child: scrollableContent,
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.all(Tokens.spacing.spacingMedium),
              child: Text(
                'Ao se cadastrar, você concorda com nossa política de privacidade',
                textAlign: TextAlign.center,
                style: Tokens.typography.body.small.smallRegular,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
