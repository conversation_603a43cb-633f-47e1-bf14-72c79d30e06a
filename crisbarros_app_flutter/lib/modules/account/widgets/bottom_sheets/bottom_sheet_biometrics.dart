import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/account/widgets/bottom_sheets/bottom_sheet_biometrics_registered.dart';
import 'package:flutter/material.dart';

class BottomSheetBiometrics extends StatefulWidget {
  final AuthCubit cubit;
  final String? route;
  const BottomSheetBiometrics({super.key, required this.cubit, this.route});

  @override
  State<BottomSheetBiometrics> createState() => _BottomSheetBiometricsState();
}

class _BottomSheetBiometricsState extends State<BottomSheetBiometrics>
    with TickerProviderStateMixin {
  void _showBottomSheet() {
    showAzzasBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (BuildContext context) {
        return BottomSheetBiometricsRegistered(
          cubit: widget.cubit,
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AzzasBottomSheet(
        showDragBar: false,
        onCloseTap: () => Modular.to.navigate(widget.route ?? '/'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Deseja facilitar seus próximos acessos?',
              style: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.typography.light),
            ),
            SizedBox(
              height: Tokens.spacing.spacingSmall,
            ),
            Text(
              'Habilite o acesso à biometria para acessar sua conta',
              style: Tokens.typography.body.medium.mediumRegular
                  .copyWith(color: Tokens.colors.typography.medium),
            ),
            SizedBox(
              height: Tokens.spacing.spacingXLarge,
            ),
            Center(
              child: SizedBox(
                child: AzzasImage(
                  imageHeight: 83,
                  imageWidth: 75,
                  image: const AssetImage("assets/images/fingerprint.png"),
                ),
              ),
            ),
            SizedBox(
              height: Tokens.spacing.spacingXLarge,
            ),
            AzzasPrimaryButton(
              expanded: true,
              child: Text('Habilitar biometria'),
              onPressed: () {
                Navigator.of(context).pop();
                _showBottomSheet();
              },
            ),
            SizedBox(
              height: Tokens.spacing.spacingMedium,
            ),
            AzzasSecondaryButton(
              expanded: true,
              child: Text('Agora não'),
              onPressed: () => Modular.to.navigate(widget.route ?? '/'),
            ),
            SizedBox(
              height: Tokens.spacing.spacingMedium,
            ),
          ],
        ));
  }
}
