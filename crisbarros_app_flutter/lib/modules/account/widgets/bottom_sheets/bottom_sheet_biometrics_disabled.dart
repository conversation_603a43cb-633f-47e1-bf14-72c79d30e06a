import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BottomSheetBiometricsDisabled extends StatefulWidget {
  final AuthCubit cubit;
  const BottomSheetBiometricsDisabled({super.key, required this.cubit});

  @override
  State<BottomSheetBiometricsDisabled> createState() =>
      _BottomSheetBiometricsDisabledState();
}

class _BottomSheetBiometricsDisabledState
    extends State<BottomSheetBiometricsDisabled> with TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return AzzasBottomSheet(
        showDragBar: false,
        onCloseTap: () => Navigator.of(context).pop(),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tem certeza de que deseja desativar o login por\nbiometria?',
              style: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.typography.light),
            ),
            SizedBox(
              height: Tokens.spacing.spacingSmall,
            ),
            Text(
              'Você pode habilitá-lo novamente na área de perfil quando desejar',
              style: Tokens.typography.body.medium.mediumRegular
                  .copyWith(color: Tokens.colors.typography.medium),
            ),
            SizedBox(
              height: Tokens.spacing.spacingXLarge,
            ),
            AzzasPrimaryButton(
              expanded: true,
              child: Text('Quero desabilitar'),
              onPressed: () async {
                await widget.cubit.deactivateBiometrics();
                AzzasSnackBar.show(
                  context: context,
                  message: 'Biometria desativada com sucesso',
                );
                Navigator.of(context).pop();
              },
            ),
            SizedBox(
              height: Tokens.spacing.spacingMedium,
            ),
            AzzasSecondaryButton(
              expanded: true,
              child: Text('Agora não'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            SizedBox(
              height: Tokens.spacing.spacingMedium,
            ),
          ],
        ));
  }
}
