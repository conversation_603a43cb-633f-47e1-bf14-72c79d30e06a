import 'dart:async';

import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/app_config.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';

import 'package:flutter/material.dart';

import '../widgets/bottom_sheets/bottom_sheets.dart';

class MyAccountPage extends StatefulWidget {
  const MyAccountPage({super.key});

  @override
  State<MyAccountPage> createState() => _MyAccountPageState();
}

class _MyAccountPageState extends State<MyAccountPage>
    with TickerProviderStateMixin {
  final _authCubit = Modular.get<AuthCubit>();
  final _loginSocialCubit = Modular.get<LoginSocialCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  bool loadingBiometric = false;

  void showSnackBar(
      {required BuildContext context,
      required String message,
      Color? backgroundColor,
      SnackBarStatus? status}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      status: status ?? SnackBarStatus.success,
      backgroundColor: backgroundColor ?? Tokens.colors.success.pure,
    );
  }

  Future<void> _handleSignInApple() async {
    try {
      Navigator.of(context).pop();
      final response = await _loginSocialCubit.handleSignInApple();

      if (response != null) {
        final appleUser = FirebaseAuth.instance.currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: appleUser?.email ?? '',
          loginType: LoginType.apple,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          showSnackBar(context: context, message: 'Login efetuado com sucesso');
          Modular.to.navigate('/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
      print(error);
    }
  }

  Future<void> _handleSignInGoogle() async {
    try {
      Navigator.of(context).pop();
      final response = await _loginSocialCubit.handleSignInGoogle();

      if (response != null) {
        final googleUser = GoogleSignIn().currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: googleUser?.email ?? '',
          loginType: LoginType.google,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          showSnackBar(context: context, message: 'Login efetuado com sucesso');
          Modular.to.navigate('/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
    }
  }

  void _onLogin({bool? enabledRegister}) {
    AccountEvents.logCreateAccount(
        local: 'my_account', opcaoClicada: "entrar-na-conta");

    if (CheckoutNavigationManager.isNewCheckout) {
      Modular.to.pushNamed('/checkout/login');
      return;
    }

    showAzzasBottomSheet(
      context: context,
      builder: (_) {
        return BlocBuilder<AuthCubit, AuthState>(
          bloc: _authCubit,
          builder: (context, state) {
            return AzzasSelectLoginMethodBottomSheet(
              onTapLoginWithEmail: () {
                AccountEvents.logLogin(method: "login_email");
                Modular.to.popAndPushNamed('/account/login_email');
              },
              onTapLoginWithPassword: () {
                AccountEvents.logLogin(method: "login_email_password");
                Modular.to.popAndPushNamed('/account/login_email_password');
              },
              titleStyle: Tokens.typography.headings.small.smallRegular,
              onTapLoginWithGoogle: _handleSignInGoogle,
              onTapLoginWithApple: _handleSignInApple,
              onTapRegister: _goToRegisterRoutes,
              enabledRegister: enabledRegister ?? false,
            );
          },
        );
      },
      vsync: this,
    );
  }

  Future<void> _biometricLogin() async {
    setState(() {
      loadingBiometric = true;
    });
    try {
      final emailFromBiometrics = _authCubit.state.biometricsTokenEmail;
      final biometricResponse = await _authCubit.loginWithBiometrics();

      if (biometricResponse != null) {
        await _authCubit.loginSuccess(
          tokenResponse: biometricResponse,
          userEmail: emailFromBiometrics,
          loginType: LoginType.biometrics,
        );

        if (_authCubit.state.isLoggedIn) {
          showSnackBar(context: context, message: 'Login efetuado com sucesso');
          return Modular.to.navigate('/');
        }
      }
    } catch (e) {
      _errorBiometric();
    } finally {
      if (mounted)
        setState(() {
          loadingBiometric = false;
        });
    }
  }

  void _errorBiometric() async {
    showSnackBar(
        context: context,
        message: 'Não foi possível fazer login com a biometria',
        status: SnackBarStatus.danger);
    await _authCubit.deactivateBiometrics();
  }

  void _goToRegisterRoutes() {
    CheckoutNavigationManager.goToLogin(legacyCheckoutArgs: {"register": true});
  }

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'my_account');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      bloc: _authCubit,
      builder: (context, state) {
        final isLoggedIn = state.isLoggedIn;
        final userInfo = state.localUserInfo;

        return Scaffold(
          body: SafeArea(
            child: CustomScrollView(
              scrollDirection: Axis.vertical,
              slivers: [
                if (!isLoggedIn) ...[
                  state.hasBiometrics
                      ? SliverToBoxAdapter(
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: Tokens.spacing.spacingXXLarge,
                                left: Tokens.spacing.spacingSmall,
                                right: Tokens.spacing.spacingSmall),
                            child: AzzasTitleSection(
                                text:
                                    "Olá${state.userName != null ? ', ${state.userName}' : ''}"),
                          ),
                        )
                      : AzzasDefaultAppBar(
                          title: 'Crie sua conta',
                          pinned: false,
                          padding: EdgeInsets.symmetric(
                            vertical: Tokens.spacing.spacingXSmall,
                            horizontal: Tokens.spacing.spacingSmall,
                          ),
                          toolbarHeight: 78.0,
                        ),
                ],
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isLoggedIn
                          ? Tokens.spacing.spacingMedium
                          : Tokens.spacing.spacingSmall,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (isLoggedIn) ...[
                          Padding(
                            padding: EdgeInsets.only(
                                top: Tokens.spacing.spacingXXLarge),
                            child: AzzasTitleSection(
                                text:
                                    "Olá${userInfo?.personName != null ? ', ${userInfo?.personName}' : ''}!"),
                          ),
                          AzzasSubtitleSection(
                            text: userInfo?.personEmail ?? '',
                          ),
                          SizedBox(height: Tokens.spacing.spacingMedium),
                        ] else ...[
                          const AzzasSubtitleSection(
                            text:
                                "Acompanhamento de pedidos, wishlist, formas de pagamento, entre outras funções para sua comodidade.",
                          ),
                          SizedBox(height: Tokens.spacing.spacingSmall),
                          BlocBuilder<LoginSocialCubit, LoginSocialState>(
                            bloc: _loginSocialCubit,
                            builder: (context, state) {
                              return _authCubit.state.hasBiometrics
                                  ? Column(
                                      children: [
                                        SizedBox(
                                          height: Tokens.spacing.spacingSmall,
                                        ),
                                        AzzasPrimaryButton(
                                          expanded: true,
                                          onPressed: () => _biometricLogin(),
                                          isLoading: loadingBiometric,
                                          child: const Text(
                                            "Entrar na conta",
                                          ),
                                        ),
                                        SizedBox(
                                            height:
                                                Tokens.spacing.spacingSmall),
                                        AzzasSecondaryButton(
                                          expanded: true,
                                          onPressed: () =>
                                              _onLogin(enabledRegister: true),
                                          isDisabled: loadingBiometric,
                                          child: const Text(
                                              "Entrar com outra conta"),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      children: [
                                        AzzasPrimaryButton(
                                          expanded: true,
                                          onPressed: () => _onLogin(),
                                          isLoading: state.isLoading,
                                          child: const Text("Entrar na conta"),
                                        ),
                                        SizedBox(
                                            height:
                                                Tokens.spacing.spacingSmall),
                                        AzzasSecondaryButton(
                                          expanded: true,
                                          onPressed: _goToRegisterRoutes,
                                          isLoading: state.isLoading,
                                          child: const Text("Cadastrar"),
                                        ),
                                      ],
                                    );
                            },
                          ),
                        ],
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        _AccountOptions(isLoggedIn: isLoggedIn),
                        SizedBox(height: Tokens.spacing.spacingLarge),
                        const _HelpSection(),
                        SizedBox(height: Tokens.spacing.spacingLarge),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class _AccountOptions extends StatefulWidget {
  final bool isLoggedIn;

  const _AccountOptions({
    required this.isLoggedIn,
  });

  @override
  State<_AccountOptions> createState() => _AccountOptionsState();
}

class _AccountOptionsState extends State<_AccountOptions> {
  final _authCubit = Modular.get<AuthCubit>();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isLoggedIn) ...[
          Text(
            "Seu perfil",
            style: Tokens.typography.headings.small.smallRegular,
          ),
          SizedBox(height: Tokens.spacing.spacingMedium),
          AzzasListItemIcon(
            onTap: () async {
              await AccountEvents.logMenuAccount(
                  local: 'my_account', opcaoClicada: "dados-pessoais");
              Modular.to.pushNamed('/account/personal_data');
            },
            title: "Dados pessoais",
            divider: true,
            leftIcon: Icon(
              Tokens.icons.shipping.home,
              size: 24,
            ),
          ),
          AzzasListItemIcon(
            onTap: () async {
              await AccountEvents.logMenuAccount(
                  local: 'my_account', opcaoClicada: "pedidos-e-devolucoes");
              Modular.to.pushNamed('/account/my_orders');
            },
            title: "Pedidos e devoluções",
            divider: true,
            leftIcon: Icon(
              Tokens.icons.shipping.vehicle,
              size: 24,
            ),
          ),
          _CheckingAccount(),
          _Biometric(),
        ],
        const _Notifications(),
        const _Location(),
        if (widget.isLoggedIn) ...[
          // AzzasListItemIcon(
          //   onTap: () {
          //     // TODO: Adicionar função para ativar localização
          //   },
          //   title: "Cartões",
          //   divider: true,
          //   leftIcon: Icon(
          //     Tokens.icons.payment.creditCard,
          //     size: 24,
          //   ),
          // ),
          AzzasListItemIcon(
            onTap: () async {
              await _authCubit.logOut();
              if (mounted) {
                AzzasSnackBar.show(
                  context: context,
                  message: 'Até a próxima.',
                );
              }

              Modular.to.navigate('/');
            },
            title: "Sair",
            divider: true,
            leftIcon: Icon(
              Tokens.icons.navigation.exit,
              size: 24,
            ),
          ),
          AzzasListItemIcon(
            onTap: _goToDeleteAccount,
            title: "Excluir conta",
            divider: true,
            leftIcon: Icon(
              Tokens.icons.feedback.error,
              size: 24,
            ),
          ),
        ],
      ],
    );
  }

  void _goToDeleteAccount() {
    Modular.to.pushNamed(
      '/webview',
      arguments: const WebViewParams(
        url: CrisBarrosAppConfig.deleteAccountUrl,
      ),
    );
  }
}

class _HelpSection extends StatefulWidget {
  const _HelpSection();

  @override
  State<_HelpSection> createState() => _HelpSectionState();
}

class _HelpSectionState extends State<_HelpSection> {
  String? version;

  @override
  void initState() {
    super.initState();
    _getVersion();
  }

  Future<void> _getVersion() async {
    PackageInfo info = await PackageInfo.fromPlatform();
    setState(() {
      version = info.version;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Dúvidas?",
          style: Tokens.typography.headings.small.smallRegular,
        ),
        SizedBox(height: Tokens.spacing.spacingLarge),
        AzzasListItemIcon(
          onTap: () => _goToPersonalShopper(CrisBarrosAppConfig.whatsapp),
          leftIcon: Icon(
            Tokens.icons.socialMedia.whatsapp,
            size: 24,
          ),
          title: "Cris Barros Concierge",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: () => _goToPersonalShopper(CrisBarrosAppConfig.whatsappLyna),
          leftIcon: Icon(
            Tokens.icons.socialMedia.whatsapp,
            size: 24,
          ),
          title: "Personal shopper Lyna",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: () => _goToPersonalShopper(CrisBarrosAppConfig.whatsappLidia),
          leftIcon: Icon(
            Tokens.icons.socialMedia.whatsapp,
            size: 24,
          ),
          title: "Personal shopper Lídia",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: () =>
              _goToPersonalShopper(CrisBarrosAppConfig.whatsappAnaLuiza),
          leftIcon: Icon(
            Tokens.icons.socialMedia.whatsapp,
            size: 24,
          ),
          title: "Personal shopper Ana Luiza",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: () => _goToPersonalShopper(CrisBarrosAppConfig.whatsappAndre),
          leftIcon: Icon(
            Tokens.icons.socialMedia.whatsapp,
            size: 24,
          ),
          title: "Personal shopper André",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: _goToStore,
          leftIcon: Icon(
            Tokens.icons.shipping.place,
            size: 24,
          ),
          title: "Página de lojas",
          divider: true,
        ),
        AzzasListItemIcon(
          onTap: _goToFaq,
          title: "FAQ",
          leftIcon: Icon(
            Tokens.icons.feedback.info,
            size: 24,
          ),
          divider: true,
        ),
        SizedBox(height: Tokens.spacing.spacingLarge),
        InkWell(
          onTap: _goToPrivacyPolicy,
          child: Text(
            "Política de privacidade",
            style: Tokens.typography.body.small.smallRegular,
          ),
        ),
        if (version != null) ...[
          SizedBox(height: Tokens.spacing.spacingSmall),
          Text(
            "Versão $version",
            style: Tokens.typography.body.small.smallRegular,
          ),
        ]
      ],
    );
  }

  void _goToPersonalShopper(Whatsapp whatsapp) async {
    await AccountEvents.logMenuAccount(
        local: 'my_account', opcaoClicada: "personal-shopper-online");
    final redirectUtils = Modular.get<RedirectUtils>();
    redirectUtils.launchWhatsapp(whatsapp);
  }

  void _goToStore() async {
    await AccountEvents.logMenuAccount(
        local: 'my_account', opcaoClicada: "FAQ");
    Modular.to.pushNamed(
      '/webview',
      arguments: const WebViewParams(
        url: CrisBarrosAppConfig.storePage,
      ),
    );
  }

  void _goToFaq() async {
    await AccountEvents.logMenuAccount(
        local: 'my_account', opcaoClicada: "FAQ");
    Modular.to.pushNamed(
      '/webview',
      arguments: const WebViewParams(
        url: CrisBarrosAppConfig.faqUrl,
      ),
    );
  }

  void _goToPrivacyPolicy() {
    Modular.to.pushNamed(
      '/webview',
      arguments: const WebViewParams(
        url: CrisBarrosAppConfig.privacyPolicyUrl,
      ),
    );
  }
}

class _Notifications extends StatefulWidget {
  const _Notifications();

  @override
  State<_Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<_Notifications>
    with WidgetsBindingObserver {
  bool toggleChecked = false;
  PermissionStatus? permissionStatus;
  late final permissionsManager = Modular.get<PermissionsManager>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    checkNotificationStatus();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkNotificationStatus();
    }
  }

  void checkNotificationStatus() {
    var checkPermission = permissionsManager.getStatus(Permission.notification);
    checkPermission.then((value) {
      setState(() {
        permissionStatus = value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AzzasListItemToggle(
      isChecked: permissionStatus == PermissionStatus.granted,
      title: "Notificações",
      divider: true,
      leftIcon: Icon(
        Tokens.icons.communication.email,
        size: 24,
      ),
      onTap: () async {
        await AccountEvents.logMenuAccount(
            local: 'my_account', opcaoClicada: "notificacoes");
        setState(() {
          toggleChecked = !toggleChecked;
        });
        _requestPermission(
          context,
          toggleChecked: toggleChecked,
        );
      },
    );
  }

  void _requestPermission(BuildContext context, {bool toggleChecked = true}) {
    late final permissionsManager = Modular.get<PermissionsManager>();

    if (toggleChecked) {
      if (PlatformUtils.isIOS) {
        permissionsManager.request(Permission.notification);
      } else {
        permissionsManager.openAppSettings();
      }
    } else {
      permissionsManager.openAppSettings();
    }
  }
}

class _Location extends StatefulWidget {
  const _Location();

  @override
  State<_Location> createState() => _LocationState();
}

class _LocationState extends State<_Location> with WidgetsBindingObserver {
  bool toggleChecked = false;
  bool? islocationChecked = false;
  final locationService = Modular.get<LocationService>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    checkLocationStatus();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkLocationStatus();
    }
  }

  Future<void> checkLocationStatus() async {
    final locationPermissionStatus = await locationService.canAccessLocation();

    setState(() {
      islocationChecked = locationPermissionStatus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AzzasListItemToggle(
      isChecked: islocationChecked ?? false,
      onTap: () async {
        AccountEvents.logMenuAccount(
            local: 'my_account', opcaoClicada: "localizacao");
        setState(() {
          toggleChecked = !toggleChecked;
        });
        locationService.requestPermission(toggleChecked: toggleChecked);
      },
      title: "Localização",
      divider: true,
      leftIcon: Icon(
        Tokens.icons.shipping.place,
        size: 24,
      ),
    );
  }
}

class _CheckingAccount extends StatefulWidget {
  @override
  State<_CheckingAccount> createState() => _CheckingAccountState();
}

class _CheckingAccountState extends State<_CheckingAccount> {
  final checkingAccountCubit = Modular.get<CheckingAccountCubit>();
  int totalBalance = 0;

  @override
  void initState() {
    super.initState();
    getFinancialOverview();
  }

  void getFinancialOverview() async {
    final financialOverview =
        await checkingAccountCubit.getCheckingAccountFinancialOverviews();
    setState(() {
      totalBalance = financialOverview.amount;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AzzasListItemIcon(
      onTap: () => Modular.to.pushNamed('/checking'),
      title: "Meus créditos",
      subtitle:
          'Saldo disponível: ${CurrencyHelper.format(amount: totalBalance, dividedBy100: true)}',
      subtitleStyle:
          Tokens.typography.body.extraSmall.extraSmallRegular.copyWith(
        color: totalBalance > 0
            ? Tokens.colors.success.dark
            : Tokens.colors.typography.medium,
      ),
      divider: true,
      leftIcon: Icon(
        Tokens.icons.payment.money,
        size: 24,
      ),
    );
  }
}

class _Biometric extends StatefulWidget {
  const _Biometric();

  @override
  State<_Biometric> createState() => __BiometricState();
}

class __BiometricState extends State<_Biometric> with TickerProviderStateMixin {
  final _authCubit = Modular.get<AuthCubit>();

  void _showBottomSheetDisabled() {
    showAzzasBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (BuildContext context) {
        return BottomSheetBiometricsDisabled(
          cubit: _authCubit,
        );
      },
      vsync: this,
    );
  }

  void _showBottomSheetRegistered() {
    showAzzasBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (BuildContext context) {
        return BottomSheetBiometricsRegistered(
          cubit: _authCubit,
          onCloseTap: () => Navigator.of(context).pop(),
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    Future<void> _toggleBiometricLogin(
        {required bool hasBiometricsActivated}) async {
      try {
        if (hasBiometricsActivated) {
          _showBottomSheetDisabled();
          return;
        }
        _showBottomSheetRegistered();

        AzzasSnackBar.show(
          context: context,
          message: 'Biometria ativada com sucesso',
        );
      } catch (_) {
        AzzasSnackBar.show(
          context: context,
          backgroundColor: Tokens.colors.error.pure,
          message:
              'Erro ao ${hasBiometricsActivated ? 'desativar' : 'ativar'} biometria',
        );
      }
    }

    return BlocBuilder<AuthCubit, AuthState>(
      bloc: _authCubit,
      builder: (context, state) {
        final hasBiometricsActivated = state.hasBiometrics;

        return AzzasListItemToggle(
          isChecked: hasBiometricsActivated,
          onTap: () async {
            await _toggleBiometricLogin(
              hasBiometricsActivated: hasBiometricsActivated,
            );
          },
          title: "Login por biometria",
          divider: true,
          leftIcon: Icon(
            Tokens.icons.general.person,
            size: 24,
          ),
        );
      },
    );
  }
}
