import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:flutter/material.dart';

class AccountDelete extends StatefulWidget {
  const AccountDelete({super.key});

  @override
  State<AccountDelete> createState() => _AccountDeleteState();
}

class _AccountDeleteState extends State<AccountDelete> {
  final _authCubit = Modular.get<AuthCubit>();
  final _emailController = TextEditingController();
  bool isDisabled = true;
  String email = '';

  @override
  void initState() {
    super.initState();
    NavigationEvents.logPageView(local: 'account_delete');
    _emailController.addListener(() {
      setState(() {
        email = _emailController.text;
        isDisabled = _authCubit.state.localUserInfo?.personEmail != email;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      bloc: _authCubit,
      listener: (context, state) {
        if (state.deleteSuccess == true) {
          MainPage.goHome();
          Modular.to.popUntil((route) => route.settings.name == '/');
          _showSuccessMessage();
        }

        if (state.deleteSuccess == false) {
          _showErrorMessage();
        }
      },
      builder: (context, state) {
        return Scaffold(
          body: SafeArea(
            child: Stack(
              children: [
                CustomScrollView(
                  slivers: [
                    AzzasSmallAppBar(
                      title: 'Excluir conta',
                      onBackButton: () => Navigator.of(context).pop(),
                      backIcon: Tokens.icons.navigation.left,
                      toolbarHeight: 68,
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Você pode excluir a sua conta a qualquer momento. Ao fazer, estaremos deletando:',
                              style: Tokens.typography.body.small.smallRegular
                                  .copyWith(
                                color: Tokens.colors.neutral.dark,
                              ),
                            ),
                            SizedBox(
                              height: Tokens.spacing.spacingXSmall,
                            ),
                            Text('''
•  seu cadastro no site
•  seu cadastro no aplicativo
•  seus dados pessoais
•  seus dados de pagamento
•  seu endereço''',
                                textAlign: TextAlign.left,
                                style:
                                    Tokens.typography.body.small.smallRegular),
                            SizedBox(
                              height: Tokens.spacing.spacingXSmall,
                            ),
                            Text(
                              'Caso queira realizar uma nova compra você pode fazer criando um novo cadastro quando preferir',
                              style: Tokens.typography.body.small.smallRegular
                                  .copyWith(
                                color: Tokens.colors.neutral.dark,
                              ),
                            ),
                            SizedBox(
                              height: Tokens.spacing.spacingXLarge,
                            ),
                            Text(
                              'Para prosseguir, confirme o seu email:',
                              style: Tokens.typography.body.small.smallRegular
                                  .copyWith(
                                color: Tokens.colors.neutral.dark,
                              ),
                            ),
                            SizedBox(
                              height: Tokens.spacing.spacingSmall,
                            ),
                            AzzasInput(
                              clearFieldMode: true,
                              textEditingController: _emailController,
                              hintText: 'Email',
                              placeholder: 'Email',
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                Positioned(
                  bottom:
                      MediaQuery.of(context).viewInsets.bottom > 0 ? -60 : 20,
                  left: Tokens.spacing.spacingMedium,
                  right: Tokens.spacing.spacingMedium,
                  child: AzzasPrimaryButton(
                    expanded: true,
                    isLoading: state.isLoadingDelete,
                    isDisabled: isDisabled,
                    onPressed: () async {
                      deleteAccount();
                    },
                    child: const Text('Excluir conta'),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSuccessMessage() {
    AzzasSnackBar.show(
      context: context,
      message: 'Conta excluída com sucesso!',
      status: SnackBarStatus.success,
    );
  }

  void _showErrorMessage() {
    AzzasSnackBar.show(
      context: context,
      message: 'Não foi possível excluir a conta.',
      status: SnackBarStatus.danger,
    );
  }

  void deleteAccount() async {
    try {
      await _authCubit.deleteAccount();
    } catch (e) {}
  }
}
