import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BillingAddress extends StatefulWidget {
  const BillingAddress({
    super.key,
    required this.useAddress,
    required this.updateAddress,
  });
  final bool useAddress;
  final Function(bool value) updateAddress;

  @override
  State<BillingAddress> createState() => _BillingAddressState();
}

class _BillingAddressState extends State<BillingAddress>
    with TickerProviderStateMixin {
  final _paymentCubit = Modular.get<PaymentCubit>();
  bool isButtonDisabled = true;
  Address? addressFromCep;
  String searchCepButton = 'Digite um CEP válido';
  AddressInfoParams? selectedBillingAddress;

  @override
  void initState() {
    super.initState();
    if (_paymentCubit.state.creditCardInfo.currentSelectedBillingAddress !=
        null) {
      selectedBillingAddress = _paymentCubit
          .state.creditCardInfo.currentSelectedBillingAddress!
          .toAddressInfoParams();
    }
  }

  Future<void> _validateCep(String cep) async {
    try {
      if (TextHelper.isCEP(cep)) {
        final address = await _paymentCubit.getAddressFromCep(cep);

        return setState(() {
          addressFromCep = address;
          searchCepButton = 'Inserir endereço da fatura';
        });
      }

      return setState(() {
        searchCepButton = 'Digite um CEP válido';
        addressFromCep = null;
        isButtonDisabled = true;
      });
    } catch (e) {
      return setState(() {
        searchCepButton = 'Digite um CEP válido';
        addressFromCep = null;
        isButtonDisabled = true;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    addressFromCep = null;
  }

  void _setNewBillingAddress({required Address address}) {
    _paymentCubit.addBillingAddressToCard(address);

    Navigator.of(context).pop();
  }

  void _showCreateNewAddressBottomSheet() {
    setState(() {
      addressFromCep = null;
    });
    showAzzasBottomSheet(
      context: context,
      vsync: this,
      builder: (_) {
        return BlocBuilder<PaymentCubit, PaymentState>(
          bloc: _paymentCubit,
          builder: (context, state) {
            return AzzasCreateNewBillingAddressBottomSheet(
              titleStyle: Tokens.typography.headings.small.smallRegular,
              isLoading: state.isLoadingAddress,
              validateCep: (cep) => _validateCep(cep),
              addressFromCep: addressFromCep?.toAddressInfoParams(),
              buttonTitle: searchCepButton,
              locationIcon: Icon(Tokens.icons.shipping.place),
              onFieldsSubmited: (newAdress) =>
                  _setNewBillingAddress(address: newAdress.toAddress()),
            );
          },
        );
      },
    );
  }

  void _onUpdateAddress() {
    final selectedAddress = selectedBillingAddress?.toAddress();

    if (selectedAddress != null) {
      _paymentCubit.setSelectedBillingAddress(selectedAddress);
      return Navigator.of(context).pop();
    }

    _showAzzasSnackbar(
      message: "Ocorreu um erro ao selecionar o endereço",
      isError: true,
    );
  }

  void _showAzzasSnackbar({required String message, bool isError = false}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  void _showBillingAddressBottomSheet() {
    showAzzasBottomSheet(
      context: context,
      builder: (_) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return BlocBuilder<PaymentCubit, PaymentState>(
              bloc: _paymentCubit,
              builder: (context, state) {
                final billingAddresses =
                    state.creditCardInfo.availableBillingAddresses;

                return AzzasBillingAddressesBottomSheet(
                  titleStyle: Tokens.typography.headings.small.smallRegular,
                  addresses: billingAddresses != null &&
                          billingAddresses.isNotEmpty
                      ? billingAddresses
                          .map((e) => e.toAddressInfoParams(
                                isSelected: selectedBillingAddress?.street ==
                                        e.street &&
                                    selectedBillingAddress?.number == e.number,
                              ))
                          .toList()
                      : [],
                  onCloseTap: () {
                    Navigator.of(context).pop();
                  },
                  onTapCreateNewAddress: () {
                    _showCreateNewAddressBottomSheet();
                  },
                  onAddressUpdated: _onUpdateAddress,
                  onSelectAddress: (address) {
                    setModalState(() {
                      selectedBillingAddress = address;
                    });
                  },
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PaymentCubit, PaymentState>(
      bloc: _paymentCubit,
      builder: (context, state) {
        final address = state.creditCardInfo.currentSelectedBillingAddress;

        return SizedBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Endereço da fatura',
                style: Tokens.typography.body.small.smallRegular,
              ),
              SizedBox(
                height: Tokens.spacing.spacingSmall,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (address != null) ...[
                    Flexible(
                      flex: 3,
                      child: Text(
                        address.formattedFullAddress,
                        style:
                            Tokens.typography.body.extraSmall.extraSmallRegular,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(
                      width: Tokens.spacing.spacingLarge,
                    ),
                  ],
                  AzzasLinkButton(
                    size: ButtonSize.small,
                    style: const AzzasButtonStyle(
                      border: Border(),
                    ),
                    onPressed: () {
                      _showBillingAddressBottomSheet();
                    },
                    trailing: Icon(Tokens.icons.navigation.right),
                    child: const Text('Alterar'),
                  ),
                ],
              ),
              SizedBox(
                height: Tokens.spacing.spacingSmall,
              ),
              Wrap(
                children: [
                  AzzasCheckbox(
                    onTap: (value) {
                      widget.updateAddress(value);
                    },
                    isChecked: widget.useAddress,
                  ),
                  SizedBox(
                    width: Tokens.spacing.spacingXSmall,
                  ),
                  Text(
                    'Usar esse endereço na fatura',
                    style: Tokens.typography.body.small.smallRegular,
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }
}
