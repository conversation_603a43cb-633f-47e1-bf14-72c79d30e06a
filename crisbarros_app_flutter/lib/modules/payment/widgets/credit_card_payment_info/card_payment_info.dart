import 'package:azzas_analytics/events/checkout/payment_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/credit_card_payment_info/azzas_new_credit_card_animated_card_widget.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/credit_card_payment_info/billing_address.dart';
import 'package:flutter/material.dart';

class CardInfoData {
  final String cardNumber;
  final String cardHolderName;
  final String cardExpireDate;
  final String cardSecurityCode;

  CardInfoData({
    required this.cardNumber,
    required this.cardHolderName,
    required this.cardExpireDate,
    required this.cardSecurityCode,
  });
}

class CardPaymentInfoPage extends StatefulWidget {
  const CardPaymentInfoPage({
    super.key,
  });

  @override
  State<CardPaymentInfoPage> createState() => _CardPaymentInfoPageState();
}

class _CardPaymentInfoPageState extends State<CardPaymentInfoPage>
    with TickerProviderStateMixin {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _paymentCubit = Modular.get<PaymentCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  final creditCardNumber = TextEditingController();
  final creditCardHolderName = TextEditingController();
  final creditCardExpireDate = TextEditingController();
  final creditCardSecurityCode = TextEditingController();
  final userDocument = TextEditingController();
  final creditCardNickName = TextEditingController();
  final ValueNotifier<CardInfoData> cardInfoData =
      ValueNotifier<CardInfoData>(CardInfoData(
    cardNumber: '',
    cardHolderName: '',
    cardExpireDate: '',
    cardSecurityCode: '',
  ));
  InstallmentOrderForm? selectedInstallment;

  //TODO: Talvez seja melhor mover esses estados para um cubit próprio dessa tela posteriormente
  bool useAddress = true;
  bool disableButton = true;
  bool useAddressError = false;
  final formKey = GlobalKey<FormState>();
  bool isCreditCardFieldsValid = false;

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'card_payment_info');
    _paymentCubit.setAddressFromOrderForm();

    super.initState();
  }

  // TODO: Refatorar posteriomente
  void validateFields() {
    useAddressError = !useAddress;

    bool areCreditCardFieldsFilled = creditCardNumber.text.isNotEmpty &&
        creditCardHolderName.text.isNotEmpty &&
        creditCardExpireDate.text.isNotEmpty &&
        creditCardSecurityCode.text.isNotEmpty &&
        userDocument.text.isNotEmpty;

    if (areCreditCardFieldsFilled) {
      if (formKey.currentState?.validate() ?? false) {
        setState(() {
          isCreditCardFieldsValid = true;
        });
      } else {
        setState(() {
          isCreditCardFieldsValid = false;
        });
      }
    } else {
      setState(() {
        isCreditCardFieldsValid = false;
      });
    }

    if (areCreditCardFieldsFilled &&
        !useAddressError &&
        selectedInstallment != null) {
      if (formKey.currentState?.validate() ?? false) {
        setState(() {
          disableButton = false;
        });
      } else {
        setState(() {
          disableButton = true;
        });
      }
    } else {
      setState(() {
        disableButton = true;
      });
    }
  }

  onChangedInput() {
    cardInfoData.value = CardInfoData(
      cardNumber: creditCardNumber.text,
      cardHolderName: creditCardHolderName.text,
      cardExpireDate: creditCardExpireDate.text,
      cardSecurityCode: creditCardSecurityCode.text,
    );
    validateFields();
  }

  void _showAzzasSnackbar({required String message, bool isError = false}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  void _showInstallmentsBottomSheet() {
    showAzzasBottomSheet(
      context: context,
      builder: (_) {
        final paymentSystemIdFromCardNumber =
            CreditCardHelper.getPaymentSystemIdByCreditCardNumber(
                cardNumber: cardInfoData.value.cardNumber);

        final availableInstallments = _paymentCubit
            .getPaymentInstallmentsForCreditCard(paymentSystemIdFromCardNumber);

        return StatefulBuilder(builder: (context, setState) {
          return AzzasSelectInstallmentsBottomSheet(
            selectInstallmentText: selectedInstallment == null
                ? "Selecione um parcelamento"
                : "Selecionar este parcelamento",
            onCloseTap: () async {
              Navigator.of(context).pop();
            },
            showDragBar: true,
            borderRadius: BorderRadius.all(
              Radius.circular(Tokens.spacing.spacingMedium),
            ),
            installmentsSubtitleTextStyle:
                Tokens.typography.body.extraSmall.extraSmallRegular.copyWith(
              color: Tokens.colors.typography.medium,
            ),
            onSelectInstallment: (installment) {
              final selectedInstallment =
                  availableInstallments.firstWhereOrNull(
                (element) =>
                    element.count == installment.count &&
                    element.value == installment.value,
              );

              if (selectedInstallment == null) {
                return _showAzzasSnackbar(
                  message: "Parcelamento não encontrado",
                  isError: true,
                );
              }
              setState(() {
                this.selectedInstallment = selectedInstallment;
              });
            },
            installments: availableInstallments
                .map((installment) => installment.toAzzasInstallmentInfoParams(
                      isChecked: installment.equalsTo(
                        selectedInstallment,
                      ),
                    ))
                .toList(),
            isDisabled: selectedInstallment == null,
            titleStyle: Tokens.typography.body.extraLarge.extraLargeRegular,
            currentInstallment: selectedInstallment?.label(),
            onButtonPressed: () async {
              AzzasAnalyticsEvents.logSelectContent(
                contentType:
                    'pagamento:parcelamento:${selectedInstallment?.count}',
              );
              validateFields();
              Navigator.of(context).pop();
            },
          );
        });
      },
      vsync: this,
    );
  }

  Future<void> _navigateToOrderReview() async {
    if (!formKey.currentState!.validate() ||
        selectedInstallment == null ||
        !isCreditCardFieldsValid) {
      return;
    }

    final paymentSytemId =
        CreditCardHelper.getPaymentSystemIdByCreditCardNumber(
      cardNumber: cardInfoData.value.cardNumber,
    );

    final newCreditCardInfoData = CreditCardInfoData(
      cardNumber: cardInfoData.value.cardNumber,
      cardHolderName: cardInfoData.value.cardHolderName,
      expirationDate: cardInfoData.value.cardExpireDate,
      cvv: cardInfoData.value.cardSecurityCode,
      currentSelectedBillingAddress:
          _paymentCubit.state.creditCardInfo.currentSelectedBillingAddress,
      isNew: true,
      selectedPaymentSystemId: paymentSytemId,
    );

    await _paymentCubit.selectCreditCardPayment(
      creditCardInfo: newCreditCardInfoData,
      installment: selectedInstallment!,
    );

    _eventDispatcher.logAddPaymentInfo(
        paymentType: AzzasAnalyticsPaymentType.creditCard);

    Modular.to.pushNamed('/checkout/order_review');
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PaymentCubit, PaymentState>(
      bloc: _paymentCubit,
      builder: (context, state) {
        final areCreditCardFieldsFilled = creditCardNumber.text.isNotEmpty &&
            creditCardHolderName.text.isNotEmpty &&
            creditCardExpireDate.text.isNotEmpty &&
            creditCardSecurityCode.text.isNotEmpty &&
            userDocument.text.isNotEmpty;

        final selectInstallmentsText =
            areCreditCardFieldsFilled && selectedInstallment != null
                ? '${selectedInstallment!.label(
                    showMultiplication: true,
                  )} ${selectedInstallment!.description}'
                : 'Selecione uma opção';

        return Scaffold(
          body: SafeArea(
            child: CustomScrollView(
              slivers: [
                AzzasSmallAppBar(
                  title: 'Novo cartão',
                  pinned: true,
                  toolbarHeight: Tokens.spacing.spacingXUltraLarge,
                  onBackButton: () {
                    Navigator.of(context).pop();
                  },
                  centerTitle: true,
                  titleTextStyle:
                      Tokens.typography.body.small.smallRegular.copyWith(
                    color: Tokens.colors.brand.light,
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Tokens.spacing.spacingMedium,
                    ),
                    child: Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: Tokens.spacing.spacingXLarge,
                          ),
                          Text(
                            'Adicionar novo cartão',
                            style:
                                Tokens.typography.headings.small.smallRegular,
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingXSmall,
                          ),
                          Text(
                            'Preencha as informações para prosseguir',
                            style: Tokens.typography.body.small.smallRegular,
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingLarge,
                          ),
                          ValueListenableBuilder(
                            valueListenable: cardInfoData,
                            builder: (context, value, child) {
                              return AzzasNewCreditCardAnimatedCard(
                                cardCvv: cardInfoData.value.cardSecurityCode,
                                cardExpirationDate:
                                    cardInfoData.value.cardExpireDate,
                                cardHolderName:
                                    cardInfoData.value.cardHolderName,
                                cardNumber: cardInfoData.value.cardNumber,
                              );
                            },
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingMedium,
                          ),
                          AzzasInput(
                            textEditingController: creditCardNumber,
                            hintText: '',
                            placeholder: 'Número do cartão',
                            cursorHeight: 24,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final isValid =
                                    CreditCardHelper.validateCardByflag(
                                        cardNumber: value);
                                return !isValid
                                    ? "Digite um número de cartão válido"
                                    : null;
                              }
                              return null;
                            },
                            keyboardType:
                                const TextInputType.numberWithOptions(),
                            inputFormatter:
                                AzzasInputMask.creditCardNumber.formatter,
                            onChanged: (value) {
                              onChangedInput();
                            },
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingSmall,
                          ),
                          AzzasInput(
                            textEditingController: creditCardHolderName,
                            hintText: '',
                            cursorHeight: 24,
                            placeholder: 'Nome do titular',
                            validator: (value) =>
                                value!.isEmpty ? "Digite um nome válido" : null,
                            onChanged: (value) {
                              onChangedInput();
                            },
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingSmall,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Flexible(
                                child: AzzasInput(
                                  textEditingController: creditCardExpireDate,
                                  hintText: '',
                                  placeholder: 'Data de validade',
                                  size: AzzasInputSize.large,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(),
                                  inputFormatter:
                                      AzzasInputMask.dateDDMM.formatter,
                                  validator: (value) {
                                    if (value != null && value.length >= 5) {
                                      final isNotValidDate =
                                          CreditCardHelper.validateExpiryDate(
                                              value);
                                      return isNotValidDate
                                          ? "Digite uma data válida"
                                          : null;
                                    }
                                    return "Digite uma data válida";
                                  },
                                  cursorHeight: 24,
                                  errorMaxLines: 3,
                                  autoValidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  onChanged: (value) {
                                    onChangedInput();
                                  },
                                ),
                              ),
                              SizedBox(
                                width: Tokens.spacing.spacingSmall,
                              ),
                              Flexible(
                                child: AzzasInput(
                                  textEditingController: creditCardSecurityCode,
                                  hintText: '',
                                  placeholder: 'CVV',
                                  cursorHeight: 24,
                                  autoValidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  inputFormatter: AzzasInputMask.cvv.formatter,
                                  validator: (value) => value == null ||
                                          value.isEmpty ||
                                          (value.length != 3 &&
                                              value.length != 4)
                                      ? "Digite um código válido"
                                      : null,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(),
                                  onChanged: (value) {
                                    onChangedInput();
                                  },
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingSmall,
                          ),
                          AzzasInput(
                            textEditingController: userDocument,
                            cursorHeight: 24,
                            inputFormatter: AzzasInputMask.cpf.formatter,
                            hintText: '',
                            placeholder: 'CPF',
                            keyboardType:
                                const TextInputType.numberWithOptions(),
                            validator: (value) => StringHelper.isCpf(value!)
                                ? null
                                : "Digite um CPF válido",
                            onChanged: (value) {
                              validateFields();
                            },
                          ),
                          //TODO: Adicionar posteriormente campo de apelido do cartão
                          // SizedBox(
                          //   height: Tokens.spacing.spacingSmall,
                          // ),
                          // AzzasInput(
                          //   textEditingController: creditCardNickName,
                          //   hintText: '',
                          //   placeholder: 'Apelido do cartão (opcional)',
                          //   autoValidateMode:
                          //       AutovalidateMode.onUserInteraction,
                          //   cursorHeight: 24,
                          // ),
                          SizedBox(
                            height: Tokens.spacing.spacingMedium,
                          ),
                          Text(
                            'Parcelamento:',
                            style: Tokens.typography.body.small.smallRegular,
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingSmall,
                          ),
                          AzzasSecondaryButton(
                            style: selectedInstallment == null
                                ? AzzasButtonStyle(
                                    borderColor: Tokens.colors.error.medium,
                                  )
                                : null,
                            onPressed: () {
                              _showInstallmentsBottomSheet();
                            },
                            isDisabled: !isCreditCardFieldsValid,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  selectInstallmentsText,
                                  textScaler: TextScaleHelper.clampTextScale(
                                      context,
                                      maxScaleFactor: 1.35),
                                ),
                                SizedBox(
                                  width: Tokens.spacing.spacingXSmall,
                                ),
                                Icon(Tokens.icons.navigation.right),
                              ],
                            ),
                          ),
                          if (selectedInstallment == null) ...[
                            SizedBox(
                              height: Tokens.spacing.spacingSmall,
                            ),
                            Text(
                              'Escolha uma opção de parcelamento',
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular
                                  .copyWith(
                                color: Tokens.colors.error.medium,
                              ),
                            ),
                          ],
                          SizedBox(
                            height: Tokens.spacing.spacingLarge,
                          ),
                          const Divider(
                            height: 0.5,
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingLarge,
                          ),
                          //TODO: Adicionar posteriormente botão de salvar cartão

                          // Row(
                          //   children: [
                          //     AzzasToggle(
                          //       toggleFunction: (value) {
                          // PaymentEvents.logSelectPaymentOption(option: 'cartao');
                          //         setState(() {
                          //           saveCard = value;
                          //         });
                          //       },
                          //       isSelected: saveCard,
                          //     ),
                          //     SizedBox(
                          //       width: Tokens.spacing.spacingXSmall,
                          //     ),
                          //     Text(
                          //       'Salvar dados do cartão',
                          //       style:
                          //           Tokens.typography.body.small.smallRegular,
                          //     ),
                          //   ],
                          // ),
                          // SizedBox(
                          //   height: Tokens.spacing.spacingLarge,
                          // ),
                          // const Divider(
                          //   height: 0.5,
                          // ),
                          SizedBox(
                            height: Tokens.spacing.spacingLarge,
                          ),
                          BillingAddress(
                            useAddress: useAddress,
                            updateAddress: (value) {
                              setState(() {
                                useAddressError = !value;
                                useAddress = value;
                              });
                              validateFields();
                            },
                          ),
                          if (useAddressError) ...[
                            SizedBox(
                              height: Tokens.spacing.spacingSmall,
                            ),
                            Text(
                              'Confirme ou altere o endereço da fatura',
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular
                                  .copyWith(
                                color: Tokens.colors.error.medium,
                              ),
                            ),
                          ],
                          SizedBox(
                            height: Tokens.spacing.spacingMedium,
                          ),
                          BlocBuilder<OrderFormCubit, OrderFormState>(
                            bloc: _orderFormCubit,
                            builder: (context, orderFormState) {
                              return AzzasPrimaryButton(
                                expanded: true,
                                isLoading: orderFormState.isLoading,
                                isDisabled: disableButton,
                                onPressed: () {
                                  _navigateToOrderReview();
                                },
                                child: Text(
                                  disableButton
                                      ? 'Preencha as informações'
                                      : 'Continuar',
                                  style:
                                      Tokens.typography.body.small.smallRegular,
                                ),
                              );
                            },
                          ),
                          SizedBox(
                            height: Tokens.spacing.spacingMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
