import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class AzzasNewCreditCardAnimatedCard extends StatefulWidget {
  const AzzasNewCreditCardAnimatedCard({
    Key? key,
    required this.cardCvv,
    required this.cardExpirationDate,
    required this.cardNumber,
    required this.cardHolderName,
    this.backgroundColor,
    this.width = 327.0,
    this.height = 184.0,
    this.cardBorderRadius = 16.0,
    this.elevation = 0,
    this.borderOnForeground = false,
    this.aspectRatio = 1.78,
    this.subtitle = 'CPF do titular',
    this.cardNumberPlaceholder = '0000 0000 0000 0000',
  }) : super(key: key);

  final String cardNumber;
  final String cardHolderName;
  final String cardCvv;
  final String cardExpirationDate;
  final Color? backgroundColor;
  final double cardBorderRadius;
  final double elevation;
  final bool borderOnForeground;
  final double aspectRatio;
  final double height;
  final double width;
  final String subtitle;
  final String cardNumberPlaceholder;

  @override
  State<AzzasNewCreditCardAnimatedCard> createState() =>
      _AzzasNewCreditCardAnimatedCardState();
}

class _AzzasNewCreditCardAnimatedCardState
    extends State<AzzasNewCreditCardAnimatedCard> {
  @override
  Widget build(BuildContext context) {
    return _SMCardContainer(
      cardNumber: widget.cardNumber,
      cardHolderName: widget.cardHolderName,
      cvv: widget.cardCvv,
      expirationDate: widget.cardExpirationDate,
      backgroundColor: widget.backgroundColor,
      elevation: widget.elevation,
      width: widget.width,
      height: widget.height,
      cardBorderRadius: widget.cardBorderRadius,
      borderOnForeground: widget.borderOnForeground,
      aspectRatio: widget.aspectRatio,
      subtitle: widget.subtitle,
      cardNumberPlaceholder: widget.cardNumberPlaceholder,
    );
  }
}

class _SMCardContainer extends StatelessWidget {
  final Color? backgroundColor;
  final double cardBorderRadius;
  final double elevation;
  final bool borderOnForeground;
  final double aspectRatio;
  final double height;
  final double width;
  final String subtitle;
  final String cardNumber;
  final String cardNumberPlaceholder;
  final String cardHolderName;
  final String cvv;
  final String expirationDate;

  const _SMCardContainer({
    required this.backgroundColor,
    required this.width,
    required this.height,
    required this.cardBorderRadius,
    required this.elevation,
    required this.borderOnForeground,
    required this.aspectRatio,
    required this.subtitle,
    required this.cardHolderName,
    required this.cardNumber,
    required this.cardNumberPlaceholder,
    required this.cvv,
    required this.expirationDate,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        color: Colors.black,
        borderOnForeground: borderOnForeground,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(cardBorderRadius)),
        elevation: elevation,
        child: SizedBox(
          height: height,
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: const EdgeInsets.only(left: 16, top: 24, bottom: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cardHolderName.isEmpty ? 'nome impresso' : cardHolderName,
                  textScaler: TextScaleHelper.clampTextScale(context),
                  style: Tokens.typography.body.extraSmall.extraSmallRegular
                      .copyWith(
                    color: Tokens.colors.neutral.light,
                  ),
                ),
                cardNumber.isNotEmpty
                    ? Text(
                        cardNumber,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                  textScaler: TextScaleHelper.clampTextScale(context, maxScaleFactor: 1.0),
                        style: Tokens.typography.headings.small.smallRegular
                            .copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      )
                    : Text(
                        cardNumberPlaceholder,
                        softWrap: false,
                        overflow: TextOverflow.visible,
                  textScaler: TextScaleHelper.clampTextScale(context, maxScaleFactor: 1.0),
                        style: Tokens.typography.headings.small.smallRegular
                            .copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      ),
                Wrap(
                  children: [
                    Visibility(
                      visible: expirationDate.isNotEmpty,
                      replacement: Text(
                        'validade mm/aa',
                        textScaler: TextScaleHelper.clampTextScale(context),
                        style:
                            Tokens.typography.body.small.smallRegular.copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      ),
                      child: Text(
                        'validade $expirationDate',
                        textScaler: TextScaleHelper.clampTextScale(context),
                        style:
                            Tokens.typography.body.small.smallRegular.copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: Tokens.spacing.spacingXLarge,
                    ),
                    Visibility(
                      visible: cvv.isNotEmpty,
                      replacement: Text(
                        'cod. segurança 000',
                        textScaler: TextScaleHelper.clampTextScale(context),
                        style:
                            Tokens.typography.body.small.smallRegular.copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      ),
                      child: Text(
                        'cod. segurança $cvv',
                        textScaler: TextScaleHelper.clampTextScale(context),
                        style:
                            Tokens.typography.body.small.smallRegular.copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
