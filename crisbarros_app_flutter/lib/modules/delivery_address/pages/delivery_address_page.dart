import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class DeliveryAddressPage extends StatefulWidget {
  const DeliveryAddressPage({super.key});

  @override
  State<DeliveryAddressPage> createState() => _DeliveryAddressPageState();
}

class _DeliveryAddressPageState extends State<DeliveryAddressPage>
    with TickerProviderStateMixin {
  bool loadingGetLocation = false;
  bool showClearInput = false;
  final _cepInputController = TextEditingController();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'delivery_address');
    _cepInputController.addListener(_onCepChanged);
    super.initState();
  }

  void _onCepChanged() {}

  void _clearCep() => _cepInputController.clear();

  void _onConfirmCep(Function(void Function()) setState) async {
    setState(() {
      loadingGetLocation = true;
    });
    await Future.delayed(const Duration(seconds: 2)).then((value) {
      setState(() {
        loadingGetLocation = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Tokens.spacing.spacingMedium,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: Tokens.spacing.spacingLarge,
                    ),
                    Text(
                      'Como você quer receber sua compra?',
                      style: Tokens.typography.headings.small.smallRegular,
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingSmall,
                    ),
                    Text(
                      'Digite seu CEP para consultar as opções de entrega.',
                      style: Tokens.typography.body.small.smallRegular,
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingMedium,
                    ),
                    AzzasCepButton(
                      controller: _cepInputController,
                      showSearchButton: false,
                      suffix: IconButton(
                        icon: Icon(Tokens.icons.action.close),
                        onPressed: _clearCep,
                      ),
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingXLarge,
                    ),
                    AzzasPrimaryButton(
                      expanded: true,
                      onPressed: () async {
                        await AnalyticsService.trackEvent('select_content', {
                          'content_type': 'checkout:complete-o-endereco',
                        });
                        Modular.to.pushNamed('delivery-address-filled');
                      },
                      child: const Text(
                        "Calcular para este CEP",
                      ),
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingSmall,
                    ),
                    AzzasSecondaryButton(
                      expanded: true,
                      onPressed: () {
                        showAzzasBottomSheet(
                          context: context,
                          builder: (context) {
                            return StatefulBuilder(
                              builder: (context, setState) {
                                return AzzasActivateLocationBottomSheet(
                                  onCloseTap: () => Navigator.pop(context),
                                  title: 'Podemos acessar sua localização?',
                                  titleStyle: Tokens
                                      .typography.headings.small.smallRegular,
                                  bodyStyle:
                                      Tokens.typography.body.small.smallRegular,
                                  confirmButtonText: 'Sim, vamos lá',
                                  cancelButtonText: 'Agora não',
                                  loadingGetLocation: loadingGetLocation,
                                  onConfirmButtonTap: () async {
                                    await AnalyticsService.trackEvent(
                                        'select_content', {
                                      'content_type':
                                          'checkout:acessar-localização:confirmar',
                                    });
                                    _onConfirmCep(setState);
                                  },
                                  onCancelButtonTap: () =>
                                      Navigator.pop(context),
                                );
                              },
                            );
                          },
                          vsync: this,
                        );
                      },
                      child: const Text(
                        "Ativar localização",
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
