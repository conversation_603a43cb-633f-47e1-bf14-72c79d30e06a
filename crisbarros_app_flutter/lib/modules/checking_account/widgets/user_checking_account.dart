import 'dart:async';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';
import 'widgets.dart';

class UserCheckingAccount extends StatefulWidget {
  final CheckingAccountCubit checkingAccountCubit;

  const UserCheckingAccount({super.key, required this.checkingAccountCubit});

  @override
  State<UserCheckingAccount> createState() => _UserCheckingAccountState();
}

class _UserCheckingAccountState extends State<UserCheckingAccount> {
  int totalBalance = 0;
  List<GiftCardsToExpire> giftCardsToExpire = [];
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    scheduleMicrotask(() async {
      final financialOverview = await widget.checkingAccountCubit
          .getCheckingAccountFinancialOverviews();
      final expirationDetails =
          await widget.checkingAccountCubit.getExpirationDetails();
      totalBalance = financialOverview.amount;
      giftCardsToExpire = expirationDetails.balances;

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            AzzasSmallAppBar(
              onBackButton: () => Navigator.of(context).pop(),
              backIcon: Tokens.icons.action.close,
              title: 'Meus créditos',
              titleTextStyle: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.typography.light),
              pinned: true,
              isColunm: true,
              backgroundColor: Color(0xFFF2F2F2),
              toolbarHeight: 116,
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.only(
                  top: Tokens.spacing.spacingMedium,
                  bottom: Tokens.spacing.spacingSmall,
                ),
                child: _AccountScreenOverview(
                  isLoading: isLoading,
                  totalBalance: totalBalance,
                  balancesToExpire: giftCardsToExpire,
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: FinancialTransactions(
                listType: TransactionsListType.splitByMonth,
                cardBuilder: (_, transaction) => TransactionCard(
                  transaction: transaction,
                ),
                cubit: widget.checkingAccountCubit,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AccountOverviewContainer extends StatelessWidget {
  const _AccountOverviewContainer({required this.overviewBuilder, this.margin});

  final Widget Function(BuildContext context) overviewBuilder;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: margin,
      padding: EdgeInsets.symmetric(
        horizontal: Tokens.spacing.spacingSmall,
      ),
      decoration: BoxDecoration(
        color: Tokens.colors.neutral.pure,
        border: Border.all(color: Tokens.colors.neutral.medium, width: 2),
      ),
      child: overviewBuilder(context),
    );
  }
}

class _AccountBalanceOverview extends StatelessWidget {
  const _AccountBalanceOverview({
    required this.totalBalance,
    required this.isLoading,
    required this.balancesToExpire,
  });

  final int totalBalance;
  final bool isLoading;
  final List<GiftCardsToExpire> balancesToExpire;

  @override
  Widget build(BuildContext context) {
    return _AccountOverviewContainer(
      margin: EdgeInsets.all(Tokens.spacing.spacingMedium),
      overviewBuilder: (_) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: Tokens.spacing.spacingMedium,
          ),
          Icon(Tokens.icons.payment.money, size: 28.0, color: Colors.black),
          BalanceOverview(
            isLoading: isLoading,
            totalAmount: totalBalance,
            balancesToExpire: balancesToExpire,
          ),
        ],
      ),
    );
  }
}

class _AccountScreenOverview extends StatelessWidget {
  const _AccountScreenOverview({
    required this.totalBalance,
    required this.balancesToExpire,
    required this.isLoading,
  });

  final int totalBalance;
  final List<GiftCardsToExpire> balancesToExpire;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _AccountBalanceOverview(
          totalBalance: totalBalance,
          isLoading: isLoading,
          balancesToExpire: balancesToExpire,
        ),
      ],
    );
  }
}
