import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class UserDocumentUnknown extends StatefulWidget {
  final VoidCallback onTapButton;
  const UserDocumentUnknown({super.key, required this.onTapButton});

  @override
  State<UserDocumentUnknown> createState() => _UserDocumentUnknownState();
}

class _UserDocumentUnknownState extends State<UserDocumentUnknown> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          physics: NeverScrollableScrollPhysics(),
          slivers: [
            AzzasSmallAppBar(
              onBackButton: () => Navigator.of(context).pop(),
              backIcon: Tokens.icons.action.close,
              title: 'Meus créditos',
              titleTextStyle: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.typography.light),
              pinned: true,
              isColunm: true,
              backgroundColor: Color(0xFFF2F2F2),
              toolbarHeight: 116,
            ),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  const SizedBox(height: 140.0),
                  Icon(
                    Tokens.icons.feedback.warning,
                    size: 40,
                    color: Colors.black,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: Tokens.spacing.spacingSmall,
                        horizontal: Tokens.spacing.spacingMedium),
                    child: Text(
                        'Complete o seu cadastro para visualizar e acompanhar o valor dos créditos disponíveis para usar online.',
                        textAlign: TextAlign.center,
                        style: Tokens.typography.body.medium.mediumRegular
                            .copyWith(color: Tokens.colors.typography.medium)),
                  ),
                  AzzasPrimaryButton(
                    size: ButtonSize.small,
                    onPressed: () => widget.onTapButton,
                    child: Text(
                      "Completar cadastro",
                      style:
                          Tokens.typography.body.extraSmall.extraSmallRegular,
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
