import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class UserEmptyAccount extends StatefulWidget {
  const UserEmptyAccount({super.key});

  @override
  State<UserEmptyAccount> createState() => _UserEmptyAccountState();
}

class _UserEmptyAccountState extends State<UserEmptyAccount> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          physics: NeverScrollableScrollPhysics(),
          slivers: [
            AzzasSmallAppBar(
              onBackButton: () => Navigator.of(context).pop(),
              backIcon: Tokens.icons.action.close,
              title: 'Meus créditos',
              titleTextStyle: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.typography.light),
              pinned: true,
              isColunm: true,
              backgroundColor: Color(0xFFF2F2F2),
              toolbarHeight: 116,
            ),
            SliverToBoxAdapter(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 140.0),
                    Icon(
                      Tokens.icons.payment.money,
                      size: 40,
                      color: Colors.black,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: Tokens.spacing.spacingSmall,
                      ),
                      child: Text(
                          'Você não possui nenhum \ncrédito disponível para uso.',
                          textAlign: TextAlign.center,
                          style: Tokens.typography.body.medium.mediumRegular
                              .copyWith(
                                  color: Tokens.colors.typography.medium)),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
