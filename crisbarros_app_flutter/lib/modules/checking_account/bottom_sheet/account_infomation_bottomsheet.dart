import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class AccountInfomationBottomsheet extends StatefulWidget {
  final CheckingAccountStorage? storage;
  const AccountInfomationBottomsheet({super.key, required this.storage});

  @override
  State<AccountInfomationBottomsheet> createState() =>
      _AccountInfomationBottomsheetState();
}

class _AccountInfomationBottomsheetState
    extends State<AccountInfomationBottomsheet> {
  @override
  Widget build(BuildContext context) {
    return AzzasBottomSheet(
      padding: EdgeInsets.only(
          top: Tokens.spacing.spacingMedium,
          right: Tokens.spacing.spacingMedium),
      borderRadius: BorderRadius.zero,
      showDragBar: false,
      onCloseTap: () {
        Navigator.of(context).pop();
        widget.storage?.saveCheckingAccountBottomSheet();
      },
      closeIcon: Tokens.icons.action.close,
      content: SizedBox(
        height: 270,
        child: Padding(
          padding: EdgeInsets.only(left: Tokens.spacing.spacingMedium),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Meus créditos',
                  textAlign: TextAlign.start,
                  style: Tokens.typography.headings.small.smallRegular
                      .copyWith(color: Tokens.colors.neutral.dark),
                ),
                SizedBox(
                  height: Tokens.spacing.spacingSmall,
                ),
                Text(
                  'Uma forma mais fácil de acompanhar e resgatar seus créditos gerados em devoluções e/ou bônus recebidos. Nosso objetivo é ajudar a visualizar suas movimentações e entender os prazos de uso para que você possa garantir seus novos desejos.',
                  style: Tokens.typography.body.small.smallRegular
                      .copyWith(color: Tokens.colors.neutral.dark),
                ),
                SizedBox(
                  height: Tokens.spacing.spacingXLarge,
                ),
                AzzasPrimaryButton(
                  size: ButtonSize.large,
                  onPressed: () {
                    Navigator.of(context).pop();
                    widget.storage?.saveCheckingAccountBottomSheet();
                  },
                  expanded: true,
                  child: Text(
                    "Conhecer meus créditos",
                    style: Tokens.typography.body.extraSmall.extraSmallRegular,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
