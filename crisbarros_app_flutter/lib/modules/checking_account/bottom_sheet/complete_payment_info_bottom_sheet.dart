import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class CompletePaymentInfoBottomSheet extends StatelessWidget {
  const CompletePaymentInfoBottomSheet({
    super.key,
    required this.remainingAmountText,
  });

  factory CompletePaymentInfoBottomSheet.fromRemainigPurchaseAmount({
    required String remainingAmountText,
  }) {
    return CompletePaymentInfoBottomSheet(
      remainingAmountText: remainingAmountText,
    );
  }

  final String remainingAmountText;

  @override
  Widget build(BuildContext context) {
    return AzzasBottomSheet(
        padding: EdgeInsets.only(
          left: Tokens.spacing.spacingMedium,
          top: Tokens.spacing.spacingMedium,
          right: Tokens.spacing.spacingMedium,
        ),
        borderRadius: BorderRadius.zero,
        showDragBar: true,
        onCloseTap: () => Navigator.pop(context),
        closeIcon: Tokens.icons.action.close,
        content: SizedBox(
          height: 250,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: Tokens.spacing.spacingXSmall),
              Text(
                'Saldo aplicado com sucesso',
                style: Tokens.typography.headings.small.smallRegular
                    .copyWith(color: Tokens.colors.neutral.dark),
              ),
              SizedBox(
                height: Tokens.spacing.spacingSmall,
              ),
              Text(
                'Atenção: ainda faltam ${remainingAmountText} para seguir com a sua compra. Complete a forma de pagamento',
                style: Tokens.typography.body.small.smallRegular
                    .copyWith(color: Tokens.colors.neutral.dark),
              ),
              SizedBox(
                height: Tokens.spacing.spacingXLarge,
              ),
              AzzasPrimaryButton(
                size: ButtonSize.large,
                onPressed: () => Navigator.of(context).pop(),
                expanded: true,
                child: Text(
                  "Completar forma de pagamento",
                  style: Tokens.typography.body.extraSmall.extraSmallRegular,
                ),
              ),
            ],
          ),
        ));
  }
}
