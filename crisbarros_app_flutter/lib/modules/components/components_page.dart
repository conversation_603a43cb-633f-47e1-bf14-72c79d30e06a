import 'package:azzas_analytics/events/category/favorites_page_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/app_tokens.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class ComponentsPage extends StatefulWidget {
  const ComponentsPage({super.key});

  @override
  State<ComponentsPage> createState() => _ComponentsPageState();
}

class _ComponentsPageState extends State<ComponentsPage>
    with TickerProviderStateMixin {
  bool toggleActive = true;
  bool isFavoriteButtonActive = false;
  bool isProductInWishlist = false;
  bool isCardAddress = true;
  bool checkboxActive = true;
  bool radioActive = true;
  double minSliderManagent = 500;
  double maxSliderManagent = 1400.0;
  List<String> listSelectedSliderManagent = [];
  String installmentOption = 'A';
  AzzasDeliveryOption? selectedDeliveryOption;
  static const String imageUrl =
      "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100";
  bool expanded = false;
  ColorSelectorItem? colorSelectorItem;
  final _formKey = GlobalKey<FormState>();
  final _cepInputController = TextEditingController();
  final _streetInputController = TextEditingController();
  final _numberInputController = TextEditingController();
  final _complementInputController = TextEditingController();
  final _responsibleNameInputController = TextEditingController();
  final _neighborhoodInputController = TextEditingController();

  showTestToast(BuildContext context, {String? message}) {
    final scaffold = ScaffoldMessenger.of(context);
    scaffold.showSnackBar(
      SnackBar(
        content: Text(message ?? "Teste de onTap"),
        action: SnackBarAction(
          label: 'fechar',
          onPressed: scaffold.hideCurrentSnackBar,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: AzzasBottomBar(
        title: 'Regata Básica Feminina Regular Muscle Tee - Off White',
        buttonText: 'Comprar',
        onButtonTap: () {
          Modular.to.pushNamed('/pdp');
        },
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'R\$ 1.499',
                  style: Tokens.typography.body.extraSmall.extraSmallCaption
                      .copyWith(
                    color: AppTokens.color6,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'R\$ 1.499',
                  style: Tokens.typography.body.extraSmall.extraSmallCaption,
                ),
              ],
            ),
            Text(
              'em até 12x',
              style:
                  Tokens.typography.body.extraSmall.extraSmallRegular.copyWith(
                color: AppTokens.color6,
              ),
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            const AzzasDefaultAppBar(
              title: 'Cris Barros App',
              supportText: 'Navegue na aplicação:',
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AzzasTitleSection(text: "Modulos:"),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        AzzasButton.primary(
                          child: const Text("Dados pessoais"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/account/personal_data');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("Meus pedidos"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/account/my_orders');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("Wishlist"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/wishlist');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("Delivery"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/delivery-address');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("PDP"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/pdp');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("PDC"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/pdc/vestido');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("Pagamento"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/checkout/select-payment');
                          },
                        ),
                        AzzasButton.primary(
                          child: const Text("Complete User Data"),
                          size: ButtonSize.small,
                          onPressed: () {
                            Modular.to.pushNamed('/account/complete_user_data');
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const AzzasTitleSection(text: "Componentes:"),
                    const AzzasSubtitleSection(
                      text: "Lista de componentes desenvolvidos no azzas_ui.",
                    ),
                    Icon(
                      Tokens.icons.action.add,
                      size: 50,
                      color: Tokens.colors.error.light,
                    ),
                    const AzzasLine(),
                    const SizedBox(height: 12),
                    AzzasLine(
                      lineColor: Tokens.colors.error.pure,
                      lineSize: AzzasLineSize.large,
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      children: [
                        AzzasSpinner(
                          activeColor: Tokens.colors.brand.medium,
                        ),
                        const SizedBox(width: 32),
                        AzzasSpinner(
                          activeColor: Tokens.colors.brand.pure,
                        ),
                        const SizedBox(width: 32),
                        AzzasSpinner(
                          activeColor: Tokens.colors.brand.light,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Wrap(
                      children: [
                        const AzzasSkuSelector(text: "M"),
                        const SizedBox(width: 32),
                        AzzasSkuSelector(
                          text: "P",
                          backgroundColor: Tokens.colors.component.pure,
                          textVariant: AzzasSkuSelectorTextVariant.lowerCase,
                        ),
                        const AzzasSkuSelector(
                          text: "M",
                          description: "Só resta um!",
                        ),
                        const AzzasSkuSelector(
                          text: "G",
                          isActive: true,
                        ),
                        const SizedBox(width: 32),
                        AzzasSkuSelector(
                          text: "PP",
                          description: "Restam 2!",
                          descriptionTextStyle: Tokens
                              .typography.body.medium.mediumRegular
                              .copyWith(
                            color: Tokens.colors.success.pure,
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 24),
                    const Wrap(
                      children: [
                        AzzasSkuSelector(
                          text: "Esse é um SkuSelector expandido",
                          isActive: true,
                          isExpandable: true,
                          padding: EdgeInsets.all(4.0),
                        ),
                        SizedBox(
                          width: 10.0,
                        ),
                        AzzasSkuSelector(
                          text: "G",
                          isDisabled: true,
                          isCrossed: true,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Wrap(
                      children: [
                        AzzasSkuSelector(
                          text: "Clique aqui",
                          isActive: true,
                          isExpandable: true,
                          onTap: () {
                            final scaffold = ScaffoldMessenger.of(context);
                            scaffold.showSnackBar(
                              SnackBar(
                                content: const Text('Teste de onTap'),
                                action: SnackBarAction(
                                  label: 'fechar',
                                  onPressed: scaffold.hideCurrentSnackBar,
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFeedbackCard(
                      card: AzzasFeedbackCardParams(
                        title: "Av. Rainha Maria da Graça Meneghel",
                        subtitle: "leblon - Rio de Janeiro - rj",
                        textStyle: Tokens.typography.body.medium.mediumRegular
                            .copyWith(
                          color: Tokens.colors.neutral.light,
                        ),
                        rowAlignment: CrossAxisAlignment.start,
                        type: AzzasFeedbackCardType.success,
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFeedbackCard(
                      card: AzzasFeedbackCardParams(
                          title: "app100",
                          textStyle: Tokens.typography.body.medium.mediumRegular
                              .copyWith(
                            color: Tokens.colors.neutral.light,
                          ),
                          type: AzzasFeedbackCardType.error,
                          rowAlignment: CrossAxisAlignment.center,
                          trailing: Icon(
                            Tokens.icons.general.arrowSwap,
                            size: 18.0,
                            color: Tokens.colors.neutral.light,
                          ),
                          onTap: () {
                            final scaffold = ScaffoldMessenger.of(context);
                            scaffold.showSnackBar(
                              SnackBar(
                                content: const Text('Teste de onTap'),
                                action: SnackBarAction(
                                  label: 'fechar',
                                  onPressed: scaffold.hideCurrentSnackBar,
                                ),
                              ),
                            );
                          }),
                      supportContent: AzzasFeedbackCardSupportContentParams(
                        icon: Icon(
                          Tokens.icons.feedback.info,
                          color: Tokens.colors.brand.pure,
                          size: 18.0,
                        ),
                        iconSpacing: 8.0,
                        text: "saldo restante: R\$ 100,00",
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12,
                      children: [
                        AzzasToggle(
                          toggleFunction: (value) {
                            setState(() {
                              toggleActive = value;
                            });
                          },
                          isSelected: toggleActive,
                          size: AzzasToggleSize.medium,
                        ),
                        AzzasToggle(
                          toggleFunction: (value) {
                            setState(() {
                              toggleActive = value;
                            });
                          },
                          isSelected: toggleActive,
                          isDisabled: true,
                          size: AzzasToggleSize.small,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12,
                      children: [
                        AzzasToggle(
                          toggleFunction: (value) {
                            setState(() {
                              toggleActive = value;
                            });
                          },
                          isSelected: toggleActive,
                          size: AzzasToggleSize.medium,
                        ),
                        AzzasToggle(
                          toggleFunction: (value) {
                            setState(() {
                              toggleActive = value;
                            });
                          },
                          isSelected: toggleActive,
                          isDisabled: true,
                          size: AzzasToggleSize.small,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12,
                      children: [
                        AzzasCheckbox(
                          onTap: (value) {
                            setState(() {
                              checkboxActive = value;
                            });
                          },
                          labelText: "Check 1",
                          isChecked: checkboxActive,
                        ),
                        AzzasCheckbox(
                          onTap: (value) {
                            setState(() {
                              checkboxActive = value;
                            });
                          },
                          labelText: "Check 2",
                          checkboxSize: AzzasCheckboxSize.small,
                          isChecked: checkboxActive,
                          isDisabled: true,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12,
                      children: [
                        AzzasRadioButton(
                          onTap: () {
                            setState(() {
                              radioActive = !radioActive;
                            });
                          },
                          labelText: "Radio 1",
                          isChecked: radioActive,
                        ),
                        AzzasRadioButton(
                          onTap: () {},
                          labelText: "Radio 2",
                          radioButtonSize: AzzasRadioButtonSize.small,
                          isChecked: radioActive,
                          isDisabled: true,
                        ),
                      ],
                    ),
                    Wrap(
                      spacing: 12.0,
                      alignment: WrapAlignment.center,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        AzzasFavoriteButtonBase(
                          params: AzzasFavoriteButtonParams(
                            isActive: isFavoriteButtonActive,
                            iconSize: 16.0,
                            onTap: () {
                              setState(() {
                                isFavoriteButtonActive =
                                    !isFavoriteButtonActive;
                              });
                            },
                          ),
                        ),
                        AzzasFavoriteButtonBase(
                          params: AzzasFavoriteButtonParams(
                            isActive: isFavoriteButtonActive,
                            activeColor: Tokens.colors.brand.pure,
                            decoration: BoxDecoration(
                              color: Tokens.colors.neutral.medium,
                              borderRadius: BorderRadius.circular(18.0),
                            ),
                            buttonHeight: 36.0,
                            buttonWidth: 36.0,
                            iconSize: 16.0,
                            onTap: () {
                              setState(() {
                                isFavoriteButtonActive =
                                    !isFavoriteButtonActive;
                              });
                            },
                          ),
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                        AzzasFavoriteButtonBase(
                          params: AzzasFavoriteButtonParams(
                            isActive: isFavoriteButtonActive,
                            activeColor: Tokens.colors.error.light,
                            decoration: BoxDecoration(
                              color: Tokens.colors.neutral.medium,
                              borderRadius: BorderRadius.circular(36.0),
                            ),
                            buttonHeight: 72.0,
                            favoriteAnimation: "",
                            buttonWidth: 72.0,
                            iconSize: 32.0,
                            onTap: () {
                              setState(() {
                                isFavoriteButtonActive =
                                    !isFavoriteButtonActive;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12.0,
                      children: [
                        const AzzasProductImage(
                          imageUrl: imageUrl,
                        ),
                        AzzasProductImage(
                          imageUrl: imageUrl,
                          showFavoriteButton: true,
                          favoriteButton: AzzasFavoriteButtonBase(
                            params: AzzasFavoriteButtonParams(
                              isActive: isProductInWishlist,
                              iconSize: 16.0,
                              decoration: const BoxDecoration(
                                color: Colors.transparent,
                              ),
                              onTap: () async {
                                await Future.delayed(
                                    const Duration(milliseconds: 300));
                                setState(() {
                                  isProductInWishlist = !isProductInWishlist;
                                });
                              },
                            ),
                          ),
                        ),
                        const AzzasShimmer(
                          width: 130,
                          height: 200,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const Wrap(
                      spacing: 12,
                      children: [
                        AzzasTag(
                          label: "Tag 1",
                          size: AzzasTagSize.small,
                          type: AzzasTagType.primary,
                          outline: false,
                        ),
                        AzzasTag(
                          label: "Tag 2",
                          size: AzzasTagSize.medium,
                          type: AzzasTagType.danger,
                          outline: false,
                        ),
                        AzzasTag(
                          label: "Tag 3",
                          size: AzzasTagSize.large,
                          type: AzzasTagType.secondary,
                          outline: true,
                        ),
                        AzzasTag(
                          label: "Tag 4",
                          size: AzzasTagSize.large,
                          type: AzzasTagType.success,
                          outline: false,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: [
                        AzzasFilterButton(
                          onPressed: () {},
                          isActive: true,
                          iconType: AzzasFilterButtonIcon.rightIcon,
                        ),
                        AzzasFilterButton(
                          onPressed: () {},
                          isActive: true,
                          iconType: AzzasFilterButtonIcon.leftIcon,
                        ),
                        AzzasFilterButton(
                          onPressed: () {},
                          isActive: true,
                          iconType: AzzasFilterButtonIcon.containerColor,
                        ),
                        AzzasFilterButton(
                          onPressed: () {},
                          isActive: true,
                          icon: Icons.filter_alt,
                          iconType: AzzasFilterButtonIcon.iconOnly,
                        ),
                        AzzasFilterButton(
                          onPressed: () {},
                          isActive: true,
                          iconType: AzzasFilterButtonIcon.none,
                        ),
                      ],
                    ),
                    const AzzasCheckoutProductCard(
                      product: AzzasProductCardProductParams(
                        productId: '1',
                        name: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                        size: "P",
                        quantity: 1,
                        imageUrl: imageUrl,
                      ),
                    ),
                    AzzasCheckoutProductCard(
                      product: AzzasProductCardProductParams(
                        productId: '1',
                        name: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                        size: "P",
                        quantity: 1,
                        availability: "available",
                        imageUrl: imageUrl,
                        tagsBuilder: () {
                          return Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: Tokens.spacing.spacingXXSmall,
                            ),
                            child: AzzasTag(
                              label: "Sem estoque",
                              size: AzzasTagSize.xsmall,
                              type: AzzasTagType.danger,
                              outline: false,
                              textStyle: Tokens
                                  .typography.body.extraSmall.extraSmallRegular,
                            ),
                          );
                        },
                      ),
                    ),
                    const AzzasCheckoutProductCard(
                      product: AzzasProductCardProductParams(
                        productId: '1',
                        estimatedDelivery: "Receba até xx/xx",
                        name: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                        size: "P",
                        quantity: 1,
                        availability: "available",
                        imageUrl: imageUrl,
                      ),
                    ),
                    AzzasWishlistProductCard(
                      tagsAlignment:
                          AzzasProductInfoTagsAlignment.aboveProductTitle,
                      product: AzzasProductCardProductParams(
                        productId: '1',
                        name: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                        size: "P",
                        quantity: 1,
                        imageUrl: imageUrl,
                        tagsBuilder: () {
                          return Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: Tokens.spacing.spacingXXSmall,
                            ),
                            child: AzzasTag(
                              label: "Sem estoque",
                              size: AzzasTagSize.xsmall,
                              type: AzzasTagType.danger,
                              outline: false,
                              textStyle: Tokens
                                  .typography.body.extraSmall.extraSmallRegular,
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasInput(
                      textEditingController: TextEditingController(),
                      hintText: '  Hint Text',
                      placeholder: 'Label',
                      supportedText: 'Support text',
                      clearFieldMode: true,
                      isLoading: true,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasInput(
                      hasError: true,
                      messageError: 'Support text',
                      textEditingController: TextEditingController(),
                      hintText: '  Hint Text',
                      placeholder: 'Label',
                      supportedText: 'Support text',
                      clearFieldMode: true,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasInput(
                      enabled: false,
                      textEditingController: TextEditingController(),
                      hintText: '  Hint Text',
                      placeholder: 'Label',
                      supportedText: 'Support text',
                      clearFieldMode: true,
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasTabBar(
                      height: 80.0,
                      content: [
                        Text('Content 1'),
                        Text('Content 2'),
                        Text('Content 3'),
                      ],
                      tabs: [
                        AzzasTabItem(
                          text: 'Tab 1',
                        ),
                        AzzasTabItem(
                          text: 'Tab 2',
                        ),
                        AzzasTabItem(
                          text: 'Tab 3',
                        ),
                      ],
                    ),
                    AzzasCardAddress(
                      isStore: false,
                      title: 'Hering Rio Sul',
                      city: 'Curitiba',
                      complement: 'Apto: 4',
                      neighborhood: 'Demetrrios',
                      streetName: 'Av. Jorge Tench',
                      voucher: '+5% OFF',
                      isChecked: isCardAddress,
                      number: '16A',
                      postalCode: '35501-240',
                      onTap: (isChecked) {
                        setState(() {
                          isCardAddress = !isCardAddress;
                        });
                      },
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCardAddress(
                      isStore: true,
                      title: 'Hering Rio Sul',
                      city: 'Curitiba',
                      district: 'Parana',
                      distance: '8km',
                      complement: 'Apto: 4',
                      neighborhood: 'Demetrios',
                      streetName: 'Av. Jorge Tench',
                      shipping: 'Retire hoje de 8h às 18h',
                      voucher: '+5% OFF',
                      isChecked: isCardAddress,
                      number: '25A',
                      postalCode: '35501-240',
                      onTap: (isChecked) {
                        setState(() {
                          isCardAddress = !isCardAddress;
                        });
                      },
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const SizedBox(height: 24),
                    AzzasTabBar(
                      tabSize: TabSizeEnum.large,
                      height: 80.0,
                      tabs: [
                        AzzasTabItem(
                          text: 'Tab 1',
                          leading: Icon(
                            Tokens.icons.shop.shoppingItem,
                          ),
                        ),
                        AzzasTabItem(
                          text: 'Tab 2',
                          leading: Icon(
                            Tokens.icons.shop.gift,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasPrimaryButton(
                      expanded: true,
                      size: ButtonSize.medium,
                      child: const Text('Label'),
                      onPressed: () {},
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasPrimaryButton(
                      size: ButtonSize.medium,
                      expanded: true,
                      isDisabled: true,
                      child: const Text('Label'),
                      onPressed: () {},
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasSecondaryButton(
                      size: ButtonSize.medium,
                      expanded: true,
                      child: const Text('Label'),
                      onPressed: () {},
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasDangerButton(
                      size: ButtonSize.medium,
                      expanded: true,
                      child: const Text('Label'),
                      onPressed: () {},
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    AzzasLinkButton(
                      size: ButtonSize.medium,
                      leading: const Icon(Icons.arrow_back_ios),
                      child: const Text(
                        'Label',
                      ),
                      onPressed: () {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        AzzasCardContent(
                          title: '32 litros',
                          subtitle: 'de economia de água',
                        ),
                        AzzasCardContent(
                          title: '64 gramas',
                          subtitle: 'de C02 evitado',
                        ),
                        AzzasCardContent(
                          title: '03 peças',
                          subtitle: 'reciclados na produção',
                        ),
                        AzzasCardContent(
                          title: '23 pessoas',
                          subtitle: 'envolvidos no processo',
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutInstallments<String>(
                      value: 'A',
                      groupValue: installmentOption,
                      onChanged: (value) {
                        setState(() {
                          if (value != null) {
                            installmentOption = value;
                          }
                        });
                      },
                      title: '1x de R\$ 1.500',
                      subtitle: 'Valor á vista',
                    ),
                    Divider(color: AppTokens.color5, height: 0.5),
                    AzzasCheckoutInstallments<String>(
                      value: 'B',
                      groupValue: installmentOption,
                      onChanged: (value) {
                        setState(() {
                          if (value != null) {
                            installmentOption = value;
                          }
                        });
                      },
                      title: '2x de R\$ 750',
                      subtitle: 'Sem juros',
                    ),
                    Divider(color: AppTokens.color5, height: 0.5),
                    AzzasCheckoutInstallments<String>(
                      value: 'C',
                      groupValue: installmentOption,
                      onChanged: (value) {
                        setState(() {
                          if (value != null) {
                            installmentOption = value;
                          }
                        });
                      },
                      title: '3x de R\$ 475',
                      subtitle: 'Sem juros',
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Divider(color: AppTokens.color5, height: 0.5),
                    AzzasCheckoutResume(
                        title: '1 Produtos',
                        totalAmount: 'R\$ 1425.00',
                        deliveryTitle: 'Entrega',
                        deliveryType: 'Entrega gratuita',
                        discountTitle: 'Desconto',
                        discountValue: 'R\$ -50.00',
                        submitLabel: 'Comprar',
                        onButtonPressed: () {},
                        titleCupom: 'Cupom aplicado',
                        cupomValue: 'R\$ -10.00',
                        totalDesc: '1x de R\$ 800,00',
                        totalTitle: 'Total'),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasControllerCarrousel(),
                    const SizedBox(
                      height: 8,
                    ),
                    const AzzasControllerCarrousel(
                      size: AzzasControllerCarrouselSize.medium,
                      itemsCount: 3,
                      activeItemIndex: 2,
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasSpotProduct(
                      product: const AzzasSpotProductParams(
                        image: imageUrl,
                        productTitle: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                      ),
                      onTap: () {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasSpotProduct(
                      product: const AzzasSpotProductParams(
                        image: imageUrl,
                        productTitle: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                      ),
                      onTap: () {},
                      hasPlusButton: true,
                      plusButtonParams: AzzasPlusButtonParams(onTap: () {}),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasSpotProduct(
                      product: const AzzasSpotProductParams(
                        image: imageUrl,
                        productTitle: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$ 1.200,00",
                        fullPrice: "R\$ 1.500,00",
                      ),
                      onTap: () {
                        final scaffold = ScaffoldMessenger.of(context);
                        scaffold.showSnackBar(
                          SnackBar(
                            content: const Text('Teste de onTap'),
                            action: SnackBarAction(
                              label: 'fechar',
                              onPressed: scaffold.hideCurrentSnackBar,
                            ),
                          ),
                        );
                      },
                    ),
                    AzzasSpotProduct(
                      spotDimension: SpotDimension.xsmall,
                      imageHeight: 156.0,
                      imageWidth: 104.0,
                      fullPriceTextStyle: Tokens
                          .typography.body.extraSmall.extraSmallCaption
                          .copyWith(
                        decoration: TextDecoration.lineThrough,
                        color: Tokens.colors.typography.medium,
                      ),
                      currentPriceTextStyle: Tokens
                          .typography.body.extraSmall.extraSmallCaption
                          .copyWith(
                        color: Tokens.colors.typography.light,
                      ),
                      productInfoPadding: const EdgeInsets.only(
                        top: 8.0,
                      ),
                      product: const AzzasSpotProductParams(
                        image: imageUrl,
                        productTitle: "Jaqueta Reyna - Amarelo",
                        currentPrice: "R\$1.200",
                        fullPrice: "R\$1.500",
                      ),
                      onTap: () {},
                      hasPlusButton: true,
                      plusButtonParams: AzzasPlusButtonParams(onTap: () {}),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasSpotProduct(
                      centerInfo: true,
                      imageBorderRadius: BorderRadius.circular(10.0),
                      spotDimension: SpotDimension.xsmall,
                      imageHeight: 156.0,
                      imageWidth: 104.0,
                      currentPriceTextStyle: Tokens
                          .typography.body.extraSmall.extraSmallCaption
                          .copyWith(
                        color: Tokens.colors.typography.medium,
                      ),
                      productInfoPadding: const EdgeInsets.only(
                        top: 8.0,
                      ),
                      product: const AzzasSpotProductParams(
                        image: imageUrl,
                        productTitle: "Jaqueta",
                        currentPrice: "R\$1.200",
                      ),
                      onTap: () {},
                      hasPlusButton: true,
                      plusButtonParams: AzzasPlusButtonParams(onTap: () {
                        final scaffold = ScaffoldMessenger.of(context);
                        scaffold.showSnackBar(
                          SnackBar(
                            content:
                                const Text('Teste de onTap do plus button'),
                            action: SnackBarAction(
                              label: 'fechar',
                              onPressed: scaffold.hideCurrentSnackBar,
                            ),
                          ),
                        );
                      }),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasAccordionMenu(
                      title: 'Minha conta',
                      leadingWidget: Icon(Tokens.icons.communication.sac),
                      content: Text(
                        '[O serviço/processo] funciona de maneira '
                        'simples. Basta seguir as instruções fornecidas '
                        'e garantir que todas as etapas sejam concluídas '
                        'corretamente. Caso tenha dúvidas ou precise '
                        'de assistência, consulte nossos materiais de'
                        'suporte ou entre em contato com o suporte'
                        ' técnico.',
                        style:
                            Tokens.typography.body.small.smallRegular.copyWith(
                          color: Tokens.colors.typography.medium,
                        ),
                      ),
                      expanded: expanded,
                      onTap: () {
                        setState(() {
                          expanded = !expanded;
                        });
                      },
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasPicker(
                      options: const [
                        AzzasPickerData(
                          title: 'M',
                          description: 'Avise - me',
                        ),
                        AzzasPickerData(
                          title: 'G',
                          description: 'Restam apenas 2!',
                        ),
                        AzzasPickerData(
                          title: 'GG',
                          description: 'Restam apenas 1!',
                        ),
                      ],
                      onSelectedItemChanged: (item) {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasPrimaryButton(
                      child: const Text('Snack bar'),
                      onPressed: () {
                        AzzasSnackBar.show(
                          context: context,
                          size: SnackBarSize.medium,
                          message: "Código reenviado por e-mail",
                          leading: Icon(
                            Tokens.icons.feedback.success,
                            size: 24,
                            color: Colors.white,
                          ),
                          trailing: InkWell(
                            onTap: () {},
                            child: const Text(
                              'Label',
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          status: SnackBarStatus.success,
                          position: SnackBarPosition.bottom,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    AzzasPrimaryButton(
                      child: const Text('Snack bar erro'),
                      onPressed: () {
                        AzzasSnackBar.show(
                          context: context,
                          message:
                              "Algo deu errado e infelizmente não conseguimos salvar seus dados",
                          status: SnackBarStatus.danger,
                          position: SnackBarPosition.bottom,
                          backgroundColor: Tokens.colors.error.pure,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    AzzasPrimaryButton(
                      child: const Text('Abrir Bottom Sheet'),
                      onPressed: () {
                        showAzzasBottomSheet(
                          context: context,
                          vsync: this,
                          builder: (context) {
                            return AzzasBottomSheet(
                              heightFactor: 0.93,
                              onBackTap: () {
                                Navigator.pop(context);
                              },
                              onCloseTap: () {
                                Navigator.pop(context);
                              },
                              scrollController: ScrollController(),
                              content: const Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AzzasTitleSection(text: "Componentes"),
                                  AzzasSubtitleSection(
                                    text:
                                        "Lista de componentes desenvolvidos no azzas_ui.",
                                  ),
                                  SizedBox(height: 24),
                                ],
                              ),
                              scrollContent: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AzzasCheckoutProductCard(
                                    product: AzzasProductCardProductParams(
                                      productId: '1',
                                      name: "Jaqueta Rayne - Amarelo",
                                      currentPrice: "R\$ 1.200,00",
                                      fullPrice: "R\$ 1.500,00",
                                      size: "P",
                                      quantity: 1,
                                      availability: "available",
                                      imageUrl:
                                          "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100",
                                      tagsBuilder: () {
                                        return Padding(
                                          padding: EdgeInsets.symmetric(
                                            vertical:
                                                Tokens.spacing.spacingXXSmall,
                                          ),
                                          child: AzzasTag(
                                            label: "Sem estoque",
                                            size: AzzasTagSize.xsmall,
                                            type: AzzasTagType.danger,
                                            outline: false,
                                            textStyle: Tokens.typography.body
                                                .extraSmall.extraSmallRegular,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  const AzzasCheckoutProductCard(
                                    product: AzzasProductCardProductParams(
                                      productId: '1',
                                      estimatedDelivery: "Receba até xx/xx",
                                      name: "Jaqueta Rayne - Amarelo",
                                      currentPrice: "R\$ 1.200,00",
                                      fullPrice: "R\$ 1.500,00",
                                      size: "P",
                                      quantity: 1,
                                      availability: "available",
                                      imageUrl:
                                          "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100",
                                    ),
                                  ),
                                  AzzasWishlistProductCard(
                                    tagsAlignment: AzzasProductInfoTagsAlignment
                                        .aboveProductTitle,
                                    product: AzzasProductCardProductParams(
                                      productId: '1',
                                      name: "Jaqueta Rayne - Amarelo",
                                      currentPrice: "R\$ 1.200,00",
                                      fullPrice: "R\$ 1.500,00",
                                      size: "P",
                                      quantity: 1,
                                      imageUrl:
                                          "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100",
                                      tagsBuilder: () {
                                        return Padding(
                                          padding: EdgeInsets.symmetric(
                                            vertical:
                                                Tokens.spacing.spacingXXSmall,
                                          ),
                                          child: AzzasTag(
                                            label: "Sem estoque",
                                            size: AzzasTagSize.xsmall,
                                            type: AzzasTagType.danger,
                                            outline: false,
                                            textStyle: Tokens.typography.body
                                                .extraSmall.extraSmallRegular,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  AzzasCheckoutProductCard(
                                    product: AzzasProductCardProductParams(
                                      productId: '1',
                                      name: "Jaqueta Rayne - Amarelo",
                                      currentPrice: "R\$ 1.200,00",
                                      fullPrice: "R\$ 1.500,00",
                                      size: "P",
                                      quantity: 1,
                                      availability: "available",
                                      imageUrl:
                                          "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100",
                                      tagsBuilder: () {
                                        return Padding(
                                          padding: EdgeInsets.symmetric(
                                            vertical:
                                                Tokens.spacing.spacingXXSmall,
                                          ),
                                          child: AzzasTag(
                                            label: "Sem estoque",
                                            size: AzzasTagSize.xsmall,
                                            type: AzzasTagType.danger,
                                            outline: false,
                                            textStyle: Tokens.typography.body
                                                .extraSmall.extraSmallRegular,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(
                      height: 24.0,
                    ),
                    AzzasCheckoutStepAddress(
                      addressParams: AzzasCheckoutStepAddressOpenParams(
                          formKey: _formKey,
                          cepInputParams: StepAddressInputParams(
                            controller: _cepInputController,
                          ),
                          responsibleNameInputParams: StepAddressInputParams(
                            controller: _responsibleNameInputController,
                          ),
                          streetNameInputParams: StepAddressInputParams(
                            controller: _streetInputController,
                          ),
                          numberInputParams: StepAddressInputParams(
                            controller: _numberInputController,
                          ),
                          complementInputParams: StepAddressInputParams(
                            controller: _complementInputController,
                          ),
                          neighborhoodInputParams: StepAddressInputParams(
                            controller: _neighborhoodInputController,
                          ),
                          onTapUseLocation: () {
                            showTestToast(context);
                          },
                          onTapFinishButton: () {
                            if (_formKey.currentState?.validate() == true) {
                              showTestToast(context,
                                  message: "Endereço salvo!");
                              return;
                            }
                            showTestToast(context,
                                message: "Endereço inválido!");
                          }),
                    ),
                    const SizedBox(
                      height: 16.0,
                    ),
                    AzzasCheckoutStepAddress(
                      addressParams: AzzasCheckoutStepAddressFilledParams(
                        complement: "apto 1210, bloco 1",
                        neighborhood: "Barra da Tijuca",
                        streetName: "Av. das Américas",
                        number: "500",
                        postalCode: "22640-100",
                        responsibleName: "Fabricio",
                        labelCta: "Ver mais",
                        onTapCta: () {
                          showTestToast(context);
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 16.0,
                    ),
                    AzzasCheckoutStepAddress(
                      showHeader: false,
                      addressParams: AzzasCheckoutStepAddressFilledParams(
                        complement: "apto 1210, bloco 1",
                        neighborhood: "Barra da Tijuca",
                        streetName: "Av. das Américas",
                        number: "500",
                        postalCode: "22640-100",
                        responsibleName: "Fabricio",
                        labelCta: "Ver mais",
                        onTapCta: () {
                          showTestToast(context);
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 16.0,
                    ),
                    AzzasCheckoutStepAddress(
                      isDisabled: true,
                      addressParams: AzzasCheckoutStepAddressFilledParams(
                        complement: "apto 1210, bloco 1",
                        neighborhood: "Barra da Tijuca",
                        streetName: "Av. das Américas",
                        number: "500",
                        postalCode: "22640-100",
                        responsibleName: "Fabricio",
                        labelCta: "Ver mais",
                        onTapCta: () {
                          showTestToast(context);
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 16.0,
                    ),
                    AzzasCheckoutStepAddress(
                      isLoading: true,
                      addressParams: AzzasCheckoutStepAddressFilledParams(
                        complement: "apto 1210, bloco 1",
                        neighborhood: "Barra da Tijuca",
                        streetName: "Av. das Américas",
                        number: "500",
                        postalCode: "22640-100",
                        responsibleName: "Fabricio",
                        labelCta: "Ver mais",
                        onTapCta: () {
                          showTestToast(context);
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    AzzasPrimaryButton(
                      child: const Text('Product Added Bottom Sheet'),
                      onPressed: () {
                        showAzzasBottomSheet(
                          context: context,
                          builder: (context) {
                            return AzzasProductAddedBottomSheet(
                              onCloseTap: () => Navigator.pop(context),
                              title: 'Produto adicionado à sacola',
                              titleStyle:
                                  Tokens.typography.headings.small.smallRegular,
                              productCard: AzzasCheckoutProductCard(
                                product: AzzasProductCardProductParams(
                                  productId: '1',
                                  name: "Jaqueta Rayne - Amarelo",
                                  currentPrice: "R\$ 1.200,00",
                                  fullPrice: "R\$ 1.500,00",
                                  size: "P",
                                  quantity: 1,
                                  availability: "available",
                                  imageUrl:
                                      "https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100",
                                  tagsBuilder: () {
                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                        vertical: Tokens.spacing.spacingXXSmall,
                                      ),
                                      child: AzzasTag(
                                        label: "Sem estoque",
                                        size: AzzasTagSize.xsmall,
                                        type: AzzasTagType.danger,
                                        outline: false,
                                        textStyle: Tokens.typography.body
                                            .extraSmall.extraSmallRegular,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              buttonText: 'Ver sacola',
                              onButtonTap: () => Navigator.pop(context),
                            );
                          },
                          vsync: this,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    AzzasPrimaryButton(
                      child: const Text('Sku selector bottom sheet'),
                      onPressed: () {
                        showAzzasBottomSheet(
                          context: context,
                          builder: (context) {
                            return AzzasSkuSelectionBottomSheet(
                              onCloseTap: () => Navigator.pop(context),
                              title: 'Selecione o seu tamanho',
                              titleStyle:
                                  Tokens.typography.headings.small.smallRegular,
                              content: AzzasPicker(
                                options: const [
                                  AzzasPickerData(
                                    title: 'M',
                                    description: 'Avise - me',
                                  ),
                                  AzzasPickerData(
                                    title: 'G',
                                    description: 'Restam apenas 2!',
                                  ),
                                  AzzasPickerData(
                                    title: 'GG',
                                    description: 'Restam apenas 1!',
                                  ),
                                ],
                                onSelectedItemChanged: (item) {},
                              ),
                              action: AzzasSecondaryButton(
                                onPressed: () => Navigator.pop(context),
                                expanded: true,
                                child: const Text('Avise - me'),
                              ),
                            );
                          },
                          vsync: this,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    AzzasPrimaryButton(
                      child: const Text('Sku selector bottom sheet'),
                      onPressed: () {
                        showAzzasBottomSheet(
                          context: context,
                          builder: (context) {
                            return AzzasSouldOutBottomSheet(
                              onCloseTap: () => Navigator.pop(context),
                              title:
                                  'Pronto, pode deixar que a gente te avisa!',
                              description:
                                  'Fique de olho no seu email, caso o seu '
                                  'desejo estiver disponível novamente '
                                  'mandaremos o aviso por lá, ok?',
                              descriptionStyle: TextStyle(
                                color: AppTokens.color1,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              titleStyle:
                                  Tokens.typography.headings.small.smallRegular,
                              content: AzzasPrimaryButton(
                                expanded: true,
                                child: const Text('Fechar'),
                                onPressed: () => Navigator.pop(context),
                              ),
                            );
                          },
                          vsync: this,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    AzzasSlider(
                      initialValue: 0.0,
                      minValue: 0.0,
                      maxValue: 100.0,
                      onChanged: (value, _) {},
                      currentValues: const RangeValues(0, 100),
                      leftSupportingText: "text support",
                      rightSupportingText: "text support",
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.black,
                      ),
                      child: AzzasSlider(
                        initialValue: 0.0,
                        minValue: 0.0,
                        isRangeSlider: true,
                        maxValue: 100.0,
                        onChanged: (value, _) {},
                        mode: AzzasSliderMode.light,
                        isDisabled: false,
                        currentValues: const RangeValues(20, 50),
                      ),
                    ),
                    const SizedBox(height: 24),
                    AzzasOrderStatus(
                      orders: [
                        OrderStatusItem(
                          title: 'Pedido confirmado',
                          subtitle: '11 de janeiro de 2020 às 14:23h',
                          active: true,
                        ),
                        OrderStatusItem(
                          title: 'Pagamento aprovado',
                          subtitle: '11 de janeiro de 2020 às 14:23h',
                          active: true,
                        ),
                        OrderStatusItem(
                          title: 'Saiu para entrega',
                          active: false,
                        ),
                        OrderStatusItem(
                          title: 'Entregue',
                          active: false,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24.0),
                    AzzasCheckoutStepShipping(
                      shippingParams: AzzasCheckoutStepShippingFilledParams(
                        shippingType: "Entrega Econômica",
                        shippingPrice: "R\$ 8,00",
                        shippingEstimatedTime: "Chega até segunda, 17/05",
                        labelCta: "Label",
                        onTapCta: () {
                          showTestToast(context,
                              message: "CheckoutStepShipping clicado!");
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 24.0,
                    ),
                    AzzasCheckoutStepShipping(
                      shippingParams: AzzasCheckoutStepShippingOpenParams(
                        tabBarHeight: 100.0,
                        commonDeliveryParams: AzzasCommonDeliveryParams(
                            showDeliveryTitle: true,
                            cardTitle: "Aproveite 50% OFF na Entrega Expressa",
                            deliveries: [
                              AzzasDelivery(
                                options: [
                                  AzzasCommonDeliveryOption(
                                    id: "1",
                                    deliveryPrice: "R\$ 8",
                                    deliveryTitle: "Econômica",
                                    deliveryTime: "receba até amanhã às 8h",
                                    isSelected:
                                        selectedDeliveryOption?.id == "1",
                                  ),
                                  AzzasCommonDeliveryOption(
                                    id: "2",
                                    deliveryPrice: "R\$ 18",
                                    deliveryTitle: "Expressa",
                                    deliveryTime: "receba hoje até 8h",
                                    isSelected:
                                        selectedDeliveryOption?.id == "2",
                                  ),
                                ],
                              ),
                            ],
                            onCommonOptionSelected: (option) {
                              setState(() {
                                selectedDeliveryOption = option;
                              });
                            }),
                        pickupDeliveryParams: AzzasPickupDeliveryParams(
                          cardTitle: "",
                          pickupOptions: [],
                          onPickupOptionSelected: (option) {
                            setState(() {
                              selectedDeliveryOption = option;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    AzzasCheckoutStepShipping(
                      shippingParams: AzzasCheckoutStepShippingOpenParams(
                        tabBarHeight: 480.0,
                        commonDeliveryParams: AzzasCommonDeliveryParams(
                            showDeliveryTitle: true,
                            cardTitle: "Aproveite 50% OFF na Entrega Expressa",
                            deliveries: [
                              AzzasDelivery(
                                productImages: [
                                  imageUrl,
                                  imageUrl,
                                  imageUrl,
                                ],
                                options: [
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 8",
                                    deliveryTitle: "Econômica",
                                    deliveryTime: "receba até amanhã às 8h",
                                    id: "3",
                                    isSelected:
                                        selectedDeliveryOption?.id == "3",
                                  ),
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 18",
                                    deliveryTitle: "Expressa",
                                    deliveryTime: "receba hoje até 8h",
                                    id: "4",
                                    isSelected:
                                        selectedDeliveryOption?.id == "4",
                                  ),
                                ],
                              ),
                            ],
                            onCommonOptionSelected: (option) {
                              setState(() {
                                selectedDeliveryOption = option;
                              });
                            }),
                        pickupDeliveryParams: AzzasPickupDeliveryParams(
                          cardTitle: "Aproveite 50% OFF na Entrega Expressa",
                          pickupOptions: [
                            AzzasPickupDeliveryOption(
                              storeName: "Hering Rio Sul",
                              streetName: "Av. Lauro Sodré",
                              number: "445",
                              neighborhood: "Botafogo",
                              complement: "loja 201",
                              storeDistance: "8km",
                              pickupTime: "Retire hoje de 8h às 18h",
                              voucher: "5% OFF",
                              id: "5",
                              isSelected: selectedDeliveryOption?.id == "5",
                            ),
                            AzzasPickupDeliveryOption(
                              storeName: "Hering Rio Sul",
                              streetName: "Av. Lauro Sodré",
                              number: "445",
                              neighborhood: "Botafogo",
                              complement: "loja 201",
                              storeDistance: "24km",
                              pickupTime: "Retire hoje das 9h até as 20h",
                              id: "6",
                              isSelected: selectedDeliveryOption?.id == "6",
                            ),
                          ],
                          onPickupOptionSelected: (option) {
                            setState(() {
                              selectedDeliveryOption = option;
                            });
                          },
                        ),
                      ),
                    ),
                    AzzasCheckoutStepShipping(
                      shippingParams: AzzasCheckoutStepShippingOpenParams(
                        commonDeliveryParams: AzzasCommonDeliveryParams(
                            showDeliveryTitle: true,
                            cardTitle: "Aproveite 50% OFF na Entrega Expressa",
                            deliveries: [
                              AzzasDelivery(
                                title: "Entrega 1: até amanhã às 18h",
                                productImages: [
                                  imageUrl,
                                  imageUrl,
                                  imageUrl,
                                ],
                                options: [
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 8",
                                    deliveryTitle: "Econômica",
                                    deliveryTime: "receba até amanhã às 8h",
                                    id: "3",
                                    isSelected:
                                        selectedDeliveryOption?.id == "3",
                                  ),
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 18",
                                    deliveryTitle: "Expressa",
                                    deliveryTime: "receba hoje até 8h",
                                    id: "4",
                                    isSelected:
                                        selectedDeliveryOption?.id == "4",
                                  ),
                                ],
                              ),
                              AzzasDelivery(
                                title: "Entrega 2: até 17/03 às 18h",
                                productImages: [
                                  imageUrl,
                                  imageUrl,
                                  imageUrl,
                                ],
                                options: [
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 8",
                                    deliveryTitle: "Econômica",
                                    deliveryTime: "receba até amanhã às 8h",
                                    id: "3",
                                    isSelected:
                                        selectedDeliveryOption?.id == "3",
                                  ),
                                  AzzasCommonDeliveryOption(
                                    deliveryPrice: "R\$ 18",
                                    deliveryTitle: "Expressa",
                                    deliveryTime: "receba hoje até 8h",
                                    id: "4",
                                    isSelected:
                                        selectedDeliveryOption?.id == "4",
                                  ),
                                ],
                              ),
                            ],
                            onCommonOptionSelected: (option) {
                              setState(() {
                                selectedDeliveryOption = option;
                              });
                            }),
                        pickupDeliveryParams: AzzasPickupDeliveryParams(
                          cardTitle: "Aproveite 50% OFF na Entrega Expressa",
                          pickupOptions: [
                            AzzasPickupDeliveryOption(
                              storeName: "Hering Rio Sul",
                              streetName: "Av. Lauro Sodré",
                              number: "445",
                              neighborhood: "Botafogo",
                              complement: "loja 201",
                              storeDistance: "8km",
                              pickupTime: "Retire hoje de 8h às 18h",
                              voucher: "5% OFF",
                              id: "5",
                              isSelected: selectedDeliveryOption?.id == "5",
                            ),
                            AzzasPickupDeliveryOption(
                              storeName: "Hering Rio Sul",
                              streetName: "Av. Lauro Sodré",
                              number: "445",
                              neighborhood: "Botafogo",
                              complement: "loja 201",
                              storeDistance: "24km",
                              pickupTime: "Retire hoje das 9h até as 20h",
                              id: "6",
                              isSelected: selectedDeliveryOption?.id == "6",
                            ),
                          ],
                          onPickupOptionSelected: (option) {
                            setState(() {
                              selectedDeliveryOption = option;
                            });
                          },
                        ),
                      ),
                    ),
                    const AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: true,
                        creditCardInfo: CreditCardInfo(
                          lastDigits: "3321",
                          creditCardType: AzzasCreditCardType.visa,
                          installmentsLabel: "2x de R\$ 750",
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: true,
                        pixInfo: PixInfo(
                          paymentValue: "R\$ 800",
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: true,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: true,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalCreditCard: CreditCardInfo(
                            lastDigits: "9087",
                            creditCardType: AzzasCreditCardType.visa,
                            installmentsLabel: "2x de R\$ 750",
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: true,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalPix: PixInfo(
                            paymentValue: "R\$ 800",
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: false,
                        creditCardInfo: CreditCardInfo(
                          lastDigits: "3321",
                          creditCardType: AzzasCreditCardType.visa,
                          installmentsLabel: "2x de R\$ 750",
                          changePaymentMethodLabel: "Label",
                          onPaymentMethodChange: () {
                            showTestToast(context,
                                message: "Método de pagamento alterado!");
                          },
                          onAddVoucher: () {
                            showTestToast(context, message: "Vale adicionado!");
                          },
                          onInstallmentsChange: () {
                            showTestToast(context,
                                message: "Parcelamento alterado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: false,
                        pixInfo: PixInfo(
                          paymentValue: 'R\$ 800',
                          onInstallmentsChange: () {
                            showTestToast(context,
                                message: "Parcelamento alterado!");
                          },
                          onPaymentMethodChange: () {
                            showTestToast(context,
                                message: "Método de pagamento alterado!");
                          },
                          changePaymentMethodLabel: "Label",
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalCreditCard: const CreditCardInfo(
                            lastDigits: "9087",
                            creditCardType: AzzasCreditCardType.visa,
                            installmentsLabel: "2x de R\$ 750",
                          ),
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalPix: PixInfo(
                            paymentValue: 'R\$ 800',
                            changePaymentMethodLabel: "Label",
                            onInstallmentsChange: () {
                              showTestToast(context,
                                  message: "Parcelamento alterado!");
                            },
                            onPaymentMethodChange: () {
                              showTestToast(context,
                                  message: "Método de pagamento alterado!");
                            },
                          ),
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        hasError: true,
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        hasError: true,
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalCreditCard: CreditCardInfo(
                            lastDigits: "9087",
                            creditCardType: AzzasCreditCardType.visa,
                            installmentsLabel: "2x de R\$ 750",
                            onAddVoucher: () {
                              showTestToast(context,
                                  message: "Vale adicionado!");
                            },
                            onInstallmentsChange: () {
                              showTestToast(context,
                                  message: "Parcelamento alterado!");
                            },
                            onPaymentMethodChange: () {
                              showTestToast(context,
                                  message: "Método de pagamento alterado!");
                            },
                          ),
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        hasError: true,
                        isOrderCompleted: false,
                        voucherInfo: VoucherInfo(
                          voucherCode: "Go Live",
                          voucherValue: "R\$ 200,00",
                          voucherName: "Vale Hering",
                          additionalPix: PixInfo(
                            paymentValue: 'R\$ 800',
                            changePaymentMethodLabel: "Label",
                            onInstallmentsChange: () {
                              showTestToast(context,
                                  message: "Parcelamento alterado!");
                            },
                          ),
                          onUsePartOfValue: () {
                            showTestToast(context,
                                message: "Parte do vale utilizado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        hasError: true,
                        isOrderCompleted: false,
                        hasAvailableVoucher: true,
                        onAddVoucher: () {
                          showTestToast(context, message: "Vale adicionado!");
                        },
                        pixInfo: PixInfo(
                          paymentValue: 'R\$ 800',
                          onInstallmentsChange: () {
                            showTestToast(context,
                                message: "Parcelamento alterado!");
                          },
                          onPaymentMethodChange: () {
                            showTestToast(context,
                                message: "Método de pagamento alterado!");
                          },
                          changePaymentMethodLabel: "Label",
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentFilledParams(
                        hasError: true,
                        isOrderCompleted: false,
                        hasAvailableVoucher: true,
                        onAddVoucher: () {
                          showTestToast(context, message: "Vale adicionado!");
                        },
                        creditCardInfo: CreditCardInfo(
                          lastDigits: "3321",
                          creditCardType: AzzasCreditCardType.mastercard,
                          installmentsLabel: "2x de R\$ 750",
                          onPaymentMethodChange: () {
                            showTestToast(context,
                                message: "Método de pagamento alterado!");
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentOpenParams(
                        ctaLabel: "Usar este cartão",
                        onTapCta: () {
                          showTestToast(context, message: "CTA clicado!");
                        },
                        totalValue: "R\$ 1.500,00",
                        onPaymentMethodSelected: (_) {
                          showTestToast(context,
                              message: "Método de pagamento selecionado!");
                        },
                        hasAvailableVoucher: true,
                        onAddVoucher: () =>
                            showTestToast(context, message: "Vale adicionado!"),
                        availablePaymentMethods: [
                          const CreditCardAvailablePaymentMethod(
                            creditCardInfo: CreditCardInfo(
                              lastDigits: "3321",
                              creditCardType: AzzasCreditCardType.mastercard,
                              installmentsLabel:
                                  'até 2x de R\$ 750,50 sem júros',
                            ),
                            isSelected: true,
                          ),
                          const CreditCardAvailablePaymentMethod(
                            creditCardInfo: CreditCardInfo(
                              lastDigits: "3321",
                              creditCardType: AzzasCreditCardType.visa,
                              installmentsLabel:
                                  'até 2x de R\$ 750,50 sem júros',
                            ),
                            isSelected: false,
                          ),
                          const PixAvailablePaymentMethod(
                            isSelected: false,
                          ),
                          const NewCreditCardAvailablePaymentMethod(
                            isSelected: false,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasCheckoutStepPayment(
                      paymentParams: AzzasCheckoutStepPaymentOpenParams(
                        hasError: true,
                        ctaLabel: "Usar este cartão",
                        onTapCta: () {
                          showTestToast(context, message: "CTA clicado!");
                        },
                        totalValue: "R\$ 1.500,00",
                        onPaymentMethodSelected: (_) {
                          showTestToast(context,
                              message: "Método de pagamento selecionado!");
                        },
                        hasAvailableVoucher: true,
                        onAddVoucher: () =>
                            showTestToast(context, message: "Vale adicionado!"),
                        availablePaymentMethods: [
                          const CreditCardAvailablePaymentMethod(
                            creditCardInfo: CreditCardInfo(
                              lastDigits: "3321",
                              creditCardType: AzzasCreditCardType.mastercard,
                              installmentsLabel:
                                  'até 2x de R\$ 750,50 sem júros',
                            ),
                            isSelected: true,
                          ),
                          const CreditCardAvailablePaymentMethod(
                            creditCardInfo: CreditCardInfo(
                              lastDigits: "3321",
                              creditCardType: AzzasCreditCardType.visa,
                              installmentsLabel:
                                  'até 2x de R\$ 750,50 sem júros',
                            ),
                            isSelected: false,
                          ),
                          const PixAvailablePaymentMethod(
                            isSelected: false,
                          ),
                          const NewCreditCardAvailablePaymentMethod(
                            isSelected: false,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasListItemDefault(
                      onTap: () {},
                      title: 'Digite o título aqui',
                    ),
                    const SizedBox(height: 16),
                    AzzasListItemDefault(
                      onTap: () {},
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                    ),
                    const SizedBox(height: 16),
                    AzzasListItemDefault(
                      onTap: () {},
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                      tag: const ListItemTag(text: '+5% OFF'),
                    ),
                    AzzasListItemDefault(
                      onTap: () {},
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                      divider: true,
                    ),
                    AzzasListItemIcon(
                      onTap: () {},
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      divider: true,
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                    ),
                    AzzasListItemToggle(
                      onTap: () {
                        setState(() {
                          toggleActive = !toggleActive;
                        });
                      },
                      isChecked: toggleActive,
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                      divider: true,
                    ),
                    AzzasListItemRadio(
                      isChecked: radioActive,
                      onTap: () {
                        setState(() {
                          radioActive = !radioActive;
                        });
                      },
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      divider: true,
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                    ),
                    AzzasListItemCheckbox(
                      isChecked: checkboxActive,
                      onTap: () {
                        setState(() {
                          checkboxActive = !checkboxActive;
                        });
                      },
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      divider: true,
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                    ),
                    AzzasListItemButton(
                      buttonText: 'Label',
                      onTap: () {},
                      leftIcon: Icon(
                        Tokens.icons.shipping.home,
                        size: 24,
                      ),
                      divider: true,
                      title: 'Digite o título aqui',
                      subtitle: 'Digite o subtítulo aqui',
                      benefit: 'Digite o benefício aqui',
                    ),
                    const SizedBox(height: 24),
                    AzzasOrderCompleted(
                      title: 'Seu pedido foi concluído com sucesso!',
                      subtitle:
                          'Após a aprovação do pagamento pela instituição financeira, você receberá a confirmação do pedido no e-mail:',
                      email: '<EMAIL>',
                      imagePath: 'assets/images/order_completed.png',
                      buttonText: 'Acompanhar pedido',
                      onButtonTap: () {},
                      onShareTap: () {},
                    ),
                    const SizedBox(height: 24),
                    AzzasColorSelector(
                      colors: const [
                        ColorSelectorItem(
                          image: imageUrl,
                          key: 'A',
                        ),
                        ColorSelectorItem(
                          image: imageUrl,
                          key: 'B',
                        ),
                        ColorSelectorItem(
                          image: imageUrl,
                          key: 'C',
                        ),
                      ],
                      selectedColor: colorSelectorItem,
                      onSelected: (item) {
                        setState(
                          () {
                            colorSelectorItem = item;
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    AzzasOrderSummaryStatus(
                      onTap: () {},
                      title: 'v1087682anm',
                      orderDate: '11 jan',
                      amount: 'R\$ 1000,00',
                      tagLabel: 'Pagamento aprovado',
                      tagColor: Tokens.colors.brand.medium,
                      productImages: const [
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100'
                      ],
                    ),
                    const SizedBox(height: 16),
                    AzzasOrderSummaryStatus(
                      onTap: () {},
                      hasTrack: true,
                      onTrackTap: () {},
                      title: 'v1087682anm',
                      orderDate: '11 jan',
                      amount: 'R\$ 1000,00',
                      tagLabel: 'Saiu para entrega',
                      tagColor: Tokens.colors.brand.pure,
                      productImages: const [
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                      ],
                    ),
                    const SizedBox(height: 16),
                    AzzasCardOrder(
                      onTap: () {},
                      title: 'v1087682anm',
                      orderDate: '23 nov',
                      tagLabel: 'Pagamento aprovado',
                      amount: 'R\$ 1000,00',
                      productImages: const [
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                      ],
                      tagColor: Tokens.colors.brand.medium,
                    ),
                    const SizedBox(height: 16),
                    AzzasCardOrder(
                      onTap: () {},
                      title: 'v1087682anm',
                      orderDate: '23 nov',
                      tagLabel: 'Entregue',
                      amount: 'R\$ 1000,00',
                      productImages: const [
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                      ],
                      tagColor: Tokens.colors.brand.medium,
                    ),
                    const SizedBox(height: 16),
                    AzzasCardOrder(
                      onTap: () {},
                      title: 'v1087682anm',
                      orderDate: '23 nov',
                      tagLabel: 'Cancelado',
                      amount: 'R\$ 1000,00',
                      productImages: const [
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100',
                        'https://www.crisbarros.com.br/_next/image?url=https%3A%2F%2Flojacrisbarros.vtexassets.com%2Farquivos%2Fids%2F459151%2F35JQ256VC_00249_1-JAQUETA-RAYNE.jpg%3Fv%3D638131017985770000&w=3840&q=100'
                      ],
                      tagColor: Tokens.colors.error.pure,
                    ),
                    const SizedBox(height: 24),
                    const AzzasProductInformation(
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      ref: '306409_19804',
                      currentPrice: 'R\$ 1.599',
                      fullPrice: 'R\$ 1.599',
                      installments: '10x R\$ 32,90 sem juros',
                    ),
                    const SizedBox(height: 24),
                    AzzasProductInformation(
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      ref: '306409_19804',
                      currentPrice: 'R\$ 1.499',
                      fullPrice: 'R\$ 1.599',
                      installments: '10x R\$ 32,90 sem juros',
                      onShareTap: () {},
                    ),
                    const SizedBox(height: 24),
                    const AzzasProductBoxDefault(
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      currentPrice: 'R\$ 1.499',
                      fullPrice: 'R\$ 1.499',
                      installments: 'em até 12x',
                    ),
                    const SizedBox(height: 16),
                    const AzzasProductBoxShipping(
                      shippingTitle: 'Receba até xx/xx',
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      currentPrice: 'R\$ 1.499',
                      fullPrice: 'R\$ 1.599',
                      installments: 'em até 12x',
                    ),
                    const SizedBox(height: 16),
                    const AzzasProductBoxArrivesToday(
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      currentPrice: 'R\$ 1.499',
                      fullPrice: 'R\$ 1.599',
                      installments: 'em até 12x',
                      arrivesTitle: 'Chega hoje',
                    ),
                    const SizedBox(height: 16),
                    AzzasProductBoxButton(
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                      currentPrice: 'R\$ 1.499',
                      fullPrice: 'R\$ 1.599',
                      installments: 'em até 12x',
                      buttonText: 'Adicionar à sacola',
                      onTap: () {},
                    ),
                    const SizedBox(height: 16),
                    AzzasProductBoxUnnavailable(
                      onTap: () async {
                        await FavoritesPageEvents.logSelectWishlistOption(
                            local: 'components', action: 'salvar-para-depois');
                      },
                      removeButtonText: 'Remover',
                      buttonText: 'Salvar para depois',
                      title: 'Calça Feminina Cintura Alta Jogger - Branco',
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "pageManagement",
                      title: "Categoria",
                      filterOptions: const {
                        "Categoria": ["Roupas", "Calçados", "Acessórios"]
                      },
                      filterType: FilterTypeUi.categories,
                      selectedFilters: const ["Calçados"],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {
                        final isSelected = listSelectedSliderManagent
                            .contains(filterTypeSelected);

                        setState(() {
                          if (!isSelected) {
                            listSelectedSliderManagent.add(filterTypeSelected);
                          } else {
                            listSelectedSliderManagent
                                .remove(filterTypeSelected);
                          }
                        });
                      },
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "pageManagement",
                      title: "Tamanho",
                      filterOptions: const {
                        "Tamanho": ["PP", "P", "M", "G", "GG"],
                      },
                      filterType: FilterTypeUi.sizes,
                      selectedFilters: const ["PP", "G"],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "pageManagement",
                      title: "Tipo de produto",
                      filterOptions: const {
                        "Tipo de produto": [
                          "Macacão",
                          "Vestido",
                          "Blusa",
                          "Saia",
                          "Bolsas",
                          "Short",
                          "Calça",
                          "T-shirt",
                          "Top"
                        ],
                      },
                      filterType: FilterTypeUi.products,
                      selectedFilters: const [
                        "Blusa",
                        "Saia",
                        "Bolsas",
                      ],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "pageManagement",
                      title: "Preço",
                      filterType: FilterTypeUi.price,
                      leftSupportingText:
                          "R\$ ${minSliderManagent.toStringAsFixed(0)}",
                      rightSupportingText:
                          "R\$ ${maxSliderManagent.toStringAsFixed(0)}",
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                      sliderMin: minSliderManagent,
                      sliderMax: maxSliderManagent,
                      initialSliderMin: minSliderManagent,
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "bottomSheet",
                      title: "Categoria",
                      filterOptions: const {
                        "Categoria": ["Roupas", "Calçados", "Acessórios"]
                      },
                      filterType: FilterTypeUi.categories,
                      selectedFilters: const ["Calçados"],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {
                        final isSelected = listSelectedSliderManagent
                            .contains(filterTypeSelected);

                        setState(() {
                          if (!isSelected) {
                            listSelectedSliderManagent.add(filterTypeSelected);
                          } else {
                            listSelectedSliderManagent
                                .remove(filterTypeSelected);
                          }
                        });
                      },
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "bottomSheet",
                      title: "Tamanho",
                      filterOptions: const {
                        "Tamanho": ["PP", "P", "M", "G", "GG"],
                      },
                      filterType: FilterTypeUi.sizes,
                      selectedFilters: const ["PP", "G"],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "bottomSheet",
                      title: "Tipo de produto",
                      filterOptions: const {
                        "Tipo de produto": [
                          "Macacão",
                          "Vestido",
                          "Blusa",
                          "Saia",
                          "Bolsas",
                          "Short",
                          "Calça",
                          "T-shirt",
                          "Top"
                        ],
                      },
                      filterType: FilterTypeUi.products,
                      selectedFilters: const [
                        "Blusa",
                        "Saia",
                        "Bolsas",
                      ],
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    AzzasFilterManagement(
                      local: "bottomSheet",
                      title: "Preço",
                      filterType: FilterTypeUi.price,
                      leftSupportingText:
                          "R\$ ${minSliderManagent.toStringAsFixed(0)}",
                      rightSupportingText:
                          "R\$ ${maxSliderManagent.toStringAsFixed(0)}",
                      onFilterChanged:
                          (String filterTypeSelected, dynamic value) {},
                      sliderMin: minSliderManagent,
                      sliderMax: maxSliderManagent,
                      currentRangeValues:
                          RangeValues(minSliderManagent, maxSliderManagent),
                    ),
                    const SizedBox(height: 16),
                    AzzasOrderReviewProductCard(
                      product: const AzzasProductCardProductParams(
                        productId: '1',
                        imageUrl: imageUrl,
                        name: 'Calça Feminina Cintura Alta Jogger - Branco',
                        currentPrice: 'R\$ 199,00',
                        size: 'M',
                        quantity: 1,
                      ),
                      onTapImage: () {
                        Modular.to.pushNamed('/checkout/order_review');
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
