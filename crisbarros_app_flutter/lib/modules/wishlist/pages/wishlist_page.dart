import 'package:azzas_analytics/events/category/favorites_page_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_add_to_bag_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_sold_out_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/wishlist/pages/widgets/empty_wishlist.dart';
import 'package:flutter/material.dart';

class WishlistPage extends StatefulWidget {
  const WishlistPage({super.key});

  @override
  State<WishlistPage> createState() => _WishlistPageState();
}

class _WishlistPageState extends State<WishlistPage>
    with TickerProviderStateMixin {
  final wishlistCubit = WishlistPageCubit();
  final scrollController = ScrollController();
  late PdpCubit _pdpCubit;

  @override
  void initState() {
    super.initState();
    NavigationEvents.logPageView(local: 'wishlist');
    wishlistCubit.getProducts();
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent &&
          wishlistCubit.state.reachedLastPage == false &&
          wishlistCubit.state.isLoading == false) {
        wishlistCubit.getProducts(
            page: wishlistCubit.state.currentPage + 1, appendProducts: true);
      }
    });
  }

  void _navigateToBag() {
    CheckoutNavigationManager.goToBag(
      legacyCheckoutArgs: {
        'cameFromPdp': true,
      },
      newCheckoutArgs: CdsBackPackArgs(
        hasCloseButton: true,
        paymentArgs: CdsPaymentArgs(
          goToBackPack: (bool? showAddressCard) {
            Modular.to.popUntil(ModalRoute.withName('/'));
            final backPackCubit = Modular.get<BagPageCubit>();
            backPackCubit.setShowAddressCard(showAddressCard);
            MainPage.goBag();
          },
          onTapViewBalance: () {
            Modular.to.popUntil((route) {
              return route.settings.name == '/';
            });
            NavigatorDynamic.call('/perfil');
            Modular.to.pushNamed('/checking');
          },
        ),
      ),
    );
  }

  void getPdpCubit(product) {
    _pdpCubit = PdpCubit(
      getProductsByIdsUseCase: Modular.get<GetProductsByIdsUseCase>(),
      getSimilarProductsUseCase: Modular.get<GetSimilarProductsUseCase>(),
      warnMeUseCase: Modular.get<WarnMeUseCase>(),
      getSimilarProductsByRefIdUseCase:
          Modular.get<GetSimilarProductsByRefIdUseCase>(),
      simulateShippingOptionsUseCase:
          Modular.get<SimulateShippingOptionsUseCase>(),
    );
    _pdpCubit.fetchSelectedProduct(product);
    // // TODO: Refatorar componente de addToBag
    // // será preciso criar um cubit proprio de product
    // // que devera comportar as ações e bottom sheet
    // // add a sacolaa; seleção de tamanho; avise-me; fora de estoque
  }

  Future<void> _removeProduct({required Product product}) async {
    try {
      await wishlistCubit.removeProduct(product);
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Produto removido',
        );
        Navigator.of(context).pop();
      }
    } catch (_) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          isError: true,
          message: 'Erro ao remover produto',
        );
        Navigator.of(context).pop();
      }
    }
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    bool isError = false,
    required String message,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  void _showAzzasRemoveProductBottomSheet({required Product product}) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<WishlistPageCubit, WishlistPageState>(
          bloc: wishlistCubit,
          builder: (context, state) {
            return AzzasRemoveProductBottomSheet(
              onCancelTap: () => Navigator.of(context).pop(),
              isCancelButtonDisabled: state.isLoading,
              titleStyle: Tokens.typography.headings.small.smallRegular,
              isLoading: state.isLoading,
              onRemoveTap: () async {
                await _removeProduct(product: product);
                FavoritesPageEvents.logRemoveFromWishlist(
                  local: 'wishlist',
                  itemName: product.productName ?? '',
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void addToBag(Product product, OrderFormItem? selectedSize,
      PdpCubit _pdpCubit, int index) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return PdpAddToBagBottomSheet(
            item: product,
            controller: _pdpCubit,
            selectedSize: selectedSize,
            productIndex: index,
            navigateToBag: () {
              Navigator.of(context).pop();
              _navigateToBag();
            });
      },
      vsync: this,
    );
  }

  void showUnavailableProductBottomSheet(PdpCubit _pdpCubit) {
    showAzzasBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return PdpSoldOutBottomSheet(controller: _pdpCubit, skuId: '');
      },
      vsync: this,
    );
  }

  @override
  void dispose() {
    super.dispose();
    scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<WishlistPageCubit, WishlistPageState>(
            bloc: wishlistCubit,
            builder: (context, state) {
              if (!state.isLoading && state.products.isEmpty) {
                return const EmptyWishlist();
              }
              return SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 56),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: Tokens.spacing.spacingMedium,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Wishlist',
                                style: Tokens
                                    .typography.headings.small.smallRegular,
                                textScaler: TextScaleHelper.clampTextScale(
                                    context,
                                    maxScaleFactor: 2.0),
                              ),
                              Visibility(
                                visible: state.products.isNotEmpty,
                                child: Text(
                                  '${state.productsTotal} ${state.productsTotal > 1 ? 'produtos' : 'produto'}',
                                  textScaler: TextScaleHelper.clampTextScale(
                                      context,
                                      maxScaleFactor: 2.0),
                                  style: Tokens.typography.body.extraSmall
                                      .extraSmallRegular
                                      .copyWith(
                                    color: Tokens.colors.typography.medium,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: Tokens.spacing.spacingXLarge),
                        ListView.separated(
                          separatorBuilder: (_, index) {
                            return SizedBox(
                              height: Tokens.spacing.spacingXLarge,
                            );
                          },
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: state.products.length,
                          itemBuilder: (_, index) {
                            final product = state.products[index];
                            return AzzasSlidable(
                              endActionPane: CardSlideAction(
                                backgroundColor: Tokens.colors.error.pure,
                                foregroundColor: Tokens.colors.neutral.light,
                                onPressed: (_) async {
                                  await FavoritesPageEvents
                                      .logRemoveFromWishlist(
                                          local: 'wishlist',
                                          itemName: product.productName ?? '');
                                  _showAzzasRemoveProductBottomSheet(
                                    product: product,
                                  );
                                },
                                child: Icon(
                                  Tokens.icons.action.delete,
                                  size: 16.0,
                                ),
                              ),
                              slidableWidget: AzzasWishlistProductCard(
                                showPrice: product.isAvailable,
                                tagsAlignment: AzzasProductInfoTagsAlignment
                                    .aboveProductTitle,
                                onTapImage: () {
                                  Modular.to.pushNamed(
                                    '/pdp',
                                    arguments: product,
                                  );
                                },
                                onTapRemoveProduct: () {
                                  _showAzzasRemoveProductBottomSheet(
                                    product: product,
                                  );
                                },
                                onTapAddToBag: () async {
                                  getPdpCubit(product);
                                  if (product.isAvailable) {
                                    return addToBag(
                                        product,
                                        product.items?.firstOrNull,
                                        _pdpCubit,
                                        index);
                                  }

                                  return showUnavailableProductBottomSheet(
                                      _pdpCubit);
                                },
                                addToBagText: product.isAvailable
                                    ? 'Adicionar à sacola'
                                    : 'Avise-me',
                                complementarWidget: !product.isAvailable
                                    ? Padding(
                                        padding: EdgeInsets.symmetric(
                                          vertical:
                                              Tokens.spacing.spacingXXSmall,
                                        ),
                                        child: AzzasTag(
                                          label: "Sem estoque",
                                          size: AzzasTagSize.xsmall,
                                          type: AzzasTagType.danger,
                                          outline: false,
                                          textStyle: Tokens.typography.body
                                              .extraSmall.extraSmallRegular,
                                        ),
                                      )
                                    : null,
                                product: AzzasProductCardProductParams(
                                  productId: product.productId ?? '',
                                  imageUrl: product.images.first.imageUrl ?? '',
                                  name: product.productName ?? '',
                                  currentPrice: product.productFormattedPrice,
                                  availability: product.availability,
                                  size: '',
                                  tagsBuilder: () {
                                    // if (product.isLowStock) {
                                    //   return Padding(
                                    //     padding: EdgeInsets.symmetric(
                                    //         vertical:
                                    //             Tokens.spacing.spacingXXSmall),
                                    //     child: AzzasTag(
                                    //       label: "Pouco estoque",
                                    //       size: AzzasTagSize.xsmall,
                                    //       type: AzzasTagType.danger,
                                    //       outline: false,
                                    //       textStyle: Tokens.typography.body
                                    //           .extraSmall.extraSmallRegular,
                                    //     ),
                                    //   );
                                    // } else

                                    return Container();
                                  },
                                ),
                              ),
                            );
                          },
                        ),
                        if (!state.reachedLastPage)
                          Padding(
                            padding:
                                EdgeInsets.all(Tokens.spacing.spacingSmall),
                            child: const Center(child: AzzasSpinner()),
                          ),
                        const SizedBox(height: 20),
                      ],
                    )
                  ],
                ),
              );
            }),
      ),
    );
  }
}
