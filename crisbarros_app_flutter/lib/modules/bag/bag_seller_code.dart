import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BagAddSellerCode extends StatefulWidget {
  const BagAddSellerCode({super.key, this.sellerCodeApplied});

  final String? sellerCodeApplied;

  @override
  State<BagAddSellerCode> createState() => _BagAddSellerCodeState();
}

class _BagAddSellerCodeState extends State<BagAddSellerCode>
    with TickerProviderStateMixin {
  final _controller = TextEditingController();
  final _orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AzzasListItemButton(
          buttonText: widget.sellerCodeApplied != null ? 'Alterar' : 'Aplicar',
          isColumn: true,
          onTap: () {
            _showMainSheet(context);
          },
          leftIcon: Icon(
            Tokens.icons.general.person,
            size: 24,
          ),
          divider: false,
          title: 'Código de vendedora',
        ),
        const SizedBox(height: 24),
        Visibility(
          visible: widget.sellerCodeApplied != null,
          child: Column(
            children: [
              codeContainer(context),
              const SizedBox(height: 24),
            ],
          ),
        ),
        const Divider(
          height: 0.5,
          color: Color(0xFFE9E9E9),
        ),
      ],
    );
  }

  Widget codeContainer(
    BuildContext context, {
    bool containerTapEnabled = true,
    bool popBeforeRemove = false,
    bool allowRemove = true,
  }) {
    return GestureDetector(
      onTap: () {
        if (!containerTapEnabled) return;
        if (widget.sellerCodeApplied == null) {
          AnalyticsService.trackEvent('select_content', {
            'content_type': 'codigo-de-vendedora-ok',
          });
        }
        _showMainSheet(context);
      },
      child: Container(
        color: Tokens.colors.success.dark,
        height: 52,
        padding: const EdgeInsets.only(left: 16, right: 4),
        child: Row(
          children: [
            Text(
              getSellerCodeApplied() ?? "",
              style: TextStyle(
                color: Tokens.colors.neutral.pure,
              ),
            ),
            const Spacer(),
            Visibility(
              visible: allowRemove,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: Icon(
                  Tokens.icons.action.delete,
                  color: Tokens.colors.neutral.pure,
                ),
                onPressed: () {
                  if (popBeforeRemove) {
                    Navigator.pop(context);
                  }
                  _showRemoveSheet(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _validateSellerCode(String sellerCode) async {
    try {
      await _orderCheckoutHandler.addSellerCode(
        sellerCode: sellerCode,
      );
      if (mounted) {
        Navigator.of(context).pop();
        _showSuccessSheet(context: context);
      }
    } catch (e) {
      if (!mounted) return;
      _showAzzasSnackBar(
        context: context,
        message: 'Erro ao aplicar código de vendedora',
        isError: true,
      );
    }
  }

  void _showMainSheet(BuildContext context) {
    final orderFormCubit = Modular.get<OrderFormCubit>();
    _controller.clear();

    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        bool buttonActive = _controller.text.isNotEmpty;

        return StatefulBuilder(
          builder: (context, setState) {
            return BlocBuilder<OrderFormCubit, OrderFormState>(
              bloc: orderFormCubit,
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AzzasAddSellerCodeAndCouponsBottomSheet(
                      showDragBar: true,
                      onCloseTap: () => Navigator.of(context).pop(),
                      titleStyle: Tokens.typography.headings.small.smallRegular
                          .copyWith(color: Tokens.colors.neutral.dark),
                      bodyStyle: Tokens.typography.body.small.smallRegular
                          .copyWith(color: Tokens.colors.neutral.dark),
                      widget: Column(
                        children: [
                          AzzasCodeAndCouponButton(
                            controller: _controller,
                            onChanged: (value) {
                              bool buttonActiveCurrent = value.isNotEmpty;
                              if (buttonActive != buttonActiveCurrent) {
                                setState(() {
                                  buttonActive = buttonActiveCurrent;
                                });
                              }
                            },
                            isDisabled: !buttonActive,
                            onTap: () async {
                              FocusScope.of(context).unfocus();
                              await _validateSellerCode(_controller.text);
                            },
                            isLoading: state.isLoading,
                          ),
                          Visibility(
                            visible: widget.sellerCodeApplied != null,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: Tokens.spacing.spacingXLarge),
                                Text(
                                  "Código aplicado:",
                                  style: Tokens
                                      .typography.body.medium.mediumRegular,
                                ),
                                SizedBox(height: Tokens.spacing.spacingMedium),
                                codeContainer(
                                  context,
                                  containerTapEnabled: false,
                                  popBeforeRemove: true,
                                ),
                                SizedBox(height: Tokens.spacing.spacingSmall),
                                Text(
                                  "Se aplicável aos produtos da sacola, você poderá visualizar os benefícios no resumo da compra.",
                                  style: Tokens.typography.body.extraSmall
                                      .extraSmallRegular
                                      .copyWith(
                                    color: const Color(0xFF777777),
                                  ),
                                ),
                                SizedBox(height: Tokens.spacing.spacingSmall),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _showRemoveSheet(BuildContext context) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, state) {
            return AzzasRemoveItemBottomSheet(
              padding: EdgeInsets.all(Tokens.spacing.spacingLarge),
              closeIcon: Tokens.icons.action.close,
              showDragBar: true,
              isLoading: state.isLoading,
              isCancelButtonDisabled: state.isLoading,
              titleStyle:
                  Tokens.typography.headings.small.smallRegular.copyWith(
                color: Tokens.colors.neutral.dark,
              ),
              descriptionStyle:
                  Tokens.typography.body.small.smallRegular.copyWith(
                color: Tokens.colors.neutral.dark,
              ),
              onCancelTap: () {
                Navigator.pop(context);
              },
              onRemoveTap: () async {
                await _orderCheckoutHandler.removeSellerCode().then((value) {
                  if (context.mounted) Navigator.pop(context);
                });
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _showSuccessSheet({
    required BuildContext context,
  }) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return AzzasBottomSheet(
          showDragBar: true,
          onCloseTap: () => Navigator.of(context).pop(),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Código aplicado!",
                    style: Tokens.typography.headings.small.smallRegular,
                  ),
                  SizedBox(height: Tokens.spacing.spacingXLarge),
                  codeContainer(
                    context,
                    containerTapEnabled: false,
                    popBeforeRemove: true,
                    allowRemove: false,
                  ),
                  SizedBox(height: Tokens.spacing.spacingSmall),
                  Text(
                    "Se aplicável aos produtos da sacola, você poderá visualizar os benefícios no resumo da compra.",
                    style: Tokens.typography.body.extraSmall.extraSmallRegular
                        .copyWith(
                      color: const Color(0xFF777777),
                    ),
                  ),
                  SizedBox(height: Tokens.spacing.spacingXLarge),
                  AzzasPrimaryButton(
                    expanded: true,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text("Fechar"),
                  ),
                  SizedBox(height: Tokens.spacing.spacingMedium),
                ],
              ),
            ],
          ),
        );
      },
      vsync: this,
    );
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  String? getSellerCodeApplied() {
    return _orderFormCubit.getSalesPersonCodeApplied();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
