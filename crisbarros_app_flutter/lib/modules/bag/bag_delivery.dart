import 'package:azzas_analytics/events/checkout/bag_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart' hide Options;
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/delivery_address/pages/delivery_select_page.dart';
import 'package:flutter/material.dart';

class BagDelivery extends StatefulWidget {
  final BagPageCubit bagPageCubit;
  final bool showWarningField;
  const BagDelivery({
    super.key,
    required this.bagPageCubit,
    required this.showWarningField,
  });

  @override
  State<BagDelivery> createState() => _BagDeliveryState();
}

class _BagDeliveryState extends State<BagDelivery>
    with TickerProviderStateMixin {
  final cepController = TextEditingController();
  final _locationService = Modular.get<LocationService>();
  bool showShipping = false;
  AzzasDeliveryOption? selectedDeliveryOption;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _calculateDeliveriesByCep(String cep) async {
    try {
      await widget.bagPageCubit.getAddressFromCep(cep);
      Navigator.of(context).pop();
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao calcular frete',
          isError: true,
        );
      }
    }
  }

  void bottomSheetDeliveryDetails(
    BuildContext context,
    List<PackageModel> packages,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return AzzasDeliveryDetailsBottomSheet(
          onCloseTap: () {
            Navigator.pop(context);
          },
          titleStyle: Tokens.typography.headings.small.smallRegular
              .copyWith(color: Tokens.colors.neutral.dark),
          bodyStyle: Tokens.typography.body.small.smallRegular,
          estimateStyle: Tokens.typography.body.extraSmall.extraSmallRegular,
          packages: packages.map((element) => element.toPackageUi()).toList(),
          imageHeight: 155,
        );
      },
      vsync: this,
    );
  }

  Future<void> _calculateDeliveriesByDeviceLocation() async {
    try {
      widget.bagPageCubit.setLoadingLocation(true);
      final canAccessLocation = await _locationService.canAccessLocation();

      if (!canAccessLocation && mounted) {
        final result = await LocationHelper.requestLocationAccessBottomSheet(
          context,
          this,
          _locationService,
          bottomSheetTitleStyle: Tokens.typography.headings.small.smallRegular
              .copyWith(color: Tokens.colors.neutral.dark),
          bottomSheetBodyStyle: Tokens.typography.body.small.smallRegular,
          onCancelButtonTap: () {
            Navigator.of(context).pop();
          },
        );

        if (!result) {
          Navigator.of(context).pop();
        }
      }

      final coordinates = await _locationService.getCoordinates();
      final address = await widget.bagPageCubit.getAddressByGeolocalization(
        latitude: coordinates.latitude.toString(),
        longitude: coordinates.longitude.toString(),
      );

      final cep = address.postalCode;

      if (cep == null) {
        throw Exception('CEP não encontrado');
      }

      cepController.text = cep;
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao calcular frete',
          isError: true,
        );
      }
    } finally {
      widget.bagPageCubit.setLoadingLocation(false);
    }
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  void bottomSheetCalculateDelivery(BuildContext context) {
    showAzzasBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return BlocBuilder<BagPageCubit, BagPageState>(
          bloc: widget.bagPageCubit,
          builder: (context, state) {
            final isLoadingLocation = state.isLoadingLocation;

            return AzzasCalculateCepBottomSheet(
              showDragBar: true,
              onCloseTap: () => Navigator.pop(context),
              calculateCepButtonTap: (cep) => _calculateDeliveriesByCep(cep),
              locationActiveButtonTap: () {
                _calculateDeliveriesByDeviceLocation();
              },
              isLoadingCalculateCepButton: state.isLoadingAddress,
              cepController: cepController,
              isLoadingLocation: isLoadingLocation,
              titleStyle: Tokens.typography.headings.small.smallRegular
                  .copyWith(color: Tokens.colors.neutral.dark),
              bodyStyle: Tokens.typography.body.small.smallRegular
                  .copyWith(color: Tokens.colors.neutral.dark),
            );
          },
        );
      },
      vsync: this,
    );
  }

  Widget _buildShippingOptions(
    List<PackageModel> packages,
    AzzasDeliveryOption? selectedDeliveryOption,
    bool? isLoading,
  ) {
    return DeliveryAddressSelectPage(
      packages: packages,
      bottomSheetDeliveryDetails: () =>
          bottomSheetDeliveryDetails(context, packages),
      selectedDeliveryOption: selectedDeliveryOption,
      selectDeliveryOption: (AzzasDeliveryOption option) {
        widget.bagPageCubit.updateSelectedAddress(option).then((_) {
          Modular.get<EventDispatcher>().logAddShippingInfo();
        });
      },
      getDeliveryPrice: () => widget.bagPageCubit.getDeliveryPrice(packages),
      getDeliveryTime: () => widget.bagPageCubit.getDeliveryTime(packages),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
      bloc: widget.bagPageCubit,
      builder: (context, state) {
        final addressTitle = state.currentAddress?.cityAndState != null
            ? 'Entrega para ${state.currentAddress?.cityAndState}'
            : 'Frete e prazo de entrega';

        final subtitle = state.currentAddress?.formattedCep != null
            ? 'CEP ${state.currentAddress?.formattedCep}'
            : 'receba em casa ou retire na loja';
        return Column(
          children: [
            AzzasListItemButton(
              isColumn: true,
              buttonText:
                  state.currentAddress != null ? 'Alterar CEP' : 'Adicionar',
              onTap: () async {
                await BagEvents.logBagOption(
                    local: 'bag',
                    opcaoClicada: state.currentAddress != null
                        ? 'alterar-cep'
                        : 'adicionar-cep');
                bottomSheetCalculateDelivery(context);
              },
              leftIcon: Icon(
                Tokens.icons.shipping.home,
                size: 24,
              ),
              divider: false,
              title: addressTitle,
              subtitle: subtitle,
              showWarningField: widget.showWarningField,
              warningField: Padding(
                padding:
                    EdgeInsets.symmetric(vertical: Tokens.spacing.spacingSmall),
                child: Container(
                  padding: EdgeInsets.all(Tokens.spacing.spacingSmall),
                  color: Tokens.colors.warning.light,
                  child: Text(
                    'Adicione o prazo de entrega para prosseguir',
                    style: Tokens.typography.body.extraSmall.extraSmallMedium
                        .copyWith(
                      color: Tokens.colors.warning.medium,
                    ),
                  ),
                ),
              ),
            ),
            if (state.packages?.isNotEmpty == true)
              _buildShippingOptions(
                state.packages ?? [],
                state.selectedDeliveryOption,
                state.isLoadingAddress,
              ),
            Padding(
              padding: state.packages?.isNotEmpty == true
                  ? EdgeInsets.zero
                  : EdgeInsets.only(top: Tokens.spacing.spacingMedium),
              child: Divider(
                height: 0.5,
                color: Tokens.colors.neutral.medium,
              ),
            ),
          ],
        );
      },
    );
  }
}
