import 'package:azzas_analytics/events/category/favorites_page_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BagProductUnavailableBottomSheet extends StatefulWidget {
  const BagProductUnavailableBottomSheet({
    super.key,
  });

  @override
  State<BagProductUnavailableBottomSheet> createState() =>
      _BagProductUnavailableBottomSheetState();
}

class _BagProductUnavailableBottomSheetState
    extends State<BagProductUnavailableBottomSheet> {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final _wishlistCommonCubit = Modular.get<WishlistCommonCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  Future<void> _removeAllUnavailableProducts() async {
    try {
      await _eventDispatcher.logRemoveAllUnavailableItems();
      await _orderCheckoutHandler.removeAllUnavailableItems();

      Navigator.of(context).pop();
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao remover produtos indisponíveis',
          isError: true,
        );
      }
    }
  }

  Future<void> _addProductWishList({required OrderFormProduct item}) async {
    try {
      await _wishlistCommonCubit.addProduct(item.productId ?? '');
      await _orderCheckoutHandler.updateQuantityItem(
          itemId: item.id ?? '', quantity: 0);

      if (mounted) {
        _showAzzasSnackBar(
            context: context, message: "Produto adicionado aos desejos");
      }

      final hasUnavailableItems =
          _orderFormCubit.state.orderForm?.getUnavailableItems.isNotEmpty;

      if (hasUnavailableItems == false) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao adicionar produto aos desejos',
          isError: true,
        );
      }
    }
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderFormCubit, OrderFormState>(
      bloc: _orderFormCubit,
      builder: (context, state) {
        final unavailableProducts = state.orderForm?.getUnavailableItems ?? [];

        return AzzasBottomSheet(
          backgroundColor: Tokens.colors.neutral.pure,
          padding: EdgeInsets.only(
              top: Tokens.spacing.spacingMedium,
              right: Tokens.spacing.spacingMedium),
          borderRadius: BorderRadius.zero,
          showDragBar: true,
          onCloseTap: () => Navigator.pop(context),
          closeIcon: Tokens.icons.action.close,
          heightFactor: 0.87,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: Tokens.spacing.spacingMedium),
                child: Column(
                  children: [
                    Text(
                      'Ops, um ou mais produtos esgotaram',
                      style: Tokens.typography.headings.small.smallRegular
                          .copyWith(color: Tokens.colors.neutral.dark),
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingSmall,
                    ),
                    Text(
                      'Infelizmente um ou mais produtos  ficaram sem estoque.',
                      style: Tokens.typography.body.small.smallRegular
                          .copyWith(color: Tokens.colors.neutral.dark),
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingSmall,
                    ),
                    Container(
                      padding: EdgeInsets.all(Tokens.spacing.spacingSmall),
                      height: 52,
                      color: Tokens.colors.success.dark,
                      child: Text(
                        'Dica: salve nos desejos antes de remover :)        ',
                        style: Tokens.typography.body.small.smallRegular,
                      ),
                    ),
                    SizedBox(
                      height: Tokens.spacing.spacingSmall,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 300,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: unavailableProducts.length,
                  itemBuilder: (context, index) => AzzasCheckoutProductCard(
                    imageHeight: 197,
                    imageWidth: 130,
                    saveProductText: 'Salvar para depois',
                    showRemoveButton: true,
                    onTapSaveProduct: () async {
                      await FavoritesPageEvents.logSelectWishlistOption(
                          local: 'bag', action: 'salvar-para-depois');
                      _addProductWishList(
                        item: unavailableProducts[index],
                      );
                    },
                    product: unavailableProducts[index].toCardParams(),
                  ),
                ),
              ),
              SizedBox(
                height: Tokens.spacing.spacingMedium,
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: Tokens.spacing.spacingMedium,
                  bottom: Tokens.spacing.spacingXXSmall,
                ),
                child: AzzasPrimaryButton(
                  expanded: true,
                  isLoading: state.isLoading,
                  size: ButtonSize.large,
                  onPressed: _removeAllUnavailableProducts,
                  child: Text(
                    "Remover itens sem estoque",
                    style: Tokens.typography.body.small.smallRegular,
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
