import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BagAddCoupons extends StatefulWidget {
  const BagAddCoupons({super.key, this.couponApplied});

  final String? couponApplied;

  @override
  State<BagAddCoupons> createState() => _BagAddCouponsState();
}

class _BagAddCouponsState extends State<BagAddCoupons>
    with TickerProviderStateMixin {
  final _controller = TextEditingController();
  final _orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AzzasListItemButton(
          buttonText: widget.couponApplied != null ? 'Alterar' : 'Aplicar',
          isColumn: true,
          onTap: () {
            _showMainSheet(context);
          },
          leftIcon: Icon(
            Tokens.icons.payment.wallet,
            size: 24,
          ),
          divider: false,
          title: 'Voucher',
        ),
        const SizedBox(height: 24),
        Visibility(
          visible: widget.couponApplied != null,
          child: Column(
            children: [
              _couponContainer(context),
              const SizedBox(height: 24),
            ],
          ),
        ),
        const Divider(
          height: 0.5,
          color: Color(0xFFE9E9E9),
        ),
      ],
    );
  }

  Widget _couponContainer(
    BuildContext context, {
    bool containerTapEnabled = true,
    bool popBeforeRemove = false,
    bool allowRemove = true,
  }) {
    return GestureDetector(
      onTap: () {
        if (!containerTapEnabled) return;
        _showMainSheet(context);
      },
      child: Container(
        color: Tokens.colors.success.dark,
        height: 52,
        padding: const EdgeInsets.only(left: 16, right: 4),
        child: Row(
          children: [
            Text(
              getCouponApplied() ?? "",
              style: TextStyle(
                color: Tokens.colors.neutral.pure,
              ),
            ),
            const Spacer(),
            Visibility(
              visible: allowRemove,
              child: IconButton(
                icon: Icon(
                  Tokens.icons.action.delete,
                  color: Tokens.colors.neutral.pure,
                ),
                onPressed: () async {
                  if (popBeforeRemove) {
                    Navigator.pop(context);
                  }
                  _showRemoveSheet(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _validateCoupon(String coupon) async {
    try {
      await _orderCheckoutHandler.addCoupon(coupon: coupon);

      if (mounted) {
        Navigator.of(context).pop();
        _showSuccessSheet(context: context);
      }
    } on InvalidCouponException catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: e.message ?? "Cupom inválido",
          isError: true,
        );
      }
    } on ExpiredCouponException catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: e.message ?? "Cupom expirado",
          isError: true,
        );
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao aplicar cupom',
          isError: true,
        );
      }
    } finally {
      _controller.clear();
    }
  }

  void _showMainSheet(BuildContext context) {
    final orderFormCubit = Modular.get<OrderFormCubit>();
    _controller.clear();

    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        bool buttonActive = _controller.text.isNotEmpty;

        return StatefulBuilder(
          builder: (context, setState) {
            return BlocBuilder<OrderFormCubit, OrderFormState>(
              bloc: orderFormCubit,
              builder: (context, state) {
                return AzzasAddSellerCodeAndCouponsBottomSheet(
                  showDragBar: true,
                  title: 'Voucher',
                  body: 'Caso você possua um voucher aplique abaixo',
                  titleStyle: Tokens.typography.headings.small.smallRegular
                      .copyWith(color: Tokens.colors.neutral.dark),
                  bodyStyle: Tokens.typography.body.small.smallRegular
                      .copyWith(color: Tokens.colors.neutral.dark),
                  onCloseTap: () => Navigator.of(context).pop(),
                  widget: Column(
                    children: [
                      AzzasCodeAndCouponButton(
                        controller: _controller,
                        onChanged: (value) {
                          bool buttonActiveCurrent = value.isNotEmpty;
                          if (buttonActive != buttonActiveCurrent) {
                            setState(() {
                              buttonActive = buttonActiveCurrent;
                            });
                          }
                        },
                        isDisabled: !buttonActive,
                        placeholder: 'Cupom',
                        inputPadding: EdgeInsets.all(
                          Tokens.spacing.spacingSmall,
                        ),
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          AnalyticsService.trackEvent('add_coupon', {
                            'coupon_name': _controller.text,
                          });
                          _validateCoupon(_controller.text);
                        },
                        isLoading: state.isLoading,
                      ),
                      Visibility(
                        visible: widget.couponApplied != null,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: Tokens.spacing.spacingXLarge),
                            Text(
                              "Cupom aplicado:",
                              style:
                                  Tokens.typography.body.medium.mediumRegular,
                            ),
                            SizedBox(height: Tokens.spacing.spacingMedium),
                            _couponContainer(
                              context,
                              containerTapEnabled: false,
                              popBeforeRemove: true,
                            ),
                            SizedBox(height: Tokens.spacing.spacingSmall),
                            Text(
                              "Se aplicável aos produtos da sacola, você poderá visualizar os benefícios no resumo da compra.",
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular
                                  .copyWith(
                                color: const Color(0xFF777777),
                              ),
                            ),
                            SizedBox(height: Tokens.spacing.spacingSmall),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _showRemoveSheet(BuildContext context) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, state) {
            return AzzasRemoveItemBottomSheet(
              padding: EdgeInsets.all(Tokens.spacing.spacingLarge),
              closeIcon: Tokens.icons.action.close,
              showDragBar: true,
              isLoading: state.isLoading,
              isCancelButtonDisabled: state.isLoading,
              titleStyle:
                  Tokens.typography.headings.small.smallRegular.copyWith(
                color: Tokens.colors.neutral.dark,
              ),
              descriptionStyle:
                  Tokens.typography.body.small.smallRegular.copyWith(
                color: Tokens.colors.neutral.dark,
              ),
              onCancelTap: () {
                Navigator.pop(context);
              },
              onRemoveTap: () async {
                await _orderCheckoutHandler.removeCoupon().then((value) {
                  if (context.mounted) Navigator.pop(context);
                });
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _showSuccessSheet({
    required BuildContext context,
  }) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return AzzasBottomSheet(
          showDragBar: true,
          onCloseTap: () => Navigator.of(context).pop(),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Cupom aplicado!",
                    style: Tokens.typography.headings.small.smallRegular,
                  ),
                  SizedBox(height: Tokens.spacing.spacingXLarge),
                  _couponContainer(
                    context,
                    containerTapEnabled: false,
                    popBeforeRemove: true,
                    allowRemove: false,
                  ),
                  SizedBox(height: Tokens.spacing.spacingSmall),
                  Text(
                    "Se aplicável aos produtos da sacola, você poderá visualizar os benefícios no resumo da compra.",
                    style: Tokens.typography.body.extraSmall.extraSmallRegular
                        .copyWith(
                      color: const Color(0xFF777777),
                    ),
                  ),
                  SizedBox(height: Tokens.spacing.spacingXLarge),
                  AzzasPrimaryButton(
                    expanded: true,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text("Fechar"),
                  ),
                  SizedBox(height: Tokens.spacing.spacingMedium),
                ],
              ),
            ],
          ),
        );
      },
      vsync: this,
    );
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  String? getCouponApplied() {
    return _orderFormCubit.getCouponApplied();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
