import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';
import 'package:crisbarros_app_flutter/modules/account/my_account/my_account_page.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_page.dart';
import 'package:crisbarros_app_flutter/modules/home/<USER>';
import 'package:crisbarros_app_flutter/modules/home/<USER>/home_bottom_navigation.dart';
import 'package:crisbarros_app_flutter/modules/menu/menu_page.dart';
import 'package:crisbarros_app_flutter/modules/wishlist/pages/wishlist_page.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

bool _initialURILinkHandled = false;

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  static goHome() {
    final cubit = Modular.get<MainPageCubit>();
    cubit.switchTab(MainPageTab.home);
  }

  static goMenu() {
    final cubit = Modular.get<MainPageCubit>();
    cubit.switchTab(MainPageTab.menu);
  }

  static goMyAccount() {
    final cubit = Modular.get<MainPageCubit>();
    cubit.switchTab(MainPageTab.myAccount);
  }

  static goBag() {
    var ttt;
    final cubit = Modular.get<MainPageCubit>();
    cubit.switchTab(MainPageTab.bag);
  }

  static goWishlist() {
    final cubit = Modular.get<MainPageCubit>();
    cubit.switchTab(MainPageTab.wishlist);
  }

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final deeplink = Modular.get<DeeplinkHandler>();
  MainPageTab selectedTab = MainPageTab.home;
  StreamSubscription? _streamSubscription;

  @override
  void initState() {
    super.initState();
    scheduleMicrotask(() async => handlePushNotification());
    _handleIncomingLinks();
    handleLink();
  }

  @override
  dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  void _handleIncomingLinks() {
    _initDeeplinkHandler();
  }

  void handleLink() {
    if (_streamSubscription != null) {
      _streamSubscription!.cancel();
      _streamSubscription = null;
    }

    if (!LinkManager().isHandlingLink &&
        WidgetsBinding.instance.lifecycleState != AppLifecycleState.paused) {
      LinkManager().startHandling();
      _incomingLinkHandler();
      Future.delayed(const Duration(seconds: 2), () {
        LinkManager().reset();
      });
    } else {
      debugPrint(
          'Link handling is currently blocked or app is not in foreground.');
    }
  }

  final cubit = Modular.get<MainPageCubit>();
  final pages = {
    MainPageTab.home: const HomePage(),
    MainPageTab.menu: const MenuPage(),
    MainPageTab.wishlist: const WishlistPage(),
    MainPageTab.bag: CheckoutNavigationManager.bagTag,
    MainPageTab.myAccount: const MyAccountPage(),
  };

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MainPageCubit, MainPageState>(
      bloc: cubit,
      builder: (context, state) {
        return Scaffold(
          bottomNavigationBar: HomeBottomNavigation(
            indexSelected: state.mainPageTab.index,
            onChange: (index) {
              cubit.switchTab(MainPageTab.values[index]);
            },
          ),
          body: pages[state.mainPageTab],
        );
      },
    );
  }

  Future<void> _initDeeplinkHandler() async {
    if (!_initialURILinkHandled) {
      _initialURILinkHandled = true;
      try {
        final appLinks = AppLinks();
        final initialURI = await appLinks.getInitialLink();

        if (initialURI != null &&
            (initialURI.host == 'www.crisbarros.com.br')) {
          debugPrint("Initial URI received $initialURI");
          if (!mounted) {
            return;
          }
          deeplink.redirectFromURL(uri: initialURI);
        } else {
          debugPrint("Null Initial URI received");
        }
      } on Exception catch (e) {
        debugPrint(e.toString());
      }
    }
  }

  void _incomingLinkHandler() {
    if (!kIsWeb) {
      try {
        final appLinks = AppLinks();

        _streamSubscription = appLinks.uriLinkStream.listen((Uri? uri) {
          if (uri != null && (uri.host == 'www.crisbarros.com.br')) {
            if (deeplink.getHandlingLink() == true) return;
            debugPrint("Initial URI received $uri");
            if (!mounted) {
              return;
            }
            deeplink.updateHandlingLink(true);
            deeplink.redirectFromURL(uri: uri);
          } else {
            debugPrint("Null Initial URI received");
          }
        }, onError: (Object err) {
          if (!mounted) {
            return;
          }
          debugPrint('Error occurred: $err');
        });
      } catch (_) {}
    }
  }

  void _handleMessage(RemoteMessage message) {
    final indexNavigateTo = message.data.keys.toList().indexOf('navigate_to');

    if (indexNavigateTo >= 0) {
      final navigateToNotification =
          message.data.values.toList().elementAtOrNull(indexNavigateTo);

      NavigatorDynamic.call(navigateToNotification);
    }
  }

  Future<void> handlePushNotification() async {
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
  }
}

class LinkManager {
  static final LinkManager _instance = LinkManager._internal();
  bool isHandlingLink = false;

  // Fábrica para acessar a instância do singleton
  factory LinkManager() {
    return _instance;
  }

  // Construtor interno privado
  LinkManager._internal();

  // Método para resetar o estado
  void reset() {
    isHandlingLink = false;
  }

  // Método para iniciar o processamento de um link
  void startHandling() {
    isHandlingLink = true;
  }
}
