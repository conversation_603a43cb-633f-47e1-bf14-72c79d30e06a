import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/deeplink/loading_screen.dart';
import 'package:crisbarros_app_flutter/app_config.dart';
import 'package:crisbarros_app_flutter/config/services/remote_config/remote_config_manager.dart';
import 'package:crisbarros_app_flutter/modules/account/account_routes.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_routes.dart';
import 'package:crisbarros_app_flutter/modules/checking_account/checking_account_routes.dart';
import 'package:crisbarros_app_flutter/modules/checkout/checkout_routes.dart';
import 'package:crisbarros_app_flutter/modules/components/components_page.dart';
import 'package:crisbarros_app_flutter/modules/delivery_address/delivery_address_routes.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:crisbarros_app_flutter/modules/pdc/pdc_routes.dart';
import 'package:crisbarros_app_flutter/modules/pdp/pdp_routes.dart';
import 'package:crisbarros_app_flutter/modules/splash/splash_page.dart';
import 'package:crisbarros_app_flutter/modules/tapume/tapume_page.dart';
import 'package:crisbarros_app_flutter/modules/webview/webview_routes.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class AppModule extends Module {
  static final _remoteConfig = RemoteConfigManager.config;
  final appConfig = AppConfig(
    recaptchaConfig: RecaptchaConfig(
      iosKey: '6LfvVcsqAAAAAA0cTMofemIoZ68ZvYT1jI36qabz',
      androidKey: '6Leo08YqAAAAADvwYu9YpnaDBRFPHasHbelYezO-',
    ),
    brand: CrisBarrosAppConfig.brand,
    posSoma: _remoteConfig.somaBusConfig.posSoma,
    cmsStagingToken: _remoteConfig.cmsConfig.accessToken,
    cmsStagingUrl: _remoteConfig.cmsConfig.baseUrl,
    applePayConfig: ApplePayConfig(
      merchantId: 'merchant.com.crisbarros',
      merchantName: 'GrupoSoma',
      brandName: 'Cris barros',
      completeOderId: 'crb',
      paymentSystemId: '204',
    ),
    masterDataConfig: const MasterDataConfig(
      salesPersonTable: 'VD',
      measurementsTable: 'ME',
    ),
    store: StoreConfig(
      storeName: 'lojacrisbarros',
      defaultSalesChannel: 3,
      utmiCampaign: UTMICampaign(addCode: 'codigodavendedora', removeCode: ''),
    ),
    deeplinkConfig: CrisBarrosAppConfig.deeplinkConfig,
  );

  @override
  void binds(Injector i) {
    i.add<AzzasAppsflyer>(AzzasAppsflyer.new);
    final appCommonsModule = AppCommonsModule(
      appConfig: appConfig,
      posSoma: appConfig.posSoma,
    );

    appCommonsModule.binds(i);

    i.addSingleton<AppCommonsModule>(() => appCommonsModule);

    i.addSingleton<CmsService>(
      () => CmsService(
        CrisBarrosAppConfig.brand.brandCmsName(),
        appConfig.cmsStagingUrl,
        appConfig.cmsStagingToken,
      ),
    );

    i.addSingleton<HomeCubit>(() => HomeCubit());
    i.addSingleton<TapumeCubit>(() => TapumeCubit());

    super.binds(i);
  }

  @override
  void routes(r) {
    final httpClient = Modular.get<AppCommonsModule>().httpClient;

    r.child('/', child: (context) {
      return const MainPage();
    });

    r.child('/loading', child: (context) {
      return const LoadingScreen();
    });

    r.child('/components', child: (context) {
      return const ComponentsPage();
    });

    r.child('/splash', child: (context) {
      return const SplashPage();
    });
    r.child('/tapume', child: (context) {
      return const TapumePage();
    });

    r.module(
      '/account',
      module: AccountModule(
        overrideRoutes: AccountRoutes.routes,
        httpClient: httpClient,
      ),
    );

    r.module(
      '/pdp',
      module: PdpModule(
        overrideRoutes: PdpRoutes.routes,
        httpClient: httpClient,
        appConfig: appConfig,
      ),
    );

    r.module(
      '/checkout',
      module: CheckoutModule(
        overrideRoutes: CheckoutRoutes.routes,
        httpClient: httpClient,
      ),
    );

    r.module(
      '/checking',
      module: CheckingAccountModule(
        overrideRoutes: CheckingAccountRoutes.routes,
        httpClient: httpClient,
      ),
    );

    DeliveryAddressRoutes.routes(r);
    PdcRoutes.routes(r);
    BagRoutes.routes(r);
    WebViewRoutes.routes(r);
    CheckoutRoutes.routes(r);

    r.module(
      '/checkout',
      module: VertappsCheckoutWhitelabelModule(
        posSoma: appConfig.posSoma,
        privacyPolicyLink: CrisBarrosAppConfig.privacyPolicyUrl,
        brand: BrandEnum.crisbarros,
      ),
    );
  }
}
