import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';

import 'package:flutter/material.dart';

class SuggestionsProductCardTheme {
  static get value => AzzasSuggestionsProductCardTheme(
        imageWidth: 130,
        imageHeight: 197,
        padding: EdgeInsets.zero,
        productInfoPadding: EdgeInsets.symmetric(
          horizontal: Tokens.spacing.spacingSmall,
          vertical: Tokens.spacing.spacingMedium,
        ),
        tagsAlignment: AzzasProductInfoTagsAlignment.belowProductTitle,
        addToBagButton: AzzasSuggestionsProductCardAddToBagButtonTheme(
          textStyle: Tokens.typography.body.extraSmall.extraSmallRegular,
          icon: Tokens.icons.navigation.right,
          iconSize: 12.0,
          iconColor: Tokens.colors.neutral.dark,
        ),
        midSpacing: Tokens.spacing.spacingMedium,
      );
}
