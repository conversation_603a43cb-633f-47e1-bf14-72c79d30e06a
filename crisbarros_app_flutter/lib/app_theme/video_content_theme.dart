import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';

class VideoContentTheme {
  static AzzasVideoContentTheme get value {
    return AzzasVideoContentTheme(
      iconBack: Tokens.icons.navigation.back,
      color: Tokens.colors.neutral.pure,
      iconSpeakerMute: Tokens.icons.video.speakerX,
      iconSpeakerHigh: Tokens.icons.video.speakerHigh,
      shadows: Tokens.colors.neutral.dark,
      iconEye: Tokens.icons.action.visibilityTrue,
      videoDescriptionStyle: Tokens.typography.body.small.smallMedium,
      sideInteractiveButtonStyle: Tokens.typography.body.small.smallMedium,
      progressIndicatorColor: Tokens.colors.neutral.pure,
      progressIndicatorBackgroundColor: Tokens.colors.neutral.pure,
      iconPlay: Tokens.icons.video.play,
      pausedOverlayDecoration: Tokens.colors.brand.pure.withOpacity(0.33),

      // Ajustar estes themes caso a marca comece a exibir videoContent interativo
      closeIcon: Tokens.icons.action.close,
      timeStyle: Tokens.typography.body.small.smallRegular
          .copyWith(color: Tokens.colors.typography.pure),
      iconsColors: Tokens.colors.neutral.pure,
      interactiveProgressIndicatorColor: Tokens.colors.neutral.pure,
      interactiveProgressIndicatorBackgroundColor:
          Tokens.colors.neutral.pure.withOpacity(0.4),
    );
  }
}
