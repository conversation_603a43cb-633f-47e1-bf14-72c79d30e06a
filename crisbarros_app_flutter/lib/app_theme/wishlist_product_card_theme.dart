import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';

import 'package:flutter/material.dart';

class WishlistProductCardTheme {
  static get value => AzzasWishlistProductCardTheme(
        imageWidth: 130,
        imageHeight: 197,
        padding: EdgeInsets.zero,
        productInfoPadding: EdgeInsets.symmetric(
          horizontal: Tokens.spacing.spacingSmall,
          vertical: Tokens.spacing.spacingMedium,
        ),
        tagsAlignment: AzzasProductInfoTagsAlignment.belowProductTitle,
        removeButton: AzzasProductCardRemoveButtonTheme(
          textStyle:
              Tokens.typography.body.extraSmall.extraSmallRegular.copyWith(
            color: Tokens.colors.error.medium,
          ),
          reverseItems: false,
          icon: Tokens.icons.navigation.right,
          iconSize: 12.0,
          iconColor: Tokens.colors.error.medium,
        ),
        addToBagButton: AzzasWishlistProductCardAddToBagButtonTheme(
            textStyle: Tokens.typography.body.extraSmall.extraSmallRegular,
            icon: Tokens.icons.navigation.right,
            iconSize: 12.0,
            iconColor: Tokens.colors.neutral.dark,
            expandedAddToBag: true),
        midSpacing: Tokens.spacing.spacingMedium,
      );
}
