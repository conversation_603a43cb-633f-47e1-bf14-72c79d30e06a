<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />


    <application
        tools:replace="android:label"
        android:label="CRIS BARROS"
        android:icon="@mipmap/ic_launcher">

        <meta-data
            android:name="com.appsflyer.FacebookApplicationId"
            android:value="@string/facebook_application_id" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>


            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!--Página
                principal-->
                <data android:scheme="https" android:host="www.crisbarros.com.br" android:path="" />
                <data android:scheme="https" android:host="www.crisbarros.com.br"
                    android:pathPrefix="/" />
            
                <!-- Esse link é responsável por abrir todos os subdomínios -->
                <data android:scheme="https" android:host="*.crisbarros.com.br" />


                <!-- PDPs -->
                <data android:scheme="https" android:host="www.crisbarros.com.br"
                    android:pathPattern="/.*/p" />
                <data android:scheme="https" android:host="www.crisbarros.com.br"
                    android:pathPattern="/.*/p/.*" />

            </intent-filter>

        </activity>
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:exported="true"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
</manifest>