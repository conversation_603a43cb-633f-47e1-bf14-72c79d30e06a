import Adyen
import adyen_checkout
import UIKit
import Flutter
import Firebase

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    setDropInStyle()
    setCardComponentStyle()
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
   Messaging.messaging().apnsToken = deviceToken
   super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
 }

 private func setDropInStyle() {
      var dropInStyle = Adyen.DropInComponent.Style()
      dropInStyle.formComponent.mainButtonItem.button.backgroundColor = .black
      dropInStyle.formComponent.mainButtonItem.button.title.color = .white
      AdyenAppearance.dropInStyle = dropInStyle
  }

  private func setCardComponentStyle() {
      var cardComponentStyle = Adyen.FormComponentStyle()
      cardComponentStyle.mainButtonItem.button.backgroundColor = .black
      cardComponentStyle.mainButtonItem.button.title.color = .white
      AdyenAppearance.cardComponentStyle = cardComponentStyle
  }

}
