import 'package:azzas_ui/components/components.dart';

class AzzasTheme {
  final AzzasButtonTheme button;
  final AzzasSpinnerTheme spinner;
  final AzzasTitleSectionTheme titleSection;
  final AzzasSubtitleSectionTheme subtitleSection;
  final AzzasSkuSelectorTheme skuSelector;
  final AzzasLineTheme line;
  final AzzasFeedbackCardTheme feedbackCard;
  final AzzasToggleTheme toggle;
  final AzzasProductImageTheme productImage;
  final AzzasShimmerTheme shimmer;
  final AzzasFavoriteButtonBaseTheme favoriteButton;
  final AzzasCheckboxTheme checkbox;
  final AzzasRadioButtonTheme radioButton;
  final AzzasProductInfoTheme productInfo;
  final AzzasCheckoutProductCardTheme checkoutProductCard;
  final AzzasWishlistProductCardTheme wishlistProductCard;
  final AzzasSuggestionsProductCardTheme suggestionsProductCard;
  final AzzasOrderReviewProductCardTheme orderReviewProductCard;
  final AzzasInputTheme input;
  final AzzasTagTheme tags;
  final AzzasFilterButtonTheme filterButton;
  final AzzasCardContentTheme cardContent;
  final AzzasCheckoutInstallmentsTheme checkoutInstallments;
  final AzzasCardAddressTheme cardAddress;
  final AzzasFilterManagementTheme filterManagement;
  final AzzasCheckoutResumeTheme checkoutResume;
  final AzzasTabTheme tab;
  final AzzasAppBarTheme appBar;
  final AzzasBottomBarTheme bottomBarTheme;
  final AzzasControllerCarrouselTheme controllerCarrousel;
  final AzzasSpotProductTheme spotProduct;
  final AzzasPlusButtonTheme plusButton;
  final AzzasBottomSheetTheme bottomSheetTheme;
  final AzzasSelectLoginMethodBottomSheetTheme selectLoginMethodBottomSheet;
  final AzzasSnackBarTheme snackBar;
  final AzzasAccordionMenuTheme accordionMenu;
  final AzzasPickerTheme picker;
  final AzzasCepButtonTheme cepButton;
  final AzzasCheckoutStepTheme checkoutStep;
  final AzzasAddressInfoTheme addressInfo;
  final AzzasSliderTheme slider;
  final AzzasOrderStatusTheme orderStatus;
  final AzzasListItemTheme listItem;
  final AzzasOrderSummaryStatusTheme orderSummaryStatus;
  final AzzasCardOrderTheme cardOrderTheme;
  final AzzasOrderCompletedTheme orderCompleted;
  final AzzasColorSelectorTheme colorSelector;
  final AzzasProductInformationTheme productInformation;
  final AzzasProductBoxTheme productBox;
  final AssasPdpCarrouselTheme pdpCarrouselTheme;
  final AzzasHighlightItemTheme highlightItemTheme;
  final AzzasClocksTheme clocks;
  final AzzasVideoContentTheme videoContentTheme;
  final AzzasBrandHeaderTheme brandHeaderTheme;
  final AzzasBadgeTheme badgeTheme;

  AzzasTheme({
    required this.button,
    required this.spinner,
    required this.titleSection,
    required this.subtitleSection,
    required this.skuSelector,
    required this.line,
    required this.feedbackCard,
    required this.toggle,
    required this.productImage,
    required this.shimmer,
    required this.favoriteButton,
    required this.checkbox,
    required this.radioButton,
    required this.productInfo,
    required this.checkoutProductCard,
    required this.wishlistProductCard,
    required this.suggestionsProductCard,
    required this.orderReviewProductCard,
    required this.input,
    required this.tags,
    required this.filterButton,
    required this.cardContent,
    required this.checkoutInstallments,
    required this.cardAddress,
    required this.filterManagement,
    required this.checkoutResume,
    required this.tab,
    required this.appBar,
    required this.bottomBarTheme,
    required this.controllerCarrousel,
    required this.spotProduct,
    required this.plusButton,
    required this.bottomSheetTheme,
    required this.snackBar,
    required this.accordionMenu,
    required this.picker,
    required this.cepButton,
    required this.checkoutStep,
    required this.addressInfo,
    required this.slider,
    required this.orderStatus,
    required this.listItem,
    required this.orderSummaryStatus,
    required this.cardOrderTheme,
    required this.orderCompleted,
    required this.colorSelector,
    required this.productInformation,
    required this.productBox,
    required this.pdpCarrouselTheme,
    required this.highlightItemTheme,
    required this.selectLoginMethodBottomSheet,
    required this.clocks,
    required this.videoContentTheme,
    required this.brandHeaderTheme,
    required this.badgeTheme,
  });
}
