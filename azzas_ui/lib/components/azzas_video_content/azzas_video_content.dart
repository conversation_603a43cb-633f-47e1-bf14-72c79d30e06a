import 'package:azzas_ui/azzas_theme/azzas_theme_provider.dart';
import 'package:azzas_ui/components/azzas_video/azzas_video.dart';
import 'package:azzas_ui/components/azzas_video_content/azzas_video_content_exp.dart';
import 'package:azzas_ui/helpers/dt_shadow_helper.dart';
import 'package:flutter/material.dart';

class AzzasVideoContent extends StatefulWidget {
  final VideoContent videoContent;
  const AzzasVideoContent({
    required this.videoContent,
  });

  @override
  State<AzzasVideoContent> createState() => _AzzasVideoContentState();
}

class _AzzasVideoContentState extends State<AzzasVideoContent> {
  late final _videoGalleryController = VideoGalleryController();
  late final VideoContent _videoContent;
  final _videoController = VideoController();

  bool _isVideoMuted = false;
  bool _isVideoPaused = false;

  @override
  void initState() {
    super.initState();
    _videoContent = widget.videoContent;
    if (widget.videoContent.muteOnInit == true) {
      _isVideoMuted = true;
    }
  }

  @override
  void dispose() {
    _videoController.dispose();

    super.dispose();
  }

  void _onTapViewProducts() {
    if (_videoContent.navigateTo != null) {
      _videoContent.navigateTo?.call();
      _videoController.pause();

      setState(() {
        _isVideoPaused = true;
      });
    }
  }

  void _onTapVolume() {
    setState(() {
      _isVideoMuted = !_isVideoMuted;
    });
    _isVideoMuted
        ? _videoController.setVolume(0.0)
        : _videoController.setVolume(1.0);
  }

  void _onStartVideo() {
    final bool videoWasAlreadySeen =
        _videoGalleryController.isVideoInWatchedList(
      _videoContent.videoUrl,
    );
  }

  void _onTapVideo() {
    if (_isVideoPaused) {
      _videoController.play();
    } else {
      _videoController.pause();
    }

    setState(() {
      _isVideoPaused = !_isVideoPaused;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          VideoContentPlayer(
            onStartVideo: _onStartVideo,
            onTapVideo: _onTapVideo,
            videoUrl: _videoContent.videoUrl!,
            volume: _isVideoMuted ? 0.0 : 1.0,
            videoController: _videoController,
            isVideoProduct: _videoContent.isInteractiveProgressIndicator,
            onTapVolume: _onTapVolume,
            isVideoMuted: _isVideoMuted,
          ),
          if (_isVideoPaused)
            VideoPausedOverlay(
              onTapVideo: _onTapVideo,
              playCircleSize: 64.0,
              playIconSize: 100.0,
            ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 48),
            child: _VideoContentFeatures(
              video: _videoContent,
              isVideoMuted: _isVideoMuted,
              onTapViewProducts: _onTapViewProducts,
              onTapVolume: _onTapVolume,
              isInteractiveProgressIndicator: _videoContent.isInteractiveProgressIndicator,
            ),
          ),
        ],
      ),
    );
  }
}

class _VideoContentFeatures extends StatelessWidget {
  const _VideoContentFeatures({
    required this.video,
    required this.isVideoMuted,
    required this.onTapViewProducts,
    required this.onTapVolume,
    required this.isInteractiveProgressIndicator,
  });

  final VideoContent video;
  final bool isVideoMuted;
  final VoidCallback onTapViewProducts;
  final VoidCallback onTapVolume;
  final bool isInteractiveProgressIndicator;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _VideoContentTopFeatures(
          isVideoMuted: isVideoMuted,
          onTapVolume: onTapVolume,
          color: video.color,
          iconSize: video.iconSize,
          isInteractiveProgressIndicator: isInteractiveProgressIndicator,
        ),
        if (isInteractiveProgressIndicator != true)
          _VideoContentBottomFeatures(
            videoDescription: video.videoDescription,
            onTapViewProducts: onTapViewProducts,
            hideInteractiveButton: video.hideInteractiveButton ?? false,
          ),
      ],
    );
  }
}

class _VideoContentTopFeatures extends StatefulWidget {
  const _VideoContentTopFeatures({
    required this.onTapVolume,
    required this.isVideoMuted,
    required this.isInteractiveProgressIndicator,
    this.color,
    this.iconSize,
  });

  final VoidCallback onTapVolume;
  final bool isVideoMuted;
  final Color? color;
  final double? iconSize;
  final bool isInteractiveProgressIndicator;

  @override
  State<_VideoContentTopFeatures> createState() =>
      __VideoContentTopFeaturesState();
}

class __VideoContentTopFeaturesState extends State<_VideoContentTopFeatures> {
  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: widget.isInteractiveProgressIndicator == true
            ? [
                Spacer(),
                InkWell(
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xffffffff).withOpacity(0.6),
                      ),
                      child: Icon(
                        theme.closeIcon,
                        size: 32.0,
                        color: Colors.black,
                      ),
                    ),
                    onTap: () => Navigator.pop(context)),
              ]
            : [
                InkWell(
                  onTap: () => Navigator.pop(context),
                  child: Icon(
                    theme.iconBack,
                    size: 32.0,
                    color: widget.color ?? theme.color,
                  ),
                ),
                InkWell(
                  onTap: widget.onTapVolume,
                  child: Icon(
                    widget.isVideoMuted
                        ? theme.iconSpeakerMute
                        : theme.iconSpeakerHigh,
                    size: widget.iconSize ?? 60.0,
                    color: widget.color ?? theme.color,
                  ),
                )
              ],
      ),
    );
  }
}

class _VideoContentBottomFeatures extends StatelessWidget {
  const _VideoContentBottomFeatures({
    required this.videoDescription,
    required this.onTapViewProducts,
    this.hideInteractiveButton,
  });

  final String videoDescription;
  final VoidCallback onTapViewProducts;
  final bool? hideInteractiveButton;

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;
    const shadowValues = DTShadow(x: 0, y: 4, blur: 8, black: 0.3);
    final shadows = [
      Shadow(
        color: theme.shadows.withOpacity(shadowValues.black),
        blurRadius: shadowValues.blur,
        offset: Offset(shadowValues.x, shadowValues.y),
      ),
    ];
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (hideInteractiveButton == false) ...[
          Align(
            alignment: Alignment.centerRight,
            child: Column(children: [
              SideInteractiveButton(
                icon: theme.iconEye,
                text: 'ver\nprodutos',
                onPressed: onTapViewProducts,
              ),
            ]
                //.separated(SizedBox(height: tokens.spacingStack.md)),
                ),
          ),
        ],
        SizedBox(height: 40.0),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.0),
          child: Text(
            videoDescription,
            style: theme.videoDescriptionStyle.copyWith(
              color: theme.color,
              fontWeight: FontWeight.w400,
              height: 1.7,
              shadows: shadows,
            ),
          ),
        ),
      ],
    );
  }
}

class SideInteractiveButton extends StatelessWidget {
  const SideInteractiveButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onPressed,
  });

  final IconData icon;
  final String text;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;
    const shadowValues = DTShadow(x: 0, y: 4, blur: 8, black: 0.3);
    final shadows = [
      Shadow(
        color: theme.shadows.withOpacity(shadowValues.black),
        blurRadius: shadowValues.blur,
        offset: Offset(shadowValues.x, shadowValues.y),
      ),
    ];
    return InkWell(
      onTap: onPressed,
      child: SizedBox(
        width: 80.0,
        child: Column(
          children: [
            Icon(
              icon,
              size: 48.0,
              color: theme.color,
              shadows: shadows,
            ),
            Text(
              text,
              style: theme.sideInteractiveButtonStyle.copyWith(
                color: theme.color,
                fontWeight: FontWeight.w500,
                shadows: shadows,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class VideoContentPlayer extends StatelessWidget {
  const VideoContentPlayer({
    super.key,
    required this.onStartVideo,
    required this.onTapVideo,
    required this.volume,
    required this.videoUrl,
    required this.videoController,
    required this.isVideoProduct,
    required this.onTapVolume,
    required this.isVideoMuted,
  });

  final VoidCallback onStartVideo;
  final VoidCallback onTapVideo;
  final double volume;
  final String videoUrl;
  final VideoController videoController;
  final bool isVideoProduct;
  final VoidCallback onTapVolume;
  final bool isVideoMuted;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          InkWell(
            onTap: onTapVideo,
            child: AzzasVideo(
              controller: videoController,
              onStartVideo: onStartVideo,
              fullScreen: true,
              showButtonPlay: false,
              startVideo: true,
              looping: true,
              volume: volume,
              mediaUrl: videoUrl,
              playPauseEnabled: false,
            ),
          ),
          Positioned(
            bottom: isVideoProduct ? 12 : null,
            child: AnimatedBuilder(
              animation: videoController,
              child: VideoContentProgressIndicator(
                controller: videoController,
                isVideoProduct: isVideoProduct,
                onTapVolume: onTapVolume,
                isVideoMuted: isVideoMuted,
              ),
              builder: (context, child) {
                if (videoController.isInitialized) {
                  return child ?? const SizedBox.shrink();
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class VideoContentProgressIndicator extends StatefulWidget {
  const VideoContentProgressIndicator({
    required this.controller,
    required this.isVideoProduct,
    required this.isVideoMuted,
    required this.onTapVolume,
  });

  final VideoController controller;
  final bool isVideoProduct;
  final bool isVideoMuted;
  final VoidCallback onTapVolume;

  @override
  State<VideoContentProgressIndicator> createState() =>
      _VideoContentProgressIndicatorState();
}

class _VideoContentProgressIndicatorState
    extends State<VideoContentProgressIndicator>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    widget.controller.addListener(_onControllerUpdate);
    _animationController = AnimationController(
      vsync: this,
      duration: widget.controller.duration,
      value: widget.controller.videoProgress,
    )..addListener(() {
        setState(() {});
      });

    _animationController.repeat();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onControllerUpdate);
    _animationController.dispose();

    super.dispose();
  }

  void _onControllerUpdate() {
    if (!widget.controller.isPlaying) {
      _animationController.stop();
    } else {
      _animationController
        ..forward(from: widget.controller.videoProgress)
        ..repeat();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;
    final screenWidth = MediaQuery.of(context).size.width;

    return SizedBox(
      width: screenWidth,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return widget.isVideoProduct
              ? InteractiveProgressIndicator(
                  animationController: _animationController,
                  isVideoMuted: widget.isVideoMuted,
                  onTapVolume: widget.onTapVolume,
                  videoController: widget.controller,
                )
              : LinearProgressIndicator(
                  value: _animationController.value,
                  color: theme.progressIndicatorColor,
                  backgroundColor: theme.progressIndicatorBackgroundColor,
                );
        },
      ),
    );
  }
}

class VideoPausedOverlay extends StatelessWidget {
  const VideoPausedOverlay({
    super.key,
    required this.onTapVideo,
    required this.playCircleSize,
    required this.playIconSize,
  });

  final VoidCallback onTapVideo;
  final double playCircleSize;
  final double playIconSize;

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;
    return GestureDetector(
      onTap: onTapVideo,
      child: Container(
        decoration: BoxDecoration(
          color: theme.pausedOverlayDecoration,
        ),
        child: Center(
          child: Icon(
            theme.iconPlay,
            color: theme.color,
            size: playIconSize,
          ),
        ),
      ),
    );
  }
}

class VideoContent {
  final String? videoUrl;
  final String? videoThumb;
  final String videoTitle;
  final String videoDescription;
  final void Function()? navigateTo;
  final bool? hideInteractiveButton;
  final Color? color;
  final double? iconSize;
  final bool muteOnInit;
  /// Quando true modifica a UI.
  /// NavBar tem icone de close na esquerda.
  /// ProgressIndicator fica interativo, com icones de mute/unmute na direita
  final bool isInteractiveProgressIndicator;

  const VideoContent({
    this.videoUrl,
    this.videoThumb,
    required this.videoTitle,
    required this.videoDescription,
    this.navigateTo,
    this.hideInteractiveButton,
    this.color,
    this.iconSize,
    this.muteOnInit = false,
    this.isInteractiveProgressIndicator = false,
  });

  const VideoContent.empty({
    this.videoUrl,
    this.videoThumb,
    this.videoTitle = '',
    this.videoDescription = '',
    this.navigateTo,
    this.hideInteractiveButton = false,
    this.color,
    this.iconSize,
    this.muteOnInit = false,
    this.isInteractiveProgressIndicator = false,
  });
}

class InteractiveProgressBar extends StatelessWidget {
  final double progress;
  final ValueChanged<double> onSeek;
  final VoidCallback? onDragStart;
  final VoidCallback? onDragEnd;

  const InteractiveProgressBar({
    Key? key,
    required this.progress,
    required this.onSeek,
    this.onDragStart,
    this.onDragEnd,
  }) : super(key: key);

  void _handleInteraction(BuildContext context, Offset localPosition) {
    final box = context.findRenderObject() as RenderBox;
    final width = box.size.width;
    final newProgress = (localPosition.dx / width).clamp(0.0, 1.0);
    onSeek(newProgress);
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (details) {
        _handleInteraction(context, details.localPosition);
      },
      onHorizontalDragStart: (_) => onDragStart?.call(),
      onHorizontalDragUpdate: (details) {
        _handleInteraction(context, details.localPosition);
      },
      onHorizontalDragEnd: (_) => onDragEnd?.call(),
      child: Container(
        height: 24,
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            Container(
              height: 2,
              decoration: BoxDecoration(
                color: theme.interactiveProgressIndicatorBackgroundColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            FractionallySizedBox(
              widthFactor: progress,
              child: Container(
                height: 2,
                decoration: BoxDecoration(
                  color: theme.interactiveProgressIndicatorColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            Align(
              alignment: Alignment(progress * 2 - 1, 0),
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: theme.interactiveProgressIndicatorColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class InteractiveProgressIndicator extends StatelessWidget {
  const InteractiveProgressIndicator({
    required this.animationController,
    required this.isVideoMuted,
    required this.videoController,
    required this.onTapVolume,
    super.key,
  });

  final VoidCallback onTapVolume;
  final bool isVideoMuted;
  final AnimationController animationController;
  final VideoController videoController;

  String formatDuration(Duration duration) {
    var seconds = (duration.inMilliseconds % (60 * 1000)) / 1000;
    return '${duration.inMinutes}:${seconds.toStringAsFixed(0)}';
  }

  /// Este método calcula o tempo de progresso, baseado na animação e na duração total
  String formatVideoProgress(
      double animationValue, Duration totalVideoDuration) {
    double totalProgressInSeconds =
        animationValue * totalVideoDuration.inSeconds;
    int roundedSeconds = totalProgressInSeconds.round();
    int minutes = roundedSeconds ~/ 60;
    int seconds = roundedSeconds % 60;
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = seconds.toString().padLeft(2, '0');
    return "$formattedMinutes:$formattedSeconds";
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).videoContentTheme;

    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        child: Row(
          children: [
            Expanded(
              child: AnimatedBuilder(
                animation: animationController,
                builder: (context, child) {
                  return Column(
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              formatVideoProgress(
                                animationController.value,
                                animationController.duration!,
                              ),
                              style: theme.timeStyle,
                            ),
                            if (animationController.duration != null)
                              Text(
                                formatDuration(animationController.duration!),
                                style: theme.timeStyle,
                              ),
                          ]),
                      InteractiveProgressBar(
                        progress: videoController.videoProgress,
                        onSeek: (value) {
                          final newPosition = videoController.duration * value;
                          videoController.seekTo(newPosition);
                          animationController.value = value;
                        },
                        onDragStart: () {
                          animationController.stop();
                        },
                        onDragEnd: () {
                          if (videoController.isPlaying) {
                            animationController
                              ..forward(from: animationController.value)
                              ..repeat();
                          }
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
            SizedBox(width: 8),
            InkWell(
              onTap: () => onTapVolume(),
              child: Icon(
                isVideoMuted ? theme.iconSpeakerMute : theme.iconSpeakerHigh,
                size: 32,
                color: theme.iconsColors,
              ),
            )
          ],
        ),
      ),
    );
  }
}
