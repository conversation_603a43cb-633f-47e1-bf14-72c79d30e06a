import 'package:flutter/material.dart';

class AzzasVideoContentTheme {
  final IconData iconBack;
  final Color color;
  final IconData iconSpeakerMute;
  final IconData iconSpeakerHigh;
  final Color shadows;
  final IconData iconEye;
  final TextStyle videoDescriptionStyle;
  final TextStyle sideInteractiveButtonStyle;
  final Color progressIndicatorColor;
  final Color progressIndicatorBackgroundColor;
  final IconData iconPlay;
  final Color pausedOverlayDecoration;
  final IconData closeIcon;
  final TextStyle timeStyle;
  /// Aplicado sobre os icones de mute/unmute quando video de produto
  final Color iconsColors;
  final Color interactiveProgressIndicatorColor;
  final Color interactiveProgressIndicatorBackgroundColor;
  
  AzzasVideoContentTheme({
    required this.iconBack,
    required this.color,
    required this.iconSpeakerMute,
    required this.iconSpeakerHigh,
    required this.shadows,
    required this.iconEye,
    required this.videoDescriptionStyle,
    required this.sideInteractiveButtonStyle,
    required this.progressIndicatorColor,
    required this.progressIndicatorBackgroundColor,
    required this.iconPlay,
    required this.pausedOverlayDecoration,
    required this.closeIcon,
    required this.timeStyle,
    required this.iconsColors,
    required this.interactiveProgressIndicatorColor,
    required this.interactiveProgressIndicatorBackgroundColor,
  });
}
