import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

/// Componente `AzzasSmallAppBar`
class AzzasSmallAppBar extends StatelessWidget {
  /// Cor de fundo opcional para a AppBar. Se não for especificada, usa o valor do tema padrão.
  final Color? backgroundColor;

  /// Define se a AppBar deve permanecer "fixa" na tela quando rolada. O valor padrão é `true`.
  final bool pinned;

  /// Estilo de texto opcional para o título da AppBar. Caso não seja fornecido, será utilizado o estilo do tema padrão.
  final TextStyle? titleTextStyle;

  /// Estilo de texto opcional para o texto de suporte (texto secundário) da AppBar.
  /// Se não for fornecido, será utilizado o estilo do tema padrão.
  final TextStyle? supportTextStyle;

  /// O texto do título da AppBar. Este campo é obrigatório.
  final String title;

  /// Texto de suporte (ou texto secundário) exibido abaixo do título.
  /// O valor padrão é uma string vazia, e será exibido somente se não estiver vazio.
  final String supportText;

  /// Lista opcional de widgets que serão exibidos como ações no canto direito da AppBar.
  final List<Widget>? actions;

  /// Callback opcional para o botão de voltar, caso seja necessário. Se não fornecido, o botão de voltar pode não ser exibido.
  final VoidCallback? onBackButton;

  /// Altura da barra de ferramentas da AppBar. O valor padrão é `83`.
  final double toolbarHeight;

  /// Ícone opcional para o botão de voltar. Se não for fornecido, será utilizado o ícone padrão do tema.
  final IconData? backIcon;

  /// Padding interno da AppBar.
  final EdgeInsets? padding;

  /// Caso `true`, centralizará o texto em relação a tela dentro da AppBar.
  final bool centerTitle;

  /// Caso `true`, Transfomar em colunm dentro da AppBar.
  final bool isColunm;

  /// Se `true`, desabilita o uso de `FlexibleSpaceBar` e renderiza o conteúdo diretamente.
  final bool disableFlexibleSpace;

  const AzzasSmallAppBar({
    required this.title,
    this.backgroundColor,
    this.pinned = true,
    this.titleTextStyle,
    this.supportText = '',
    this.actions,
    this.supportTextStyle,
    this.onBackButton,
    this.toolbarHeight = 83,
    this.backIcon,
    this.padding,
    this.centerTitle = false,
    this.isColunm = false,
    this.disableFlexibleSpace = false,
    super.key,
  });

  Widget _buildContent(
      {required AppBarStyleTheme smallAppBarStyle,
      required BuildContext context}) {
    final textScale =
        TextScaleHelper.clampTextScale(context, maxScaleFactor: 1.0);

    if (centerTitle) {
      return Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  textScaler: textScale,
                  style: smallAppBarStyle.titleTextStyle,
                ),
                if (supportText.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    supportText,
                    textScaler: textScale,
                    style: smallAppBarStyle.supportTextStyle,
                  ),
                ],
              ],
            ),
          ),
          if (onBackButton != null)
            Align(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: onBackButton,
                child: Icon(
                  smallAppBarStyle.backIcon,
                  size: 32,
                ),
              ),
            ),
        ],
      );
    } else if (isColunm) {
      return Stack(
        children: [
          Align(
            alignment: Alignment.bottomLeft,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (onBackButton != null)
                  GestureDetector(
                    onTap: onBackButton,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        smallAppBarStyle.backIcon,
                        size: 32,
                      ),
                    ),
                  ),
                const SizedBox(height: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      textScaler: textScale,
                      style: smallAppBarStyle.titleTextStyle,
                    ),
                    if (supportText.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        supportText,
                        textScaler: textScale,
                        style: smallAppBarStyle.supportTextStyle,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (onBackButton != null)
            GestureDetector(
              onTap: onBackButton,
              child: Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Icon(
                  smallAppBarStyle.backIcon,
                  size: 32,
                ),
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title.isNotEmpty)
                  Text(
                    title,
                    textScaler: textScale,
                    style: smallAppBarStyle.titleTextStyle,
                  ),
                if (supportText.isNotEmpty) ...[
                  if (title.isNotEmpty) const SizedBox(height: 4),
                  Text(
                    supportText,
                    textScaler: textScale,
                    style: smallAppBarStyle.supportTextStyle,
                  ),
                ],
              ],
            ),
          ),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final smallAppBarStyle =
        AzzasThemeProvider.of(context).appBar.smallAppBarStyle.copyWith(
              backgroundColor: backgroundColor,
              titleTextStyle: titleTextStyle,
              supportTextStyle: supportTextStyle,
              backIcon: backIcon,
            );

    return disableFlexibleSpace
        ? Container(
            alignment: Alignment.bottomLeft,
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 24).copyWith(
                  top: 24,
                  bottom: 16,
                ),
            child: _buildContent(
              smallAppBarStyle: smallAppBarStyle,
              context: context,
            ),
          )
        : CustomAppBar(
            pinned: pinned,
            primary: true,
            backgroundColor: smallAppBarStyle.backgroundColor,
            toolbarHeight: toolbarHeight,
            actions: actions,
            child: FlexibleSpaceBar(
              collapseMode: CollapseMode.none,
              titlePadding: padding ??
                  const EdgeInsets.symmetric(horizontal: 24).copyWith(
                    top: 24,
                    bottom: 16,
                  ),
              title: _buildContent(
                smallAppBarStyle: smallAppBarStyle,
                context: context,
              ),
            ),
          );
  }
}
