import 'package:azzas_ui/azzas_theme/azzas_theme_provider.dart';
import 'package:azzas_ui/components/azzas_controller_carrousel/enums/azzas_controller_carrousel_size_enum.dart';
import 'package:flutter/material.dart';

class AzzasControllerCarrousel extends StatelessWidget {
  final int itemsCount;
  final int activeItemIndex;
  final int spaceBetweenDots;
  final AzzasControllerCarrouselSize size;
  final double? borderRadius;
  final Color? activeItemColor;
  final Color? secondaryColor;
  final MainAxisAlignment? mainAxisAlignment;
  final MainAxisSize? mainAxisSize;
  final void Function(int index)? onTapDot;

  const AzzasControllerCarrousel({
    super.key,
    this.itemsCount = 5,
    this.activeItemIndex = 1,
    this.spaceBetweenDots = 8,
    this.size = AzzasControllerCarrouselSize.small,
    this.borderRadius,
    this.activeItemColor,
    this.secondaryColor,
    this.mainAxisAlignment,
    this.mainAxisSize,
    this.onTapDot,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).controllerCarrousel;
    final activeItemColor = this.activeItemColor ?? theme.activeItemColor;
    final secondaryColor = this.secondaryColor ?? theme.secondaryColor;
    final borderRadius = this.borderRadius ?? theme.borderRadius;
    final height = _getHeight(size);

    return Row(
      mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
      mainAxisSize: mainAxisSize ?? MainAxisSize.max,
      children: List<Widget>.generate(
        itemsCount,
        (index) {
          final isActive = activeItemIndex == index;
          final width = isActive ? _geActiveWidth(size) : height;
          final color = isActive ? activeItemColor : secondaryColor;

          /// Stack para aumentar a área clicável do dot
          return Stack(
            children: [
              Positioned.fill(
                child: Center(
                  child: GestureDetector(
                    onTap: () => onTapDot?.call(index),
                    behavior: HitTestBehavior.translucent,
                    child: Container(
                      width: height + 7.0,
                      height: height + 2.0,
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.center,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  margin: EdgeInsets.only(right: spaceBetweenDots.toDouble()),
                  width: width,
                  height: height,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius:
                        BorderRadius.all(Radius.circular(borderRadius)),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  double _getHeight(AzzasControllerCarrouselSize size) {
    switch (size) {
      case AzzasControllerCarrouselSize.small:
        return 4.0;
      case AzzasControllerCarrouselSize.medium:
        return 8.0;
    }
  }

  double _geActiveWidth(AzzasControllerCarrouselSize size) {
    switch (size) {
      case AzzasControllerCarrouselSize.small:
        return 24.0;
      case AzzasControllerCarrouselSize.medium:
        return 40.0;
    }
  }
}
