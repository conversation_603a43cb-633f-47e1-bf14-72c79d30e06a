import 'package:azzas_ui/azzas_ui.dart';

import 'package:flutter/material.dart';

enum SnackBarPosition { top, bottom }

/// Um componente personalizado de SnackBar que permite configurações adicionais,
/// como tamanho, status, estilo da mensagem e ícones personalizados.
class AzzasSnackBar {
  /// Cria um [AzzasSnackBar].
  static void show({
    required BuildContext context,
    required String message,
    SnackBarSize size = SnackBarSize.medium,
    SnackBarStatus status = SnackBarStatus.success,
    TextStyle? messageStyle,
    Color? backgroundColor,
    Widget? leading,
    Widget? trailing,
    EdgeInsets? padding,
    Duration duration = const Duration(seconds: 2),
    Curve animationCurve = Curves.easeInOut,
    SnackBarPosition position = SnackBarPosition.bottom,
    EdgeInsets? customPosition,
    OverlayState? overlayState,
    TextOverflow textOverflow = TextOverflow.ellipsis,
    int maxLines = 2,
  }) {
    final overlay = overlayState ?? Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) {
        return _ToastOverlay(
          message: message,
          size: size,
          status: status,
          messageStyle: messageStyle,
          backgroundColor: backgroundColor,
          leading: leading,
          trailing: trailing,
          padding: padding,
          duration: duration,
          animationCurve: animationCurve,
          position: position,
          customPosition: customPosition,
          textOverflow: textOverflow,
          maxLines: maxLines,
        );
      },
    );

    overlay.insert(overlayEntry);

    Future.delayed(duration + const Duration(milliseconds: 600), () {
      overlayEntry.remove();
    });
  }
}

/// Um widget interno que exibe o conteúdo do Toast com animação.
class _ToastOverlay extends StatefulWidget {
  final String message;
  final SnackBarSize size;
  final SnackBarStatus status;
  final TextStyle? messageStyle;
  final Color? backgroundColor;
  final Widget? leading;
  final Widget? trailing;
  final EdgeInsets? padding;
  final Duration duration;
  final Curve animationCurve;
  final SnackBarPosition position;
  final EdgeInsets? customPosition;
  final TextOverflow textOverflow;
  final int maxLines;

  const _ToastOverlay({
    required this.message,
    required this.size,
    required this.status,
    this.messageStyle,
    this.backgroundColor,
    this.leading,
    this.trailing,
    this.padding,
    required this.duration,
    required this.animationCurve,
    required this.position,
    this.customPosition,
    required this.textOverflow,
    required this.maxLines,
  });

  @override
  State<_ToastOverlay> createState() => _ToastOverlayState();
}

class _ToastOverlayState extends State<_ToastOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _slideAnimation = Tween<Offset>(
      begin: _getStartOffset(),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.animationCurve,
    ));

    _controller.forward();

    Future.delayed(widget.duration, () {
      _controller.reverse();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Offset _getStartOffset() {
    switch (widget.position) {
      case SnackBarPosition.top:
        return const Offset(0, -3);
      case SnackBarPosition.bottom:
      default:
        return const Offset(0, 3);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).snackBar;

    return Positioned(
      top: widget.position == SnackBarPosition.top
          ? (widget.customPosition?.top ?? 140)
          : null,
      bottom: widget.position == SnackBarPosition.bottom
          ? (widget.customPosition?.bottom ?? 140)
          : null,
      left: widget.customPosition?.left ?? 24,
      right: widget.customPosition?.right ?? 24,
      child: SlideTransition(
        position: _slideAnimation,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: widget.padding ?? theme.padding,
            decoration: BoxDecoration(
              color: widget.backgroundColor ??
                  widget.status.color(
                    theme.successColor,
                    theme.dangerColor,
                  ),
              borderRadius: BorderRadius.circular(theme.borderRadius),
            ),
            child: _ToastContent(
              message: widget.message,
              size: widget.size,
              messageStyle: widget.messageStyle,
              leading: widget.leading,
              trailing: widget.trailing,
              textOverflow: widget.textOverflow,
              maxLines: widget.maxLines,
            ),
          ),
        ),
      ),
    );
  }
}

/// Conteúdo interno do Toast.
class _ToastContent extends StatelessWidget {
  final String message;
  final SnackBarSize size;
  final TextStyle? messageStyle;
  final Widget? leading;
  final Widget? trailing;
  final TextOverflow textOverflow;
  final int maxLines;

  const _ToastContent({
    required this.message,
    required this.size,
    this.messageStyle,
    this.leading,
    this.trailing,
    required this.textOverflow,
    required this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).snackBar;

    return SizedBox(
      height: size.value(
        theme.mediumSize,
        theme.smallSize,
      ),
      child: Row(
        mainAxisAlignment: leading != null || trailing != null
            ? MainAxisAlignment.start
            : MainAxisAlignment.center,
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Align(
              alignment: leading != null || trailing != null
                  ? Alignment.centerLeft
                  : Alignment.center,
              child: Text(
                message,
                style: messageStyle ?? theme.messageStyle,
                overflow: textOverflow,
                maxLines: maxLines,
                textScaler: TextScaleHelper.clampTextScale(context,
                    maxScaleFactor: 1.5),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}
