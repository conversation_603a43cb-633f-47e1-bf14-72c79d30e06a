import 'package:azzas_ui/azzas_ui.dart';

import 'package:flutter/material.dart';

class AzzasSuggestionsProductCard extends StatelessWidget {
  final AzzasProductCardProductParams product;
  final String? addToBagText;
  final String? saveToWishlistText;
  final TextStyle? addToBagTextStyle;
  final EdgeInsetsGeometry? padding;
  final double? imageHeight;
  final double? imageWidth;
  final AzzasProductInfoTagsAlignment? tagsAlignment;
  final EdgeInsetsGeometry? productInfoPadding;
  final bool? showPrice;
  final Widget? complementarWidget;
  final void Function()? onTapImage;
  final void Function()? onTapAddToBag;
  final void Function()? onTapSaveToWishlist;

  final double? midSpacing;

  const AzzasSuggestionsProductCard({
    super.key,
    required this.product,
    this.addToBagText,
    this.saveToWishlistText,
    this.addToBagTextStyle,
    this.padding,
    this.imageHeight,
    this.imageWidth,
    this.tagsAlignment,
    this.productInfoPadding,
    this.onTapImage,
    this.onTapAddToBag,
    this.onTapSaveToWishlist,
    this.midSpacing,
    this.showPrice,
    this.complementarWidget,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).suggestionsProductCard;

    final addToBagTheme = theme.addToBagButton;
    final effectivePadding = padding ?? theme.padding;
    final effectiveImageHeight = imageHeight ?? theme.imageHeight;
    final effectiveImageWidth = imageWidth ?? theme.imageWidth;
    final effectiveProductInfoPadding =
        productInfoPadding ?? theme.productInfoPadding;
    final effectiveAddToBagTextStyle =
        addToBagTextStyle ?? addToBagTheme.textStyle;
    final effectiveMidSpacing = midSpacing ?? theme.midSpacing;

    return Padding(
      padding: effectivePadding,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AzzasProductImage(
              heigth: effectiveImageHeight,
              width: effectiveImageWidth,
              onTap: onTapImage,
              imageUrl: product.imageUrl,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Padding(
                      padding: effectiveProductInfoPadding,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AzzasProductInfo(
                            params: AzzasProductInfoParams(
                              productTitle: product.name,
                              currentPrice: product.currentPrice,
                              fullPrice: product.fullPrice,
                              showPrice: showPrice ?? false,
                              complementarWidget: complementarWidget,
                            ),
                          ),
                          SizedBox(height: effectiveMidSpacing),
                          Wrap(
                            spacing: 24.0,
                            runSpacing: 4.0,
                            children: [
                              InkWell(
                                onTap: onTapAddToBag,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Text(
                                      addToBagText ?? 'Incluir na mochila',
                                      style: effectiveAddToBagTextStyle,
                                    ),
                                    const SizedBox(width: 4.0),
                                    Icon(
                                      addToBagTheme.icon,
                                      size: addToBagTheme.iconSize,
                                      color: addToBagTheme.iconColor,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: effectiveMidSpacing),
                          Wrap(
                            spacing: 24.0,
                            runSpacing: 4.0,
                            children: [
                              InkWell(
                                onTap: onTapSaveToWishlist,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Text(
                                      saveToWishlistText ?? 'Salvar em desejos',
                                      style: effectiveAddToBagTextStyle,
                                    ),
                                    const SizedBox(width: 4.0),
                                    Icon(
                                      addToBagTheme.icon,
                                      size: addToBagTheme.iconSize,
                                      color: addToBagTheme.iconColor,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
