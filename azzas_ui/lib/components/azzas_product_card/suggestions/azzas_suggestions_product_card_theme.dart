import 'package:azzas_ui/components/azzas_product_card/common/azzas_product_card_base_theme.dart';
import 'package:azzas_ui/components/azzas_product_card/common/azzas_product_card_button_base_theme.dart';
import 'package:azzas_ui/components/azzas_product_info/enums/azzas_product_info_tags_alignment.dart';
import 'package:flutter/material.dart';

class AzzasSuggestionsProductCardTheme extends AzzasProductCardBaseTheme {
  final AzzasSuggestionsProductCardAddToBagButtonTheme addToBagButton;
  final double midSpacing;

  const AzzasSuggestionsProductCardTheme({
    required EdgeInsetsGeometry padding,
    required double imageHeight,
    required double imageWidth,
    required AzzasProductInfoTagsAlignment tagsAlignment,
    required EdgeInsetsGeometry productInfoPadding,
    required this.addToBagButton,
    this.midSpacing = 0.0,
  }) : super(
          padding: padding,
          imageHeight: imageHeight,
          imageWidth: imageWidth,
          tagsAlignment: tagsAlignment,
          productInfoPadding: productInfoPadding,
        );
}

class AzzasSuggestionsProductCardAddToBagButtonTheme
    extends AzzasProductCardButtonThemeBase {
  const AzzasSuggestionsProductCardAddToBagButtonTheme({
    required TextStyle textStyle,
    required IconData icon,
    required double iconSize,
    required Color iconColor,
  }) : super(
          textStyle: textStyle,
          icon: icon,
          iconSize: iconSize,
          iconColor: iconColor,
          expandedAddToBag: true
        );
}
