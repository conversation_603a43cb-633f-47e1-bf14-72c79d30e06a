import 'package:azzas_ui/azzas_ui.dart';
import 'package:azzas_ui/components/azzas_product_card/common/widgets/azzas_product_card_remove_button.dart';

import 'package:flutter/material.dart';

class AzzasCheckoutProductCard extends StatelessWidget {
  final AzzasProductCardProductParams product;
  final String? saveProductText;
  final TextStyle? saveProductTextStyle;
  final EdgeInsetsGeometry? padding;
  final double? imageHeight;
  final double? imageWidth;
  final AzzasProductInfoTagsAlignment? tagsAlignment;
  final EdgeInsetsGeometry? productInfoPadding;
  final EdgeInsetsGeometry? sizeButtonsPadding;
  final double? sizeButtonsGap;
  final void Function()? onTapImage;
  final void Function()? onTapSizeButton;
  final void Function()? onTapProductQuantity;
  final void Function()? onTapSaveProduct;
  final void Function()? onTapRemoveProduct;
  final bool? showProductSize;
  final bool? showProductQuantity;
  final bool? showRemoveButton;
  final void Function()? onTapCard;

  const AzzasCheckoutProductCard({
    super.key,
    required this.product,
    this.saveProductText,
    this.saveProductTextStyle,
    this.padding,
    this.imageHeight,
    this.imageWidth,
    this.tagsAlignment,
    this.productInfoPadding,
    this.sizeButtonsPadding,
    this.sizeButtonsGap,
    this.onTapImage,
    this.onTapSizeButton,
    this.onTapProductQuantity,
    this.onTapSaveProduct,
    this.onTapRemoveProduct,
    this.onTapCard,
    this.showProductSize = true,
    this.showProductQuantity = true,
    this.showRemoveButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).checkoutProductCard;
    final removeButtonTheme = theme.removeButton;

    final saveButtonTheme = theme.saveButton;
    final effectivePadding = padding ?? theme.padding;
    final effectiveImageHeight = imageHeight ?? theme.imageHeight;
    final effectiveImageWidth = imageWidth ?? theme.imageWidth;
    final effectiveProductInfoPadding =
        productInfoPadding ?? theme.productInfoPadding;
    final effectiveTagsAlignment = tagsAlignment ?? theme.tagsAlignment;
    final effectiveSizeButtonsPadding =
        sizeButtonsPadding ?? theme.sizeButtonsPadding;
    final effectiveSizeButtonsGap = sizeButtonsGap ?? theme.sizeButtonsGap;
    final effectiveSaveProductTextStyle =
        saveProductTextStyle ?? saveButtonTheme.saveProductTextStyle;
    final isDisabled = (product.getQuantity.toString()) == '0' ? true : false;

    return Padding(
      padding: effectivePadding,
      child: InkWell(
        onTap: onTapCard,
        // onTap: NavigatorDynamic.call('pdp/${widget.bagProducts[index].productId}'),
        child: IntrinsicHeight(
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            AzzasProductImage(
              heigth: effectiveImageHeight,
              width: effectiveImageWidth,
              onTap: onTapImage,
              imageUrl: product.imageUrl,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: effectiveProductInfoPadding,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AzzasProductInfo(
                            params: AzzasProductInfoParams(
                              productTitle: product.name,
                              currentPrice: product.currentPrice,
                              fullPrice: product.fullPrice,
                              estimatedDelivery: product.estimatedDelivery,
                              tagsAlignment: effectiveTagsAlignment,
                              tagsBuilder: product.tagsBuilder,
                              showPrice: product.isAvailable,
                            ),
                          ),
                          Padding(
                            padding: effectiveSizeButtonsPadding,
                            child: Wrap(
                              spacing: effectiveSizeButtonsGap,
                              runSpacing: 8.0,
                              children: [
                                if (showProductQuantity == true &&
                                    product.getQuantity > 0)
                                  AzzasSkuSelector(
                                    isCrossed: isDisabled,
                                    isDisabled: isDisabled,
                                    text: '${product.getQuantity}un.',
                                    onTap: onTapProductQuantity,
                                  ),
                                if (showProductSize == true &&
                                    product.size != null &&
                                    product.size!.trim().length < 7)
                                  AzzasSkuSelector(
                                    isCrossed: isDisabled,
                                    text: product.size?.trim() ?? '',
                                    onTap: onTapSizeButton,
                                  ),
                              ],
                            ),
                          ),
                          Wrap(
                            spacing: 16.0,
                            runSpacing: 40.0,
                            children: [
                              Visibility(
                                visible: onTapSaveProduct != null,
                                child: InkWell(
                                  onTap: onTapSaveProduct,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          saveProductText ??
                                              "Salvar para depois",
                                          style: effectiveSaveProductTextStyle,
                                        ),
                                      ),
                                      const SizedBox(width: 4.0),
                                      Icon(
                                        saveButtonTheme.icon,
                                        size: saveButtonTheme.iconSize,
                                        color: saveButtonTheme.iconColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (!product.isAvailable &&
                                  showRemoveButton == true)
                                buildRemoveButton(
                                  theme: removeButtonTheme,
                                  onTapRemoveProduct: onTapRemoveProduct,
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ]),
        ),
      ),
    );
  }
}
