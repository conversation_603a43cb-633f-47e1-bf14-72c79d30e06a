import 'package:azzas_ui/azzas_ui.dart';
import 'package:azzas_ui/components/azzas_product_card/common/widgets/azzas_product_card_remove_button.dart';

import 'package:flutter/material.dart';

class AzzasWishlistProductCard extends StatelessWidget {
  final AzzasProductCardProductParams product;
  final String? addToBagText;
  final TextStyle? addToBagTextStyle;
  final EdgeInsetsGeometry? padding;
  final double? imageHeight;
  final double? imageWidth;
  final AzzasProductInfoTagsAlignment? tagsAlignment;
  final EdgeInsetsGeometry? productInfoPadding;
  final bool? showPrice;
  final Widget? complementarWidget;
  final void Function()? onTapImage;
  final void Function()? onTapAddToBag;
  final void Function()? onTapRemoveProduct;

  final double? midSpacing;

  const AzzasWishlistProductCard({
    super.key,
    required this.product,
    this.addToBagText,
    this.addToBagTextStyle,
    this.padding,
    this.imageHeight,
    this.imageWidth,
    this.tagsAlignment,
    this.productInfoPadding,
    this.onTapImage,
    this.onTapAddToBag,
    this.onTapRemoveProduct,
    this.midSpacing,
    this.showPrice,
    this.complementarWidget,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).wishlistProductCard;

    final removeButtonTheme = theme.removeButton;

    final addToBagTheme = theme.addToBagButton;
    final effectivePadding = padding ?? theme.padding;
    final effectiveImageHeight = imageHeight ?? theme.imageHeight;
    final effectiveImageWidth = imageWidth ?? theme.imageWidth;
    final effectiveProductInfoPadding =
        productInfoPadding ?? theme.productInfoPadding;
    final effectiveTagsAlignment = tagsAlignment ?? theme.tagsAlignment;
    final effectiveAddToBagTextStyle =
        addToBagTextStyle ?? addToBagTheme.textStyle;
    final effectiveMidSpacing = midSpacing ?? theme.midSpacing;

    final Widget addToBagWidget = Text(
      addToBagText ?? "Adicionar à sacola",
      style: effectiveAddToBagTextStyle,
    );
    return Padding(
      padding: effectivePadding,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AzzasProductImage(
              heigth: effectiveImageHeight,
              width: effectiveImageWidth,
              onTap: onTapImage,
              imageUrl: product.imageUrl,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Padding(
                      padding: effectiveProductInfoPadding,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AzzasProductInfo(
                            params: AzzasProductInfoParams(
                              productTitle: product.name,
                              currentPrice: product.currentPrice,
                              fullPrice: product.fullPrice,
                              estimatedDelivery: product.estimatedDelivery,
                              tagsAlignment: effectiveTagsAlignment,
                              tagsBuilder: product.tagsBuilder,
                              showPrice: showPrice ?? false,
                              complementarWidget: complementarWidget,
                            ),
                          ),
                          SizedBox(height: effectiveMidSpacing),
                          Wrap(
                            spacing: 24.0,
                            runSpacing: 4.0,
                            children: [
                              InkWell(
                                onTap: onTapAddToBag,
                                child: Row(
                                  mainAxisSize: addToBagTheme.expandedAddToBag
                                      ? MainAxisSize.max
                                      : MainAxisSize.min,
                                  children: [
                                    addToBagTheme.expandedAddToBag
                                        ? Expanded(child: addToBagWidget)
                                        : addToBagWidget,
                                    const SizedBox(width: 4.0),
                                    Icon(
                                      addToBagTheme.icon,
                                      size: addToBagTheme.iconSize,
                                      color: addToBagTheme.iconColor,
                                    ),
                                  ],
                                ),
                              ),
                              buildRemoveButton(
                                theme: removeButtonTheme,
                                onTapRemoveProduct: onTapRemoveProduct,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
