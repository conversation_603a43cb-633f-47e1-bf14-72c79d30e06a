import 'package:azzas_ui/components/azzas_product_card/common/azzas_product_card_button_base_theme.dart';
import 'package:flutter/material.dart';

class AzzasProductCardRemoveButtonTheme
    extends AzzasProductCardButtonThemeBase {
  final bool reverseItems;

  const AzzasProductCardRemoveButtonTheme({
    required TextStyle textStyle,
    required IconData icon,
    required double iconSize,
    required Color iconColor,
    required this.reverseItems,
  }) : super(
          textStyle: textStyle,
          icon: icon,
          iconSize: iconSize,
          iconColor: iconColor,
          expandedAddToBag: false,
        );
}
