import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

/// Esse é o widget padrão de HeaderClock dos apps Azzas. Caso precise de um `HeaderClock` mais customizado,
/// é recomendado utilizar um builder específico para a marca.
class AzzasHeaderClock extends StatefulWidget {
  const AzzasHeaderClock({
    super.key,
    this.title,
    this.coupon,
    this.couponText,
    this.backgroundColor,
    this.foregroundColor,
    this.couponButtonTextColor,
    this.applyCouponButtonLocation = AzzasClockButtonPosition.afterCoupon,
    this.subtitle,
    this.onTap,
    this.showCronometer = true,
    this.isCouponApplied = false,
    this.isLoadingCoupon = false,
    this.onTapButton,
    required this.remainingTime,
  });

  final String? title;
  final String? coupon;
  final String? couponText;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? couponButtonTextColor;
  final AzzasClockButtonPosition applyCouponButtonLocation;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool showCronometer;
  final bool isCouponApplied;
  final bool isLoadingCoupon;
  final VoidCallback? onTapButton;
  final Duration remainingTime;

  @override
  State<AzzasHeaderClock> createState() => _AzzasHeaderClockState();
}

class _AzzasHeaderClockState extends State<AzzasHeaderClock> {
  ClockPromotionController clockController = ClockPromotionController();

  @override
  void initState() {
    super.initState();
    // scheduleMicrotask(() => _logPromotionEvents(isViewPromotion: true));
  }

  ///TODO: Checar eventos
  // _logPromotionEvents({isViewPromotion = false}) {
  // final inheritedMetadata = AnalyticsMetadataProvider.metadataOf(context);
  // String? screenName = inheritedMetadata?['screen_name'].toLowerCase();
  // if (isViewPromotion) {
  //   try {
  //     dispatchViewPromotionEvent(
  //       context,
  //       screenClass: 'reloginho',
  //       screenName: screenName,
  //       promotionName: widget.bannerNameGA4?.promotionName,
  //       creativeName: widget.bannerNameGA4?.creativeName,
  //       creativeSlot: 'Banner sem imagem',
  //       promotionId: "reloginho-$screenName",
  //     );
  //   } catch (e) {
  //     debugPrint("Error on dispatchViewPromotionEvent: $e");
  //   }
  // } else {
  //   try {
  //     dispatchSelectPromotionEvent(
  //       screenClass: 'reloginho',
  //       screenName: screenName,
  //       promotionName: widget.bannerNameGA4?.promotionName,
  //       creativeName: widget.bannerNameGA4?.creativeName,
  //       creativeSlot: 'Banner sem imagem',
  //       promotionId: "reloginho-$screenName",
  //     );
  //   } catch (e) {
  //     debugPrint("Error on dispatchSelectPromotionEvent: $e");
  //   }
  // }
  // }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).clocks.headerClockTheme;
    final coupon = widget.coupon;
    final hasCoupon = coupon != null && coupon.trim().isNotEmpty;
    final applyCouponButtonIsBelowClock =
        widget.applyCouponButtonLocation == AzzasClockButtonPosition.underClock;

    final foregroundColor = widget.foregroundColor ?? theme.foregroundColor;
    final backgroundColor = widget.backgroundColor ?? theme.backgroundColor;

    final padding = theme.padding;
    final titleStyle = theme.titleTextStyle;
    final subtitleTextStyle = theme.subtitleTextStyle;

    return Material(
      color: backgroundColor,
      child: InkWell(
        onTap: widget.onTap ?? () {},
        child: Container(
          width: double.infinity,
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (widget.title != null)
                Text(
                  widget.title!,
                  style: titleStyle.copyWith(
                      color: widget.foregroundColor, height: 1.0),
                  textAlign: TextAlign.center,
                ),
              if (widget.subtitle != null)
                Text(
                  widget.subtitle!.toString(),
                  style: subtitleTextStyle.copyWith(
                    color: widget.foregroundColor,
                    decoration: TextDecoration.underline,
                    decorationColor: widget.foregroundColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: applyCouponButtonIsBelowClock ||
                        widget.showCronometer != true
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.spaceAround,
                children: [
                  if (widget.showCronometer == true)
                    AzzasScaleDownFlexible(
                      child: AzzasClockTime(
                        remainingTime: widget.remainingTime,
                        numbersTextStyle: theme.timerTextStyle.copyWith(
                          color: foregroundColor,
                        ),
                        timeToColonSpacing: 4.0,
                      ),
                    ),
                  if (hasCoupon)
                    ..._buildCouponText(
                      coupon: coupon,
                      theme: theme,
                      foregroundColor: foregroundColor,
                    ),
                ].separated(SizedBox(width: 12.0)),
              ),
              if (applyCouponButtonIsBelowClock &&
                  hasCoupon &&
                  widget.showCronometer == true) ...[
                SizedBox(height: 4.0),
                _buildApplyCouponButton(
                  coupon,
                  expanded: true,
                  theme: theme,
                ),
              ],
            ].separated(SizedBox(height: 4.0)),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildCouponText({
    required String coupon,
    required Color foregroundColor,
    required AzzasHeaderClockTheme theme,
  }) {
    final isAfterCoupon = widget.applyCouponButtonLocation ==
        AzzasClockButtonPosition.afterCoupon;
    final showCronometer = widget.showCronometer;
    final couponNameTextStyle = theme.couponNameTextStyle.copyWith(
      color: foregroundColor,
    );
    final couponLabelTextStyle = theme.couponLabelTextStyle.copyWith(
      color: foregroundColor,
    );

    return [
      AzzasScaleDownFlexible(
        child: showCronometer
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    coupon,
                    style: couponNameTextStyle,
                  ),
                  Text(
                    'cupom',
                    style: couponLabelTextStyle,
                  ),
                ],
              )
            : Row(
                children: [
                  Text(
                    'cupom',
                    style: couponLabelTextStyle,
                  ),
                  SizedBox(width: 8.0),
                  Text(
                    coupon,
                    style: couponNameTextStyle,
                  ),
                ],
              ),
      ),
      if (isAfterCoupon || !showCronometer)
        AzzasScaleDownFlexible(
          child: _buildApplyCouponButton(
            coupon,
            expanded: isAfterCoupon,
            theme: theme,
          ),
        ),
    ];
  }

  Widget _buildApplyCouponButton(
    String coupon, {
    bool? expanded,
    required AzzasHeaderClockTheme theme,
  }) {
    return AzzasApplyCouponButton(
      coupon: coupon,
      expanded: expanded,
      minimumSize: const Size(116, 0),
      textStyle: theme.couponButtonTextStyle?.copyWith(
        color: widget.couponButtonTextColor,
      ),
      child: Text(
        widget.couponText ?? 'Aplicar cupom',
      ),
      isLoading: widget.isLoadingCoupon,
      foregroundColor: widget.foregroundColor ?? theme.foregroundColor,
      isCouponApplied: widget.isCouponApplied,
      onPressed: widget.onTapButton,
    );
  }
}
