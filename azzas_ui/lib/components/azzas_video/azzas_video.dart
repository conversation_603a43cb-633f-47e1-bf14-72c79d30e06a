import 'dart:async';

import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

enum VideoType { asset, network }

typedef AzzasVideoProgressIndicatorBuilder = Widget Function(
    VideoPlayerController controller);

typedef AzzasVideoPlayPauseButtonBuilder = Widget Function(
    AnimationController controller);

class AzzasVideo extends StatefulWidget {
  final VideoController? controller;
  final String mediaUrl;
  final VideoType videoType;
  final VoidCallback? onEndVideo;
  final VoidCallback? onStartVideo;
  final bool startVideo;
  final bool fullScreen;
  final bool allowPauseButton;
  final bool looping;
  final bool showButtonPlay;
  final double volume;
  final bool centerVideo;
  final bool showOverlayGradient;
  final Color? backgroundColor;
  final double? height, width;
  final WidgetBuilder? loadingBuilder;
  final bool listenVisibilityChanges;
  final bool? showSnackbarOnError;
  final bool showProgressIndicator;
  final bool playPauseEnabled;
  final AzzasVideoProgressIndicatorBuilder? progressIndicatorBuilder;
  final bool? keepAlive;
  final bool? autoHeight;
  final bool mixWithOthers;
  final bool autoWidth;
  final AzzasVideoPlayPauseButtonBuilder? playPauseButtonBuilder;
  final AzzasPlayPauseButtonPosition playPauseButtonPosition;

  const AzzasVideo({
    super.key,
    this.mediaUrl = '',
    this.videoType = VideoType.network,
    this.onEndVideo,
    this.onStartVideo,
    this.startVideo = false,
    this.fullScreen = false,
    this.looping = true,
    this.showButtonPlay = true,
    this.volume = 1,
    this.controller,
    this.showOverlayGradient = true,
    this.backgroundColor,
    this.centerVideo = false,
    this.loadingBuilder,
    this.listenVisibilityChanges = false,
    this.allowPauseButton = true,
    this.height,
    this.width,
    this.showSnackbarOnError,
    this.showProgressIndicator = false,
    this.playPauseEnabled = true,
    this.progressIndicatorBuilder,
    this.keepAlive,
    this.autoHeight,
    this.mixWithOthers = false,
    this.autoWidth = false,
    this.playPauseButtonBuilder,
    this.playPauseButtonPosition = AzzasPlayPauseButtonPosition.bottomLeft,
  });

  @override
  State<AzzasVideo> createState() => _AzzasVideoState();
}

class _AzzasVideoState extends State<AzzasVideo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _overlayAnimationController;
  bool _playVideoWhenVisible = true;
  late final VideoController _fallbackController = VideoController();
  Duration? _lastPosition;

  VideoController get _controller => widget.controller ?? _fallbackController;
  bool muted = false;
  var switchDelay = const Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();

    _overlayAnimationController = AnimationController(
      vsync: this,
      duration: switchDelay,
      value: widget.startVideo ? 0 : 1,
    );

    _controller._initialize(_createVideoOptions()).then((value) {
      setState(() {});
      final videoPlayerController = _controller.videoPlayerController;
      if (videoPlayerController != null && widget.onStartVideo != null) {
        widget.onStartVideo!();
      }
      if (videoPlayerController != null && widget.onEndVideo != null) {
        videoPlayerController.addListener(() {
          final position = videoPlayerController.value.position;
          final duration = videoPlayerController.value.duration;
          if (position == duration && position != _lastPosition) {
            widget.onEndVideo!();
          }
          _lastPosition = position;
        });
      }
    }).catchError((err) {
      if (widget.onEndVideo is Function) widget.onEndVideo!();
      if (mounted && (widget.showSnackbarOnError ?? false)) {
        //TODO: Snackbar
      }
    });
  }

  VideoOptions _createVideoOptions() {
    return VideoOptions(
      mediaUrl: widget.mediaUrl,
      videoType: widget.videoType,
      fullScreen: widget.fullScreen,
      looping: widget.looping,
      startVideo: widget.startVideo,
      volume: widget.volume,
      mixWithOthers: widget.mixWithOthers,
    );
  }

  @override
  void dispose() {
    _overlayAnimationController.dispose();
    if (widget.controller == null) {
      // Não faz sentido chamar o dispose se o controller não foi utilizado,
      // por isso só é chamado se o dev não passou o [widget.controller]
      _fallbackController.dispose();
    }
    super.dispose();
  }

  Widget makeProgressIndicator() {
    Widget component = const SizedBox.shrink();
    if (widget.showProgressIndicator == false) return component;

    if (widget.progressIndicatorBuilder != null) {
      component = widget.progressIndicatorBuilder!(
        _controller.videoPlayerController!,
      );
    } else if (widget.progressIndicatorBuilder == null) {
      component = AzzasVideoProgressIndicator(
        controller: _controller.videoPlayerController!,
        switchDelay: switchDelay,
      );
    }

    return Positioned(
      left: 0,
      right: 0,
      bottom: 1,
      child: component,
    );
  }

  @override
  Widget build(BuildContext context) {
    final Widget child;
    if (_controller.isInitialized) {
      child = Container(
        alignment: Alignment.topCenter,
        child: buildVideo(context),
      );
    } else {
      child = widget.loadingBuilder?.call(context) ??
          const Center(child: AzzasSpinner());
    }

    return AnimatedSwitcher(
      duration: switchDelay,
      child: child,
    );
  }

  Widget buildVideo(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dimensionProportionWidth = widget.width == null
        ? screenSize.width / (widget.width ?? _controller._size.width)
        : 1.0;
    final dimensionProportionHeight = widget.height == null
        ? screenSize.height / (widget.height ?? _controller._size.height)
        : 1.0;

    return SizedBox(
      height: widget.autoHeight == true
          ? null
          : widget.height ??
              _controller._size.height * dimensionProportionHeight,
      width: widget.autoWidth
          ? null
          : widget.width ?? _controller._size.width * dimensionProportionWidth,
      child: VisibilityDetector(
        key: Key(widget.mediaUrl),
        onVisibilityChanged:
            widget.listenVisibilityChanges ? _onVisibilityChanged : null,
        child: GestureDetector(
          onTap: widget.playPauseEnabled ? playOrPauseVideo : null,
          child: Stack(
            fit: widget.fullScreen ? StackFit.expand : StackFit.loose,
            alignment: widget.playPauseButtonPosition ==
                    AzzasPlayPauseButtonPosition.center
                ? Alignment.center
                : Alignment.bottomLeft,
            children: [
              if (widget.centerVideo)
                Center(child: buildVideoPlayer())
              else
                buildVideoPlayer(),
              if (widget.showOverlayGradient)
                Positioned.fill(
                  child: buildOverlayGradient(),
                ),
              widget.showButtonPlay ? _buildPlayPauseIcon() : const SizedBox(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlayPauseIcon() {
    final _playPauseButton =
        widget.playPauseButtonBuilder?.call(_overlayAnimationController) ??
            AnimatedIcon(
              progress: _overlayAnimationController,
              icon: AnimatedIcons.pause_play,
            );

    if (widget.playPauseButtonPosition ==
        AzzasPlayPauseButtonPosition.bottomLeft) {
      return Positioned(
        bottom: 8,
        left: 8,
        child: _playPauseButton,
      );
    }

    return Center(
      child: _playPauseButton,
    );
  }

  void _onVisibilityChanged(VisibilityInfo info) {
    if (mounted) {
      if (info.visibleFraction < 0.50) {
        _pause();
      } else if (_playVideoWhenVisible) {
        _play();
      }
    }
  }

  void playOrPauseVideo() {
    if (!widget.allowPauseButton) return;

    if (_controller.isPlaying) {
      _pause();
      _playVideoWhenVisible = false;
    } else {
      _play();
      _playVideoWhenVisible = true;
    }
  }

  void _play() {
    _controller.play();
    _overlayAnimationController.animateTo(0);
  }

  void _pause() {
    _controller.pause();
    _overlayAnimationController.animateTo(1);
  }

  Widget buildOverlayGradient() {
    return FadeTransition(
      opacity: _overlayAnimationController,
      child: const DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color.fromARGB(100, 0, 0, 0),
              Color.fromARGB(0, 0, 0, 0),
            ],
            begin: FractionalOffset.bottomCenter,
            end: FractionalOffset.topCenter,
            stops: [0.0, 1.0],
          ),
        ),
      ),
    );
  }

  Widget buildVideoPlayer() => FittedBox(
        fit: BoxFit.cover,
        clipBehavior: Clip.antiAlias,
        child: SizedBox(
          height: widget.height ?? _controller._size.height,
          width: widget.width ?? _controller._size.width,
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.only(
                  bottom: widget.showProgressIndicator ? 3 : 0,
                ),
                child: VideoPlayer(_controller.videoPlayerController!),
              ),
              makeProgressIndicator(),
            ],
          ),
        ),
      );

  bool get wantKeepAlive => widget.keepAlive ?? false;
}

class VideoController extends ChangeNotifier {
  final VideoReinitializationStrategy _reinitializationStrategy;
  VideoPlayerController? videoPlayerController;

  VideoController({
    VideoReinitializationStrategy? reinitializationStrategy,
  }) : _reinitializationStrategy =
            reinitializationStrategy ?? VideoReinitializationStrategy.none;

  void play() {
    videoPlayerController?.play();
  }

  void pause() {
    videoPlayerController?.pause();
  }

  void restart() {
    videoPlayerController?.seekTo(Duration.zero);
  }

  void setVolume(double volume) {
    videoPlayerController?.setVolume(volume);
  }

  void seekTo(Duration position) {
    if (videoPlayerController != null && isInitialized) {
      videoPlayerController!.seekTo(position);
      notifyListeners();
    }
  }

  @override
  void dispose() {
    super.dispose();

    videoPlayerController?.dispose();
  }

  Future<void> _initialize(VideoOptions options) async {
    if (isInitialized &&
        _reinitializationStrategy != VideoReinitializationStrategy.none) {
      // Vai usar o mesmo controller já existente, se precisar trocar de
      // vídeo teria que criar um novo, talvez devessemos adicionar uma
      // inteligência para fazer essa criação automaticamente no futuro
      if (_reinitializationStrategy == VideoReinitializationStrategy.restart) {
        videoPlayerController!.seekTo(Duration.zero);
      }
    } else {
      videoPlayerController?.dispose();
      await _initializeNewVideoPlayerController(options);
      notifyListeners();
      videoPlayerController?.addListener(notifyListeners);
    }
    if (options.startVideo) {
      videoPlayerController!.play();
    }
  }

  Future<void> _initializeNewVideoPlayerController(VideoOptions options) {
    if (options.videoType == VideoType.network) {
      videoPlayerController = VideoPlayerController.networkUrl(
          Uri.parse(options.mediaUrl),
          videoPlayerOptions:
              VideoPlayerOptions(mixWithOthers: options.mixWithOthers));
    } else if (options.videoType == VideoType.asset) {
      videoPlayerController = VideoPlayerController.asset(options.mediaUrl,
          videoPlayerOptions:
              VideoPlayerOptions(mixWithOthers: options.mixWithOthers));
    }

    videoPlayerController!.setLooping(options.looping);
    videoPlayerController!.setVolume(options.volume);

    return videoPlayerController!.initialize();
  }

  Size get _size => videoPlayerController?.value.size ?? Size.zero;

  double? get volume => videoPlayerController?.value.volume;

  bool get isPlaying => videoPlayerController?.value.isPlaying ?? false;

  bool get isInitialized => videoPlayerController?.value.isInitialized ?? false;

  Duration get duration {
    assert(
      videoPlayerController != null,
      'O vídeo controller deve ser inicializado antes de acessar o duration',
    );
    return videoPlayerController!.value.duration;
  }

  Duration get position {
    assert(
      videoPlayerController != null,
      'O vídeo controller deve ser incializado antes de acessar o position',
    );
    return videoPlayerController!.value.position;
  }

  double get videoProgress =>
      (position.inMilliseconds / duration.inMilliseconds).toDouble();
}

enum VideoReinitializationStrategy { none, keepPlaying, restart }

class VideoOptions {
  final String mediaUrl;
  final VideoType videoType;
  final bool startVideo;
  final bool fullScreen;
  final bool looping;
  final double volume;
  final bool mixWithOthers;

  const VideoOptions({
    required this.mediaUrl,
    required this.videoType,
    this.startVideo = false,
    this.fullScreen = false,
    this.looping = true,
    this.volume = 0,
    this.mixWithOthers = false,
  });
}

abstract class VideoEvent {}

class PlayVideoEvent extends VideoEvent {}

class PauseVideoEvent extends VideoEvent {}
