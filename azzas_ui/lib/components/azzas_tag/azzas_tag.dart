
import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

class AzzasTag extends StatelessWidget {
  const AzzasTag({
    super.key,
    this.size = AzzasTagSize.medium,
    this.type = AzzasTagType.primary,
    this.label = "Tags",
    this.outline = false,
    this.textStyle,
    this.padding,
    this.limitWidth = false,
    this.primaryColor,
    this.primaryTextColor,
    this.primaryOutlineTextColor,
    this.secondaryColor,
    this.secondaryTextColor,
    this.secondaryOutlineTextColor,
    this.dangerColor,
    this.dangerTextColor,
    this.dangerOutlineTextColor,
    this.successColor,
    this.successTextColor,
    this.successOutlineTextColor,
  });

  final AzzasTagSize size;
  final AzzasTagType type;
  final String label;
  final bool outline;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final bool limitWidth;

  final Color? primaryColor;
  final Color? primaryTextColor;
  final Color? primaryOutlineTextColor;

  final Color? secondaryColor;
  final Color? secondaryTextColor;
  final Color? secondaryOutlineTextColor;

  final Color? dangerColor;
  final Color? dangerTextColor;
  final Color? dangerOutlineTextColor;

  final Color? successColor;
  final Color? successTextColor;
  final Color? successOutlineTextColor;

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).tags;

    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: _getSize(size),
        maxWidth: limitWidth ? _getSize(size) : double.infinity,
      ),
      child: Container(
        padding: padding ?? _getPaddings(size),
        decoration: BoxDecoration(
          borderRadius: theme.radius,
          color: outline ? Colors.transparent : _getBackgroundColor(theme),
          border: outline
              ? Border.all(
                  color: _getBackgroundColor(theme),
                  width: 0.5,
                )
              : null,
        ),
        child: Center(
          child: Text(
            label,
            textScaler:
                TextScaleHelper.clampTextScale(context, maxScaleFactor: 1.35),
            style: (textStyle ?? const TextStyle()).copyWith(
              color:
                  outline ? _getOutlineTextColor(theme) : _getTextColor(theme),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(AzzasTagTheme theme) {
    switch (type) {
      case AzzasTagType.primary:
        return primaryColor ?? theme.primaryColor;
      case AzzasTagType.secondary:
        return secondaryColor ?? theme.secondaryColor;
      case AzzasTagType.danger:
        return dangerColor ?? theme.dangerColor;
      case AzzasTagType.success:
        return successColor ?? theme.successColor;
    }
  }

  Color _getTextColor(AzzasTagTheme theme) {
    switch (type) {
      case AzzasTagType.primary:
        return primaryTextColor ?? theme.primaryTextColor;
      case AzzasTagType.secondary:
        return secondaryTextColor ?? theme.secondaryTextColor;
      case AzzasTagType.danger:
        return dangerTextColor ?? theme.dangerTextColor;
      case AzzasTagType.success:
        return successTextColor ?? theme.successTextColor;
    }
  }

  Color _getOutlineTextColor(AzzasTagTheme theme) {
    switch (type) {
      case AzzasTagType.primary:
        return primaryOutlineTextColor ?? theme.primaryOutlineTextColor;
      case AzzasTagType.secondary:
        return secondaryOutlineTextColor ?? theme.secondaryOutlineTextColor;
      case AzzasTagType.danger:
        return dangerOutlineTextColor ?? theme.dangerOutlineTextColor;
      case AzzasTagType.success:
        return successOutlineTextColor ?? theme.successOutlineTextColor;
    }
  }

  double _getSize(AzzasTagSize size) {
    switch (size) {
      case AzzasTagSize.xsmall:
        return 16.0;
      case AzzasTagSize.small:
        return 24.0;
      case AzzasTagSize.medium:
        return 32.0;
      case AzzasTagSize.large:
        return 40.0;
    }
  }

  EdgeInsets _getPaddings(AzzasTagSize size) {
    switch (size) {
      case AzzasTagSize.small:
      case AzzasTagSize.xsmall:
        return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.5);
      case AzzasTagSize.medium:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 4.5);
      case AzzasTagSize.large:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8.5);
    }
  }
}
