import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

class AzzasProductInfo extends StatelessWidget {
  final AzzasProductInfoParams params;

  const AzzasProductInfo({super.key, required this.params});

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).productInfo;
    final effectiveTitleStyle = params.titleTextStyle ?? theme.titleTextStyle;
    final effectiveCurrentPriceStyle =
        params.currentPriceTextStyle ?? theme.currentPriceTextStyle;
    final effectiveFullPriceStyle =
        params.fullPriceTextStyle ?? theme.fullPriceTextStyle;
    final effectiveEstimatedDeliveryStyle =
        params.estimatedDeliveryTextStyle ?? theme.estimatedDeliveryTextStyle;
    final effectiveSpacing = params.contentSpacing ?? theme.contentSpacing;
    final effectiveSoftWrap = params.softWrap ?? theme.softWrap;
    final effectiveMaxLines = params.maxLines ?? theme.maxLines;

    return SizedBox(
      width: params.infiniteWidth ? double.infinity : null,
      child: Column(
        crossAxisAlignment: params.centerInfo == true
            ? CrossAxisAlignment.center
            : CrossAxisAlignment.start,
        children: [
          if (params.tagsAlignment ==
                  AzzasProductInfoTagsAlignment.aboveProductTitle &&
              params.tagsBuilder != null)
            params.tagsBuilder!(),
          Text(
            params.productTitle,
            style: effectiveTitleStyle,
            softWrap: effectiveSoftWrap,
            maxLines: effectiveMaxLines,
            overflow: TextOverflow.ellipsis,
            textScaler:
                TextScaleHelper.clampTextScale(context, maxScaleFactor: 2.0),
          ),
          SizedBox(
            height: effectiveSpacing,
          ),
          if (params.complementarWidget != null) params.complementarWidget!,
          if (params.complementarWidget != null)
            SizedBox(
              height: effectiveSpacing,
            ),
          if (params.tagsAlignment ==
                  AzzasProductInfoTagsAlignment.belowProductTitle &&
              params.tagsBuilder != null)
            params.tagsBuilder!(),
          if (params.showPrice)
            Wrap(
              spacing: 6.0,
              children: [
                if (params.fullPrice.isNotNullOrEmpty &&
                    params.isOnSale &&
                    valueIsNotZero(params.fullPrice))
                  Text(
                    params.fullPrice!,
                    style: effectiveFullPriceStyle,
                    textScaler: TextScaleHelper.clampTextScale(context,
                        maxScaleFactor: 2.0),
                  ),
                if (params.currentPrice.isNotNullOrEmpty &&
                    valueIsNotZero(params.currentPrice))
                  Text(
                    params.currentPrice!,
                    style: effectiveCurrentPriceStyle,
                    textScaler: TextScaleHelper.clampTextScale(context,
                        maxScaleFactor: 2.0),
                  ),
              ],
            ),
          if (params.estimatedDelivery.isNotNullOrEmpty) ...[
            Text(
              params.estimatedDelivery!,
              style: effectiveEstimatedDeliveryStyle,
            ),
          ],
        ],
      ),
    );
  }

  /// Método que verifica que o valor não está zerado
  bool valueIsNotZero(String? price) {
    final value = (price ?? "0,00").replaceAll('R\$', '').trim();
    return value != "0,00";
  }
}
