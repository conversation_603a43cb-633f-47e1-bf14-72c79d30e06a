import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

/// O [AzzasCepButton] é um componente que engloba um [AzzasInput],
/// para preenchimento do CEP e um também um [AzzasPrimaryButton] para efetuar a busca do CEP.
class AzzasCepButton extends StatelessWidget {
  /// O [controller] é o controlador do [AzzasInput] que será utilizado para preenchimento do CEP.
  final TextEditingController controller;

  /// O [onTap] é a função que será chamada quando o botão de busca do CEP for pressionado.
  final void Function()? onTap;

  /// O [inputSize] é o tamanho do [AzzasInput] que será utilizado para preenchimento do CEP.
  final AzzasInputSize inputSize;

  /// O [onFieldSubmitted] é a função que será chamada quando o campo de texto do [AzzasInput] for submetido.
  final ValueChanged<String>? onFieldSubmitted;

  /// O [onChanged] é a função que será chamada quando o texto do [AzzasInput] for alterado.
  final ValueChanged<String>? onChanged;

  /// O [suffix] é o widget que será exibido no final do [AzzasInput].
  final Widget? suffix;

  /// O [validator] é a função que será chamada para validar o texto do [AzzasInput].
  final String? Function(String?)? validator;

  /// O [isLoading] é a flag que indica se o [AzzasInput] está em estado de carregamento.
  final bool? isLoading;

  /// O [inputPadding] é o padding do [AzzasInput].
  final EdgeInsets? inputPadding;

  /// O [hintText] é o texto que será exibido como dica no [AzzasInput].
  final String? hintText;

  /// O [placeholder] é o texto que será exibido como placeholder no [AzzasInput].
  final String? placeholder;

  /// O [supportedText] é o texto que será exibido como suporte(logo abaixo) no [AzzasInput].
  final String? supportedText;

  /// O [showSearchButton] é a flag que indica se será exibido o botão buscar cep no [AzzasInput].
  final bool showSearchButton;

  /// O [enabledField] é a flag que indica se o botão buscar cep no [AzzasInput] será editavel.
  final bool enabledField;

  /// Cria uma instância do [AzzasCepButton].
  ///
  /// O [controller] é obrigatório.
  const AzzasCepButton({
    super.key,
    required this.controller,
    this.inputSize = AzzasInputSize.large,
    this.onTap,
    this.onFieldSubmitted,
    this.onChanged,
    this.suffix,
    this.validator,
    this.isLoading,
    this.inputPadding,
    this.hintText,
    this.placeholder,
    this.supportedText,
    this.showSearchButton = true,
    this.enabledField = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).cepButton;
    final effectiveInputPadding = inputPadding ?? theme.inputPadding;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: AzzasInput(
            textEditingController: controller,
            hintText: hintText ?? "00000-000",
            placeholder: placeholder ?? "Digite seu CEP",
            keyboardType: TextInputType.number,
            supportedText: supportedText ?? "Apenas números",
            onFieldSubmitted: onFieldSubmitted,
            onChanged: onChanged,
            validator: validator,
            isLoading: isLoading,
            size: inputSize,
            cursorHeight: 24,
            contentPadding: effectiveInputPadding,
            mask: AzzasInputMask.cep,
            suffixWidget: suffix,
            enabled: enabledField,
          ),
        ),
        const SizedBox(
          width: 8.0,
        ),
        if (showSearchButton)
          AzzasPrimaryButton(
            size: ButtonSize.large,
            onPressed: onTap,
            child: const Text('Buscar CEP'),
          ),
      ],
    );
  }
}
