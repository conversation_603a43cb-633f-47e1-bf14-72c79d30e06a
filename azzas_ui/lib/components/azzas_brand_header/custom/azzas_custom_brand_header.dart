import 'package:azzas_ui/components/azzas_sliver_app_bar/azzas_sliver_brand_header_exp.dart';
import 'package:flutter/material.dart';

class AzzasCustomBrandHeader extends StatefulWidget {
  final BrandHeaderAppearance appearance;
  final int buttonRightBottomIndex;
  final VoidCallback? onTapButtonRight;
  final VoidCallback? onTapButtonLeft;
  final double imageHeight;
  final double? iconSize;
  final bool hasTopSafeArea;
  final bool showRigthIcon;

  const AzzasCustomBrandHeader({
    super.key,
    required this.appearance,
    this.buttonRightBottomIndex = 1,
    this.onTapButtonRight,
    this.onTapButtonLeft,
    this.imageHeight = 20,
    this.iconSize = 35,
    this.hasTopSafeArea = true,
    this.showRigthIcon = true,
  });

  @override
  State<StatefulWidget> createState() => _AzzasCustomBrandHeaderState();
}

class _AzzasCustomBrandHeaderState extends State<AzzasCustomBrandHeader> {
  @override
  Widget build(BuildContext context) {
    final isTransparent =
        widget.appearance == BrandHeaderAppearance.transparent;
    final assetName = isTransparent
        ? 'assets/images/logo-light.png'
        : 'assets/images/logo-dark.png';

    return SizedBox(
      width: double.infinity,
      child: SafeArea(
        top: widget.hasTopSafeArea,
        child: SizedBox(
          height: 56,
          child: Stack(
            children: [
              Align(
                alignment: Alignment.center,
                child: Image.asset(
                  assetName,
                  height: widget.imageHeight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
