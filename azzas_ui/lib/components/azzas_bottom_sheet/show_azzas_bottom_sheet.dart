import 'package:flutter/material.dart';

void showAzzasBottomSheet({
  required BuildContext context,
  required Widget Function(BuildContext) builder,
  required TickerProvider vsync,
  isScrollControlled = true,
  isDismissible = true,
  void Function()? onDismiss,
  enableDrag = true,
  Color? backgroundColor,
}) async {
  await showModalBottomSheet(
    context: context,
    builder: builder,
    backgroundColor: backgroundColor,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    isScrollControlled: isScrollControlled,
    transitionAnimationController: AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 300),
    ),
  ).whenComplete(onDismiss ?? () {});
}
