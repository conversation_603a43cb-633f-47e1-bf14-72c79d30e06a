import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

/// Bottom Sheet contendo os botões de calcular CEP e ativar localização.
class AzzasCalculateCepBottomSheet extends StatefulWidget {
  const AzzasCalculateCepBottomSheet({
    required this.onCloseTap,
    this.calculateCepButton,
    required this.calculateCepButtonTap,
    required this.locationActiveButtonTap,
    required this.cepController,
    this.locationActiveButton,
    this.title,
    this.body,
    this.closeIcon,
    this.titleStyle,
    this.bodyStyle,
    this.borderRadius,
    this.showDragBar = false,
    this.padding,
    this.isLoadingCalculateCepButton = false,
    this.isLoadingActivateLocationButton = false,
    this.isLoadingLocation = false,
    super.key,
  });

  /// Função chamada ao fechar o bottom sheet.
  final VoidCallback onCloseTap;

  /// Ícone de fechar o bottom sheet.
  final IconData? closeIcon;

  /// Título do bottom sheet.
  final String? title;

  /// Corpo do bottom sheet.
  final String? body;

  /// Estilo do título.
  final TextStyle? titleStyle;

  /// Estilo do corpo.
  final TextStyle? bodyStyle;

  /// Borda do bottom sheet.
  final BorderRadius? borderRadius;

  /// Se verdadeiro, exibe a barra de arrastar.
  final bool showDragBar;

  /// Texto do botão de calcular CEP.
  final String? calculateCepButton;

  /// Texto do botão de ativar localização.
  final String? locationActiveButton;

  /// Função chamada ao clicar no botão de calcular CEP.
  final Function(String) calculateCepButtonTap;

  /// Função chamada ao clicar no botão de ativar localização.
  final VoidCallback locationActiveButtonTap;

  /// Padding do bottom sheet.
  final EdgeInsets? padding;

  /// Controller do campo de CEP.
  final TextEditingController cepController;

  /// Se verdadeiro, exibe o loading no botão de calcular CEP.
  final bool isLoadingCalculateCepButton;

  /// Se verdadeiro, exibe o loading no botão de ativar localização.
  final bool isLoadingActivateLocationButton;

  /// Se verdadeiro, exibe um ícone de carregamento no [suffixWidget] do input [AzzasCepButton].
  final bool isLoadingLocation;

  @override
  State<AzzasCalculateCepBottomSheet> createState() =>
      _AzzasCalculateCepBottomSheetState();
}

class _AzzasCalculateCepBottomSheetState
    extends State<AzzasCalculateCepBottomSheet> {
  bool isCepValid = false;

  @override
  void initState() {
    super.initState();
    widget.cepController.clear();
    widget.cepController.addListener(() {
      _onTextChanged();
    });
  }

  void _onTextChanged() {
    final isValid = TextHelper.isCEP(widget.cepController.text);

    if (isValid && mounted) {
      setState(() {
        isCepValid = isValid;
        FocusManager.instance.primaryFocus?.unfocus();
      });
    } else {
      if (mounted) {
        setState(() {
          isCepValid = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bottomSheetTheme =
        AzzasThemeProvider.of(context).bottomSheetTheme.copyWith(
              closeIcon: widget.closeIcon,
            );

    final isScrollContent = MediaQuery.textScalerOf(context).scale(1) > 1;

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title ?? 'Como você quer receber sua compra?',
          style: widget.titleStyle,
        ),
        const SizedBox(
          height: 16.0,
        ),
        Text(
          widget.body ?? 'Digite seu CEP para consultar as opções de entrega.',
          style: widget.bodyStyle,
        ),
        const SizedBox(
          height: 24,
        ),
        AzzasCepButton(
          controller: widget.cepController,
          showSearchButton: false,
          isLoading: widget.isLoadingLocation,
          suffix: IconButton(
            icon: Icon(
              bottomSheetTheme.closeIcon,
            ),
            onPressed: () {
              if (mounted) {
                setState(() {
                  isCepValid = false;
                });
              }
              widget.cepController.clear();
            },
          ),
        ),
        const SizedBox(
          height: 32.0,
        ),
        AzzasPrimaryButton(
          expanded: true,
          isDisabled: !isCepValid,
          isLoading: widget.isLoadingCalculateCepButton,
          onPressed: () {
            widget.calculateCepButtonTap(widget.cepController.text);
            widget.cepController.removeListener(_onTextChanged);
          },
          child: Text(
            widget.calculateCepButton ?? "Calcular para este CEP",
          ),
        ),
        const SizedBox(
          height: 16.0,
        ),
        AzzasPrimaryButton(
          expanded: true,
          isLoading: widget.isLoadingActivateLocationButton,
          onPressed: widget.locationActiveButtonTap,
          child: Text(
            widget.locationActiveButton ?? "Ativar localização",
          ),
        ),
      ],
    );

    return AzzasBottomSheet(
      iconSize: 16,
      showDragBar: widget.showDragBar,
      closeIcon: bottomSheetTheme.closeIcon,
      onCloseTap: widget.onCloseTap,
      borderRadius: widget.borderRadius ?? BorderRadius.zero,
      padding: widget.padding ??
          const EdgeInsets.all(24).copyWith(
            bottom: MediaQuery.of(context).viewInsets.bottom + 24,
          ),
      scrollContent: isScrollContent ? content : null,
      content: isScrollContent ? const SizedBox.shrink() : content,
    );
  }
}
