import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AzzasCheckoutStepAddressOpen extends StatelessWidget {
  final AzzasCheckoutStepAddressOpenParams params;

  const AzzasCheckoutStepAddressOpen({
    super.key,
    required this.params,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).checkoutStep.address;

    final effectiveLocationIconSize =
        params.locationIconSize ?? theme.openStatus.locationIconSize;

    final effectiveCompleteAddressPadding = params.completeAddressPadding ??
        theme.openStatus.completeAddressPadding;
    final effectiveCompleteAddressTextStyle =
        params.completeAddressTitleStyle ??
            theme.openStatus.completeAddressTitleStyle;

    return Form(
      key: params.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AzzasCepButton(
            controller: params.cepInputParams.controller,
            validator: params.cepInputParams.validator,
            hintText: params.cepInputParams.hintText,
            placeholder: params.cepInputParams.placeholder,
            supportedText: params.cepInputParams.supportedText,
            onTap: params.onTapSearchCep,
            showSearchButton: params.showSearchButton,
            enabledField: params.cepInputParams.enabledField,
          ),
          const SizedBox(
            height: 16,
          ),
          if (params.showLocationButton)
            AzzasSecondaryButton(
              expanded: true,
              onPressed: params.onTapUseLocation,
              leading: Icon(
                theme.openStatus.locationIcon,
                size: effectiveLocationIconSize,
              ),
              child: Text(
                params.useLocationLabel ?? "Usar minha localização",
              ),
            ),
          Padding(
            padding: effectiveCompleteAddressPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  params.completeAddressTitle ?? "Complete seu endereço",
                  style: effectiveCompleteAddressTextStyle,
                ),
                const SizedBox(height: 24.0),
                AzzasInput(
                  textEditingController:
                      params.responsibleNameInputParams.controller,
                  placeholder: params.responsibleNameInputParams.placeholder ??
                      'Nome de quem vai receber',
                  hintText: params.responsibleNameInputParams.hintText ??
                      "Nome de quem vai receber",
                  validator: params.responsibleNameInputParams.validator ??
                      (value) =>
                          value!.isEmpty ? 'O nome não pode vir vazio' : null,
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  size: AzzasInputSize.large,
                  contentPadding: theme.openStatus.inputPadding,
                  cursorHeight: 24,
                ),
                const SizedBox(height: 16.0),
                AzzasInput(
                  textEditingController:
                      params.streetNameInputParams.controller,
                  hintText:
                      params.streetNameInputParams.hintText ?? "Nome da Rua",
                  placeholder:
                      params.streetNameInputParams.placeholder ?? 'Nome da Rua',
                  validator: params.responsibleNameInputParams.validator ??
                      (value) =>
                          value!.isEmpty ? 'A rua não pode vir vazia' : null,
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  size: AzzasInputSize.large,
                  contentPadding: theme.openStatus.inputPadding,
                  cursorHeight: 24,
                  enabled: params.streetNameInputParams.enabledField,
                ),
                const SizedBox(height: 16.0),
                AzzasInput(
                  textEditingController:
                      params.neighborhoodInputParams.controller,
                  hintText: params.neighborhoodInputParams.hintText ?? "Bairro",
                  placeholder:
                      params.neighborhoodInputParams.placeholder ?? 'Bairro',
                  validator: params.neighborhoodInputParams.validator ??
                      (value) =>
                          value!.isEmpty ? 'O Bairro não pode vir vazio' : null,
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  size: AzzasInputSize.large,
                  contentPadding: theme.openStatus.inputPadding,
                  cursorHeight: 24,
                  enabled: params.neighborhoodInputParams.enabledField,
                ),
                const SizedBox(height: 16.0),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 111.0,
                      child: AzzasInput(
                        textEditingController:
                            params.numberInputParams.controller,
                        hintText: params.numberInputParams.hintText ?? "Número",
                        placeholder:
                            params.numberInputParams.placeholder ?? 'Número',
                        validator: params.numberInputParams.validator ??
                            (value) => value!.isEmpty
                                ? 'O número não pode vir vazio'
                                : null,
                        autoValidateMode: AutovalidateMode.onUserInteraction,
                        size: AzzasInputSize.large,
                        contentPadding: theme.openStatus.inputPadding,
                        errorMaxLines: 3,
                        inputFormatter: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        keyboardType: TextInputType.number,
                        cursorHeight: 24,
                      ),
                    ),
                    const SizedBox(
                      width: 8.0,
                    ),
                    Expanded(
                      child: AzzasInput(
                        placeholder: params.complementInputParams.placeholder ??
                            'Complemento',
                        hintText: params.complementInputParams.hintText ??
                            "Complemento",
                        textEditingController:
                            params.complementInputParams.controller,
                        supportedText:
                            params.complementInputParams.supportedText ??
                                "Opcional",
                        size: AzzasInputSize.large,
                        cursorHeight: 24,
                        contentPadding: theme.openStatus.inputPadding,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          AzzasPrimaryButton(
            onPressed: params.onTapFinishButton,
            isDisabled: params.isFinishButtonDisabled ?? false,
            isLoading: params.loadingFinishButton,
            expanded: true,
            child: Text(params.finishButtonLabel ?? "ir para Pagamento"),
          ),
        ],
      ),
    );
  }
}
