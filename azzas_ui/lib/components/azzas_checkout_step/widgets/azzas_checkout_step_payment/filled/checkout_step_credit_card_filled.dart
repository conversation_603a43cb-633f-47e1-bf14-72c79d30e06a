import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

class CheckoutStepCreditCardFilled extends StatelessWidget {
  final CreditCardInfo creditCardInfo;
  final bool isOrderCompleted;
  final TextStyle? subtitleTextStyle;
  final TextStyle? subheaderTextStyle;
  final TextStyle? titleTextStyle;
  final bool showAddVoucher;
  final bool hasError;
  final void Function()? onAddVoucher;
  final bool hasCreditVoucherSelected;

  const CheckoutStepCreditCardFilled({
    super.key,
    required this.creditCardInfo,
    required this.isOrderCompleted,
    this.subtitleTextStyle,
    this.subheaderTextStyle,
    this.titleTextStyle,
    this.showAddVoucher = true,
    this.hasError = false,
    this.onAddVoucher,
    this.hasCreditVoucherSelected = false,
  });

  Widget _buildPaymentInfoRow({
    required AzzasCheckoutStepPaymentFilledTheme effectiveTheme,
    bool isWide = false
  }) {
    return Flex(
      direction: isWide ? Axis.vertical : Axis.horizontal,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment:  isWide ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Pagamento com cartão', style: effectiveTheme.subheaderStyle),
            const SizedBox(height: 8),
            Text(
              '${creditCardInfo.creditCardType.cardName} • final ${creditCardInfo.lastDigits}',
              style: effectiveTheme.titleStyle,
            ),
            const SizedBox(height: 8),
          ],
        ),
        AzzasSecondaryButton(
          style: effectiveTheme.creditCard.changeInstallmentsButtonStyle,
          onPressed: creditCardInfo.onInstallmentsChange,
          trailing: effectiveTheme.creditCard.changeInstallmentsIcon,
          child: Text(
            creditCardInfo.installmentsLabel,
            style: effectiveTheme.subtitleStyle,
          ),
        ),
      ],
    );
  }

  Widget _buildChangePaymentMethodButton(
      AzzasCheckoutStepPaymentFilledTheme effectiveTheme) {
    return AzzasSecondaryButton(
      style: effectiveTheme.creditCard.changePaymentMethodButtonStyle,
      trailing: effectiveTheme.creditCard.changePaymentMethodIcon,
      onPressed: creditCardInfo.onPaymentMethodChange,
      child: Text(
        creditCardInfo.changePaymentMethodLabel ?? 'Alterar tipo de pagamento',
        style: effectiveTheme.creditCard.ctaTextStyle,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveTheme = AzzasThemeProvider.of(context)
        .checkoutStep
        .payment
        .filledStatus
        .copyWith(
          subtitleStyle: subtitleTextStyle,
          subheaderStyle: subheaderTextStyle,
          titleStyle: titleTextStyle,
        );
    final isWide = MediaQuery.textScalerOf(context).scale(1) > 1;

    if (isOrderCompleted) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Pagamento com cartão', style: effectiveTheme.subheaderStyle),
          const SizedBox(height: 8),
          Text(
            '${creditCardInfo.creditCardType.cardName} • final ${creditCardInfo.lastDigits}',
            style: effectiveTheme.titleStyle,
          ),
          const SizedBox(height: 8),
          Text(
            creditCardInfo.installmentsLabel,
            style: effectiveTheme.subtitleStyle,
          ),
        ],
      );
    }

    if (hasError) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showAddVoucher) ...[
            Divider(
              color: effectiveTheme.dividerColor,
              height: 0.5,
            ),
            AzzasListItemIcon(
              onTap: onAddVoucher ?? () {},
              divider: true,
              title: 'Vale crédito',
              benefit: 'Você tem vale disponível pro resgate',
            ),
            const SizedBox(height: 24),
          ],
          AzzasFeedbackCard(
            card: AzzasFeedbackCardParams(
              title: 'Ops, ocorreu um erro com\no pagamento.',
              type: AzzasFeedbackCardType.error,
              textStyle:
                  effectiveTheme.creditVoucher.errorFeedbackCardTextStyle,
              backgroundColor:
                  effectiveTheme.creditVoucher.errorFeedbackCardColor,
              trailing: AzzasPrimaryButton(
                size: ButtonSize.small,
                style: AzzasButtonStyle(
                  border: const Border(),
                  backgroundColor: effectiveTheme
                          .creditVoucher.errorFeedbackCardButtonColor ??
                      AzzasFeedbackCardType.getFeedbackCardColor(
                          AzzasFeedbackCardType.error, context),
                ),
                onPressed: creditCardInfo.onPaymentMethodChange,
                child: const Text('Alterar'),
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildPaymentInfoRow(effectiveTheme: effectiveTheme, isWide: isWide),
          const SizedBox(height: 8),
          _buildChangePaymentMethodButton(effectiveTheme),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPaymentInfoRow(effectiveTheme: effectiveTheme, isWide: isWide),
        const SizedBox(height: 8),
        if (hasCreditVoucherSelected == false)
          _buildChangePaymentMethodButton(effectiveTheme),
        Divider(
          color: effectiveTheme.dividerColor,
          height: 0.5,
        ),
        if (showAddVoucher) ...[
          AzzasLinkButton(
            expanded: true,
            style: const AzzasButtonStyle(
              padding: EdgeInsets.zero,
            ),
            trailing: effectiveTheme.creditCard.addVoucherIcon,
            trailingInEnd: true,
            onPressed: creditCardInfo.onAddVoucher,
            child: Text(
              creditCardInfo.addVoucherLabel ?? 'Adicionar vale crédito',
              style: effectiveTheme.creditCard.ctaTextStyle,
            ),
          ),
        ]
      ],
    );
  }
}
