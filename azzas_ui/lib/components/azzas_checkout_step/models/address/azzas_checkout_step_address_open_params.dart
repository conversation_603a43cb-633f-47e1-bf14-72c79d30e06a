import 'package:azzas_ui/components/components.dart';
import 'package:flutter/material.dart';

class AzzasCheckoutStepAddressOpenParams
    extends AzzasCheckoutStepAddressBaseParams {
  /// GlobalKey do formulário, e que será utilizado para validação dos campos
  ///
  /// Exemplo: `final key = GlobalKey<FormState>()`
  final GlobalKey<FormState> formKey;

  /// Params do input do CEP
  ///
  /// Exemplo: `final cepInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams cepInputParams;

  /// Params do input do endereço
  ///
  /// Exemplo: `final addressInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams responsibleNameInputParams;

  /// Params do input do nome da rua
  ///
  /// Exemplo: `final streetNameInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams streetNameInputParams;

  /// Params do input do número
  ///
  /// Exemplo: `final numberInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams numberInputParams;

  /// Params do input do complemento
  ///
  /// Exemplo: `final complementInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams complementInputParams;

  /// Params do input do complemento
  ///
  /// Exemplo: `final complementInputParams = StepAddressInputParams(...)`
  final StepAddressInputParams neighborhoodInputParams;

  /// Função chamada ao clicar no botão de usar localização
  ///
  /// Exemplo: `() => print("Usar localização clicado")`
  final void Function() onTapUseLocation;

  /// Função chamada ao clicar no botão de finalizar
  ///
  /// Exemplo: `() => print("Finalizar clicado")`
  final void Function() onTapFinishButton;

  /// Define se o botão de finalizar está no estado de loading
  ///
  /// Se definido como `true`, o botão de finalizar estará em loading.
  final bool loadingFinishButton;

  /// Define se o botão de finalizar deve estar desabilitado
  ///
  /// Se definido como `true`, o botão de finalizar estará desabilitado.
  final bool? isFinishButtonDisabled;

  /// Tamanho do ícone do cabeçalho
  ///
  /// Exemplo: `28.0`. Se não for definido, o tamanho padrão do tema será utilizado.
  final double? headerIconSize;

  /// Tamanho do ícone de localização
  ///
  /// Exemplo: `28.0`. Se não for definido, o tamanho padrão do tema será utilizado.
  final double? locationIconSize;

  /// Estilo do texto do título
  ///
  /// Exemplo: `TextStyle(color: Colors.black)`. Se não for definido, o estilo padrão do tema será utilizado.
  final TextStyle? titleStyle;

  /// Padding do título do endereço completo
  ///
  /// Exemplo: `EdgeInsets.all(16.0)`. Se não for definido, o padding padrão do tema será utilizado.
  final EdgeInsets? completeAddressPadding;

  /// Estilo do texto do título do endereço completo
  ///
  /// Exemplo: `TextStyle(color: Colors.black)`. Se não for definido, o estilo padrão do tema será utilizado.
  final TextStyle? completeAddressTitleStyle;

  /// Parâmetros do input

  /// Texto do botão de finalizar
  ///
  /// Exemplo: `"Finalizar"`. Se não for definido, o texto padrão do componente será utilizado.
  final String? finishButtonLabel;

  /// Texto do botão de usar localização
  ///
  /// Exemplo: `"Usar minha localização"`. Se não for definido, o texto padrão do componente será utilizado.
  final String? useLocationLabel;

  /// Texto do título do endereço completo
  ///
  /// Exemplo: `"Complete seu endereço"`. Se não for definido, o texto padrão do componente será utilizado.
  final String? completeAddressTitle;

  /// Função chamada ao clicar em Buscar cep
  ///
  /// Exemplo: `() => print("Buscar cep")`
  final Function()? onTapSearchCep;

  /// Define se o botão de usar localização deve estar desabilitado
  ///
  /// Se definido como `false`, o botão estará desabilitado. O valor default é `true`.
  final bool showLocationButton;

  /// Define se o botão de pesquisar cep deve estar desabilitado
  ///
  /// Se definido como `false`, o botão estará desabilitado. O valor default é `true`.
  final bool showSearchButton;

  /// O [enabledField] é a flag que indica se o campo será editavel.
  final bool enabledField;

  const AzzasCheckoutStepAddressOpenParams({
    required this.formKey,
    required this.cepInputParams,
    required this.responsibleNameInputParams,
    required this.streetNameInputParams,
    required this.numberInputParams,
    required this.complementInputParams,
    required this.neighborhoodInputParams,
    required this.onTapUseLocation,
    required this.onTapFinishButton,
    this.isFinishButtonDisabled,
    this.headerIconSize,
    this.locationIconSize,
    this.titleStyle,
    this.completeAddressPadding,
    this.completeAddressTitleStyle,
    this.finishButtonLabel,
    this.useLocationLabel,
    this.completeAddressTitle,
    this.onTapSearchCep,
    this.showLocationButton = true,
    this.showSearchButton = true,
    this.enabledField = true,
    this.loadingFinishButton = false,
  });
}

class StepAddressInputParams {
  /// Controlador do input de texto
  final TextEditingController controller;

  /// Placeholder do input
  final String? placeholder;

  /// Texto de "dica" do que se trata o input
  final String? hintText;

  /// Função de validação do input
  final String? Function(String?)? validator;

  /// Texto de suporte do input (aparece logo abaixo do input)
  final String? supportedText;

  /// Parametro usado para habilitar ou desabilitar edição de texto
  final bool enabledField;

  const StepAddressInputParams({
    required this.controller,
    this.placeholder,
    this.hintText,
    this.validator,
    this.supportedText,
    this.enabledField = true,
  });
}
