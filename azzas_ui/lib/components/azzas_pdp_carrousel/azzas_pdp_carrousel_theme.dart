import 'package:azzas_ui/components/azzas_controller_carrousel/azzas_controller_carrousel_theme.dart';
import 'package:flutter/material.dart';

class AssasPdpCarrouselTheme {
  final AzzasControllerCarrouselTheme carrouselTheme;
  final TextStyle productTitleStyle;
  final TextStyle currentPriceStyle;
  final TextStyle fullPriceStyle;
  final Icon bagIcon;
  final Icon backIcon;
  final Icon? closeFullScreenIcon;

  const AssasPdpCarrouselTheme({
    required this.carrouselTheme,
    required this.productTitleStyle,
    required this.currentPriceStyle,
    required this.fullPriceStyle,
    required this.bagIcon,
    required this.backIcon,
    this.closeFullScreenIcon,
  });

  AssasPdpCarrouselTheme copyWith({
    AzzasControllerCarrouselTheme? carrouselTheme,
    TextStyle? productTitleStyle,
    TextStyle? currentPriceStyle,
    TextStyle? fullPriceStyle,
    Icon? bagIcon,
    Icon? backIcon,
    Icon? closeFullScreenIcon,
  }) {
    return AssasPdpCarrouselTheme(
      carrouselTheme: carrouselTheme ?? this.carrouselTheme,
      productTitleStyle: productTitleStyle ?? this.productTitleStyle,
      currentPriceStyle: currentPriceStyle ?? this.currentPriceStyle,
      fullPriceStyle: fullPriceStyle ?? this.fullPriceStyle,
      bagIcon: bagIcon ?? this.bagIcon,
      backIcon: backIcon ?? this.backIcon,
      closeFullScreenIcon: closeFullScreenIcon ?? this.closeFullScreenIcon,
    );
  }
}
