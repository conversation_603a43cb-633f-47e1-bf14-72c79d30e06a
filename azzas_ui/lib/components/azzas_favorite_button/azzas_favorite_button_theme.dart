import 'package:flutter/material.dart';

class AzzasFavoriteButtonBaseTheme {
  final double buttonHeight;
  final double buttonWidth;
  final double iconSize;
  final BoxDecoration decoration;
  final IconData filledIcon;
  final IconData outlinedIcon;
  final Color heartColor;
  final String? lottieAnimationPath;
  final double? lottieSize;
  final TextStyle? removeProductBottomSheetTitleStyle;

  const AzzasFavoriteButtonBaseTheme({
    required this.buttonHeight,
    required this.buttonWidth,
    required this.iconSize,
    required this.decoration,
    required this.filledIcon,
    required this.outlinedIcon,
    required this.heartColor,
    this.lottieAnimationPath,
    this.lottieSize,
    this.removeProductBottomSheetTitleStyle,
  });
}
