import 'dart:ui';

import 'package:flutter/material.dart';

/// Componente `AzzasBlurContainer`
/// Esse componente é um container que pode ser utilizado para criar um efeito de desfoque (blur) em seu conteúdo.
/// Ele pode ser usado para destacar informações ou criar um efeito visual interessante em sua interface.
/// Pode ser utilizado em conjunto com um nível de opacidade e cor.
class AzzasBlurContainer extends StatelessWidget {
  final double blur;
  final double opacity;
  final Color color;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final BorderRadiusGeometry? borderRadius;
  final BoxDecoration? decoration;

  const AzzasBlurContainer({
    Key? key,
    required this.blur,
    required this.opacity,
    required this.color,
    required this.child,
    this.padding,
    this.borderRadius,
    this.decoration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          padding: padding,
          decoration: decoration?.copyWith(
                color: color.withOpacity(opacity),
              ) ??
              BoxDecoration(
                color: color.withOpacity(opacity),
                borderRadius: borderRadius,
              ),
          child: child,
        ),
      ),
    );
  }
}
