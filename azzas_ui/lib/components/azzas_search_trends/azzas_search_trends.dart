import 'package:azzas_ui/azzas_ui.dart';
import 'package:flutter/material.dart';

class AzzasSearchTrends extends StatefulWidget {
  final String title;
  final Function(String term) onTap;
  final List trends;
  final TextStyle? titleStyle;
  final TextStyle? termsStyle;
  final Color? bordeColor;
  final bool isColumn;

  const AzzasSearchTrends({
    super.key,
    required this.title,
    required this.onTap,
    this.trends = const [],
    this.titleStyle,
    this.termsStyle,
    this.isColumn = true,
    this.bordeColor,
  });

  @override
  State<StatefulWidget> createState() => _AzzasSearchTrendsState();
}

class _AzzasSearchTrendsState extends State<AzzasSearchTrends> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              widget.title,
              style: widget.titleStyle,
            ),
          ),
          widget.isColumn
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: widget.trends.map(
                    (term) {
                      return InkWell(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Text(
                            term,
                            style: widget.termsStyle,
                          ),
                        ),
                        onTap: () {
                          widget.onTap(term);
                        },
                      );
                    },
                  ).toList(),
                )
              : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: widget.trends
                        .map(
                          (term) {
                            return GestureDetector(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 16),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: widget.bordeColor ?? Colors.black),
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: Text(
                                  term,
                                  style: widget.termsStyle,
                                ),
                              ),
                              onTap: () {
                                widget.onTap(term);
                              },
                            );
                          },
                        )
                        .toList()
                        .separated(SizedBox(
                          width: 8,
                        )),
                  ),
              ),
        ],
      ),
    );
  }
}
