enum OrderBy {
  orderByReleaseDateDESC,
  orderByTopSaleDESC,
  orderByPriceDESC,
  orderByPriceASC,
  orderByBestDiscountDESC,
  orderByNameASC;

  static OrderBy? fromString(String? str) {
    if (str == null) {
      return null;
    }
    return OrderBy.values
        .where((ob) => ob.name.toLowerCase() == str.toLowerCase())
        .firstOrNull;
  }

  String? getOrderNamePtBr() {
    switch (this) {
      case OrderBy.orderByReleaseDateDESC:
        return 'novidades';
      case OrderBy.orderByBestDiscountDESC:
        return 'maior desconto';
      case OrderBy.orderByTopSaleDESC:
        return 'mais vendidos';
      case OrderBy.orderByPriceDESC:
        return 'maior preço';
      case OrderBy.orderByPriceASC:
        return 'menor preço';
      case OrderBy.orderByNameASC:
        return 'ordernar por nome';
    }
  }
}

OrderBy? orderByFromString(String? str) {
  if (str == null) {
    return null;
  }
  return OrderBy.values.where((ob) => ob.name == str).firstOrNull;
}
