import 'package:azzas_cms/components/components.dart';
import 'package:flutter/foundation.dart';
import 'components/components.dart';

abstract class CmsComponent {
  const CmsComponent({
    required this.id,
    required this.componentType,
  });

  static CmsComponent? fromJson(Map<String, dynamic> json) {
    var componentType = json['__component'];
    try {
      return switch (componentType) {
        'carousel-list.carousel-list' =>
          CarouselListCmsComponent.fromJson(json),
        'accordion-menu.accordion-menu' =>
          AccordionMenuCmsComponent.fromJson(json),
        'category-banner.category-banner' =>
          CategoryBannerCmsComponent.fromJson(json),
        'categories-mosaic.categories-mosaic' =>
          CategoriesMosaicCmsComponent.fromJson(json),
        'spot-product.spot-product' => SpotProductCmsComponent.fromJson(json),
        'video-gallery.video-gallery' =>
          VideoGalleryCmsComponent.fromJson(json),
        'list-links.list-links' => ListLinksListCmsComponent.from<PERSON>son(json),
        'carousel-banner.carousel-banner' =>
          CarouselBannerCmsComponent.fromJson(json),
        'mosaic.categories-mosaic' =>
          CategoriesMosaicCompleteCmsComponent.fromJson(json),
        'highlight.highlight-list' => HighlightListCmsComponent.fromJson(json),
        'content-area.content-area-with-sign' =>
          ContentAreaWithSignCmsComponent.fromJson(json),
        'mosaic.products-mosaic' => ProductMosaicCmsComponent.fromJson(json),
        'media-list.media-list' => MediaListBannerComponent.fromJson(json),
        'video-banner.video-banner' => VideoBannerComponent.fromJson(json),
        'showcase-list.show-case-list' =>
          ShowCaseListCmsComponentt.fromJson(json),
        'showcase-banner-list.show-case-banner-carrousel' =>
          ShowcaseBannerCarrouselCmsComponent.fromJson(json),
        'personal-showcase.personal-showcase' =>
          PersonalShowcaseCmsComponent.fromJson(json),
        'highlight.highlight-optional-media' =>
          HighlightOptionalCmsComponent.fromJson(json),
        'promotion.promotion-card' => PromotionCardCmsComponent.fromJson(json),
        'coupon-banner.coupon-banner' =>
          CouponBannerCmsComponent.fromJson(json),
        'stories.stories' => StoriesCmsComponent.fromJson(json),
        'search-trend.search-trend' => SearchTrendsCmsComponent.fromJson(json),
        'spot-product-clock.spot-product-clock' =>
          SpotProductClockCmsComponent.fromJson(json),
        'clusters-components.category-banner-cluster' =>
          AzzasClusterCategoryBannerCmsComponent.fromJson(json),
        'tip-bar.static-tip-bar' => StaticTipbarCmsComponent.fromJson(json),
        'tip-bar.animated-tip-bar' => AnimatedTipbarCmsComponent.fromJson(json),
        'product-stamp.stamp' => ProductStampCmsComponent.fromJson(json),
        'tags.product-tag' => ProductTagCmsComponent.fromJson(json),
        'tags.stamp-tag' => StampTagCmsComponent.fromJson(json),
        'tags.pix-tag' => PixTagCmsComponent.fromJson(json),
        'tags.pix-immediate-tag' => PixImmediateTagCmsComponent.fromJson(json),
        'tags.pickup-tag' => PickupTagCmsComponent.fromJson(json),
        'tags.stamp-tag-product' => StampTagProductCmsComponent.fromJson(json),
        'grid-category-listing.grid-category-listing' =>
          GridCategoryListingCmsComponent.fromJson(json),
        'brand-slogan-lockup.brand-slogan' =>
          BrandSloganCmsComponent.fromJson(json),
        'badge-discount.badge-discount' =>
          BadgeDiscountCmsComponent.fromJson(json),
        'banner-content-text.banner-content-text' =>
          BannerContentTextCmsComponent.fromJson(json),
        'banner-content-text.banner-content-text-double-media' =>
          BannerContentDoubleMediaCmsComponent.fromJson(json),
        'etc.products-grid-filter' =>
          ProductsGridFiltersCmsComponent.fromJson(json),
        "etc.home-composition" => HomeCompositionCmsComponent.fromJson(json),
        "etc.image-content" => MediaContentCmsComponent.fromJson(json),
        'list-links.list-links-icons' =>
          ListLinksIconsCmsComponent.fromJson(json),
        'accordion-menu.accordion-menu-etc-item' =>
          AccordionMenuEtcCmsComponentItem.fromJson(json),
        'accordion-menu.accordion-menu-etc' =>
          AccordionMenuEtcCmsComponent.fromJson(json),
        'accordion-menu.content-media-menu' =>
          MenuMediaCmsComponent.fromJson(json),
        "product-carousel-image.product-carousel-image" =>
          ProductCarouselImageCmsComponent.fromJson(json),
        'full-look-carousel.full-look-carousel-item' =>
          FullLookCarouselItem.fromJson(json),
        'full-look-carousel.full-look-carousel' =>
          FullLookCarouselCmsComponent.fromJson(json),
        'menu-collections.menu-collections' =>
          MenuCollectionsCmsComponent.fromJson(json),
        _
            // Não podemos usar 'menu-tab.menu-tab' como identificador fixo
            // pois existe um MenuTab para cada marca, seguindo o padrão
            // menu-tab.menu-tab-${nomedamarca}
            //TODO Definir um nome generico no Strapi pra ficar agnostico a marca
            when componentType is String &&
                componentType.startsWith('menu-tab.menu-tab-crisbarros') =>
          MenuTabCmsComponent.fromJson(json),
        _
            when componentType is String &&
                componentType.startsWith('menu-tab.menu-tab-farm-new') =>
          MenuTabEtcCmsComponent.fromJson(json),
        _
            when componentType is String &&
                componentType.startsWith('menu-tab.menu-tab-farm-etc') =>
          MenuTabEtcCmsComponent.fromJson(json),

        /// Reloginhos
        _
            when componentType is String &&
                componentType.startsWith('clock.clock') =>
          SimpleBodyClockCmsComponent.fromJson(json),
        _
            // O mesmo se aplica ao clock-header
            when componentType is String &&
                componentType.startsWith('clock-promotion.clock-header') =>
          ClockHeaderCmsComponent.fromJson(json),
        _
            // O mesmo se aplica ao clock-product
            when componentType is String &&
                componentType.startsWith('clock-promotion.clock-product') =>
          ClockProductCmsComponent.fromJson(json),

        // Cadastre o seu componente acima
        _ when kDebugMode => UnknownCmsComponent.fromJson(json),
        _ => null,
      };
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      return kDebugMode
          ? ParseErrorCmsComponent(
              error: e,
              stackTrace: st,
              details: 'Component type: $componentType. \nID: ${json['id']}.',
            )
          : null;
    }
  }

  final int id;

  // Talvez poderia ser nullable ou um enum. Esse campo só é retornado pela API
  // para dynamic zones do Strapi.
  final String componentType;
}

class UnknownCmsComponent extends CmsComponent {
  const UnknownCmsComponent({
    required super.id,
    required super.componentType,
  });

  factory UnknownCmsComponent.fromJson(Map<String, dynamic> json) {
    return UnknownCmsComponent(
      id: json['id'],
      componentType: json['__component'],
    );
  }
}

class ParseErrorCmsComponent extends CmsComponent {
  const ParseErrorCmsComponent({
    required this.error,
    required this.stackTrace,
    required this.details,
  }) : super(id: -1, componentType: 'internal-error');
  final Object error;
  final StackTrace stackTrace;
  final String details;
}
