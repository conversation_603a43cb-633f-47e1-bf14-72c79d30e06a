import 'package:azzas_cms/azzas_cms.dart';

/// Representação do conteúdo para deeplink vindo do CMS.

class AzzasCmsDeeplinkDocument {
  const AzzasCmsDeeplinkDocument({
    required this.id,
    required this.attributes,
  });

  factory AzzasCmsDeeplinkDocument.fromJson(Map<String, dynamic> json) {
    return AzzasCmsDeeplinkDocument(
      id: json['id'],
      attributes: CmsDeeplinkDocumentAttributes.fromJson(json['attributes']),
    );
  }

  final int id;
  final CmsDeeplinkDocumentAttributes attributes;
}

class CmsDeeplinkDocumentAttributes<T> {
  const CmsDeeplinkDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.deeplinks,
    required this.invalidDeeplinks,
  });

  factory CmsDeeplinkDocumentAttributes.fromJson(Map<String, dynamic> json) {
    List<String>? invalidDeeplinks =  json['invalid_deeplink'] != null && (json['invalid_deeplink'] is List) ?
    (json['invalid_deeplink'] as List).map((deeplink) => deeplink['invalid_deeplink']).whereType<String>().toList() : null;

    final deeplinkKey = json['deeplinks']  ?? json['valid_deeplinks'] ;

    return CmsDeeplinkDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      deeplinks:deeplinkKey != null ? DeeplinkCmsComponent.listFromJson(deeplinkKey) : null,
      invalidDeeplinks:invalidDeeplinks
    );
  }
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final List<DeeplinkCmsComponent>? deeplinks;
  final List<String>? invalidDeeplinks;
}
