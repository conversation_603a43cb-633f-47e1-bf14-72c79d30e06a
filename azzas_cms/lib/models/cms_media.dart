import 'dart:ui';

class CmsMedia {
  const CmsMedia({
    required this.data,
  });

  factory CmsMedia.fromJson(Map<String, dynamic> json) {
    return CmsMedia(
      data: CmsMediaData.fromJson(json['data']),
    );
  }

  final CmsMediaData data;

  double? get height => data.attributes.height?.toDouble();
  double? get width => data.attributes.width?.toDouble();
}

class CmsMediaData {
  const CmsMediaData({
    required this.id,
    required this.attributes,
  });

  factory CmsMediaData.fromJson(Map<String, dynamic> json) {
    return CmsMediaData(
      id: json['id'],
      attributes: CmsMediaDataAttributes.fromJson(json['attributes']),
    );
  }

  final int id;
  final CmsMediaDataAttributes attributes;

  CmsMedia toCmsMedia() => CmsMedia(data: this);
}

class CmsMediaDataAttributes {
  const CmsMediaDataAttributes({
    required this.width,
    required this.height,
    required this.mime,
    required this.url,
    required this.name,
  });

  factory CmsMediaDataAttributes.fromJson(Map<String, dynamic> json) {
    return CmsMediaDataAttributes(
      width: json['width'],
      height: json['height'],
      mime: json['mime'],
      url: json['url'],
      name: json['name'],
    );
  }

  CmsMediaType get type =>
      mime.startsWith('video') ? CmsMediaType.video : CmsMediaType.image;
  Size? get size => width != null && height != null
      ? Size(width!.toDouble(), height!.toDouble())
      : null;

  double? get aspectRatio {
    final width = this.width;
    final height = this.height;
    if (width != null && height != null) {
      return width / height;
    }
    return null;
  }

  final int? width;
  final int? height;
  final String mime;
  final String url;
  final String name;
}

enum CmsMediaType { video, image }
