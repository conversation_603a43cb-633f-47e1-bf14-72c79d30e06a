

import 'package:azzas_app_commons/azzas_app_commons.dart';

import '../../components/azzas_promotion/azzas_promotion_card_cms_component.dart';

class PromotionCard {
  const PromotionCard(
      {required this.id,
      required this.title,
      required this.subtitle,
      required this.iconUrl,
      required this.feedbackText,
      required this.feedbackSubtitle,
      required this.isActive,
      required this.showProgressBar,
      required this.buttonText,
      required this.buttonNavigateTo,
      this.themeColor});

  factory PromotionCard.fromJson(Map<String, dynamic> json) {
    return PromotionCard(
      id: json['promotion_id'],
      title: json['title'],
      subtitle: json['subtitle'],
      iconUrl: json['icon'] != null ? ImageModel.fromJson(json['icon']).imageUrl : null,
      feedbackText: json['feedback_text'],
      feedbackSubtitle: json['feedback_subtitle'],
      isActive: json['is_active'] ?? false,
      showProgressBar: json['show_progress_bar'] ?? false,
      buttonText: json['button_text'],
      buttonNavigateTo: json['button_navigate_to'],
    );
  }

  factory PromotionCard.fromCmsComponent(PromotionCardCmsComponent component) {
    return PromotionCard(
        id: component.promotionId,
        title: component.title,
        subtitle: component.subtitle,
        iconUrl: component.icon?.data.attributes.url,
        feedbackText: component.feedbackText,
        feedbackSubtitle: component.feedbackSubtitle,
        isActive: component.isActive,
        showProgressBar: component.showProgressBar ?? false,
        buttonText: component.buttonText,
        buttonNavigateTo: component.buttonNavigateTo,
        themeColor: component.themeColor);
  }

  final String? id;
  final String? title;
  final String? subtitle;
  final String? iconUrl;
  final bool isActive;
  final bool showProgressBar;
  final String? buttonText;
  final String? buttonNavigateTo;
  final String? feedbackText;
  final String? feedbackSubtitle;
  final ThemeColor? themeColor;

  bool get hasId => id?.trim().isNotEmpty ?? false;
}
