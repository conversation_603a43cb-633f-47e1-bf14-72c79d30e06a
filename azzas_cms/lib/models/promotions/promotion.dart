class Promotion {
  final String? name;
  final String? description;
  final String? beginDate;
  final String? endDate;
  final String? utmSource;
  final String? status;

  Promotion({
    required this.name,
    required this.description,
    required this.beginDate,
    required this.endDate,
    required this.utmSource,
    required this.status,
  });

  factory Promotion.fromJson(Map<String, dynamic> json) {
    return Promotion(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      beginDate: json['beginDate'] ?? '',
      endDate: json['endDate'] ?? '',
      utmSource: json['utmSource'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'description': description,
        'beginDate': beginDate,
        'endDate': endDate,
        'utmSource': utmSource,
        'status': status,
      };
}
