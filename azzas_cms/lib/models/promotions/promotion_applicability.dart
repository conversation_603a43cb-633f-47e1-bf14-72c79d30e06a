class PromotionApplicability {
  const PromotionApplicability({
    required this.promotionId,
    required this.totalValueToActivate,
    required this.remainingValueToActivate,
    required this.completeness,
    required this.isCompleted,
    required this.valueType,
  });

  factory PromotionApplicability.fromJson(Map<String, dynamic> json) {
    return PromotionApplicability(
      promotionId: json['promotionId'],
      totalValueToActivate: json['totalValueToActivate'],
      remainingValueToActivate: json['remainingValueToActivate'],
      completeness: json['completeness']?.toDouble() ?? 0,
      isCompleted: json['isCompleted'],
      valueType: PromotionApplicabilityValueType.fromString(json['valueType']),
    );
  }

  final String promotionId;
  final num totalValueToActivate;
  final num remainingValueToActivate;
  final double completeness;
  final bool isCompleted;
  final PromotionApplicabilityValueType valueType;
}

enum PromotionApplicabilityValueType {
  number,
  price;

  static PromotionApplicabilityValueType fromString(String str) {
    return values.firstWhere((v) => v.name == str);
  }
}
