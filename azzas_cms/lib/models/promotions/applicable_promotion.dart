import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_cms/components/azzas_promotion/azzas_promotion_card_cms_component.dart';

import 'promotion_card.dart';
import 'promotion_applicability.dart';

class ApplicablePromotion {
  const ApplicablePromotion(
      {required this.promotionId,
      required this.title,
      required this.subtitle,
      required this.iconUrl,
      required this.feedbackText,
      required this.feedbackSubtitle,
      required this.progress,
      required this.showProgress,
      required this.isCompleted,
      required this.buttonText,
      required this.buttonNavigateTo,
      required this.themeColor,
      required this.isActive});

  factory ApplicablePromotion.fromCard(
    PromotionCard promotionCard,
  ) {
    return ApplicablePromotion(
      promotionId: promotionCard.id,
      title: promotionCard.title,
      subtitle: promotionCard.subtitle,
      iconUrl: promotionCard.iconUrl,
      feedbackText: promotionCard.feedbackText,
      feedbackSubtitle: promotionCard.feedbackSubtitle,
      progress: 0,
      showProgress: false,
      isCompleted: false,
      buttonText: promotionCard.buttonText,
      buttonNavigateTo: promotionCard.buttonNavigateTo,
      themeColor: promotionCard.themeColor,
      isActive: promotionCard.isActive,
    );
  }

  factory ApplicablePromotion.fromCardAndApplicability(
    PromotionCard promotionCard,
    PromotionApplicability applicability,
  ) {
    return ApplicablePromotion(
      promotionId: promotionCard.id,
      title: promotionCard.title
          ?.interpolate(applicability.interpolationVariables),
      subtitle: promotionCard.subtitle
          ?.interpolate(applicability.interpolationVariables),
      iconUrl: promotionCard.iconUrl,
      feedbackText: promotionCard.feedbackText,
      feedbackSubtitle: promotionCard.feedbackSubtitle,
      progress: applicability.completeness,
      showProgress: promotionCard.showProgressBar,
      isCompleted: applicability.isCompleted,
      buttonText: promotionCard.buttonText,
      buttonNavigateTo: promotionCard.buttonNavigateTo,
      themeColor: promotionCard.themeColor,
      isActive: promotionCard.isActive,
    );
  }

  final String? promotionId;
  final String? title;
  final String? subtitle;
  final String? iconUrl;
  final String? feedbackText;
  final String? feedbackSubtitle;
  final double progress;
  final bool showProgress;
  final bool isCompleted;
  final String? buttonText;
  final String? buttonNavigateTo;
  final ThemeColor? themeColor;
  final bool? isActive;

  bool get isInProgress => progress > 0 && progress < 1;

  bool get hasButton =>
      buttonText != null &&
      buttonText!.isNotEmpty &&
      buttonNavigateTo != null &&
      buttonNavigateTo!.isNotEmpty;

  bool get hasTitleOrSubtitle =>
      (title?.trim().isNotEmpty ?? false) ||
      (subtitle?.trim().isNotEmpty ?? false);
}

extension on PromotionApplicability {
  Map<String, String> get interpolationVariables {
    return {
      'valor-total-para-habilitar': _formatValue(totalValueToActivate),
      'valor-restante-para-habilitar': _formatValue(remainingValueToActivate),
    };
  }

  String _formatValue(num value) {
    switch (valueType) {
      case PromotionApplicabilityValueType.price:
        return CurrencyHelper.format(amount: value, dividedBy100: true);
      default:
        return '$value';
    }
  }
}
