import 'package:azzas_cms/azzas_cms.dart';

/// Representação do conteúdo para PDP vindo do CMS.
///
/// O conteúdo customizável da PDP funciona de forma diferentes de outras telas
/// como Home, Menu, etc. Na PDP os componentes ficam espalhados pela página ao
/// invés de concentrados em um único local. Por conta disso, a estrutura de
/// cadastro dela é diferente, existindo um campo para cada componente que pode
/// ser colocado na página ao invés de um campo para uma lista de componentes.
class AzzasCmsProductPdpDocument {
  const AzzasCmsProductPdpDocument({
    required this.id,
    required this.attributes,
  });

  factory AzzasCmsProductPdpDocument.fromJson(Map<String, dynamic> json) {
    return AzzasCmsProductPdpDocument(
      id: json['id'],
      attributes: CmsProductPdpDocumentAttributes.fromJson(json['attributes']),
    );
  }

  final int id;
  final CmsProductPdpDocumentAttributes attributes;
}

class CmsProductPdpDocumentAttributes<T> {
  const CmsProductPdpDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.spotProduct,
    required this.highlight,
    required this.showcase,
    required this.highlightOptional,
    required this.productCarouselImage,
  });

  factory CmsProductPdpDocumentAttributes.fromJson(Map<String, dynamic> json) {
    return CmsProductPdpDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      spotProduct: json['spot_product'] is Map
          ? SpotProductCmsComponent.fromJson(json['spot_product'])
          : null,
      highlight: json['highlight'] is Map
          ? HighlightListCmsComponent.fromJson(json['highlight'])
          : null,
      showcase: json['showcase'] is Map
          ? ShowCaseListCmsComponentt.fromJson(json['showcase'])
          : null,
      highlightOptional: json['highlightOptional'] is Map
          ? HighlightOptionalCmsComponent.fromJson(json['highlightOptional'])
          : null,
      productCarouselImage: json['product_carousel_image'] is Map
          ? ProductCarouselImageCmsComponent.fromJson(
              json['product_carousel_image'])
          : null,
    );
  }
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final SpotProductCmsComponent? spotProduct;
  final HighlightListCmsComponent? highlight;
  final ShowCaseListCmsComponentt? showcase;
  final HighlightOptionalCmsComponent? highlightOptional;
  final ProductCarouselImageCmsComponent? productCarouselImage;
}
