import 'package:azzas_cms/azzas_cms.dart';

class StampTagCmsComponent extends CmsComponent {
  final String title;
  final CmsTagColors colors;
  final String filterCategoryOrCluster;


  const StampTagCmsComponent({
    required super.id,
    required super.componentType,
    required this.title,
    required this.colors,
    required this.filterCategoryOrCluster,
  });

  factory StampTagCmsComponent.fromJson(Map<String, dynamic> json) {
    return StampTagCmsComponent(
      id: json['id'] ?? '',
      componentType: json['__component'] ?? '',
      title: json['tag_text'],
      colors: CmsTagColors.fromJson(json['colors']),
      filterCategoryOrCluster: json['filter_category_or_cluster'],
    
    );
  }
}
