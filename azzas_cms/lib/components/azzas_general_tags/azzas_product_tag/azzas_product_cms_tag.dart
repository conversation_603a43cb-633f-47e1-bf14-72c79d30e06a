import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        Product,
        BlocBuilder,
        TagState,
        TagCubit,
        AzzasProductsTagsModel,
        Search,
        Modular;
import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/widgets/azzas_tag.dart';

import 'package:flutter/material.dart';

enum FilterTagOption { onlyDefault, onlyCms, all }

class AzzasProductsCmsTags extends StatefulWidget {
  final Product product;
  final bool isPLP;
  final bool isEtc;
  final EdgeInsets? padding;

  const AzzasProductsCmsTags({
    super.key,
    required this.product,
    this.isPLP = false,
    this.isEtc = false,
    this.padding,
  });

  @override
  State<AzzasProductsCmsTags> createState() => _AzzasProductsCmsTagsState();
}

class _AzzasProductsCmsTagsState extends State<AzzasProductsCmsTags> {
  final _tagCubit = Modular.get<TagCubit>();

  @override
  Widget build(BuildContext context) {
    List<Widget> tags = setTags();

    return BlocBuilder<TagCubit, TagState>(
      bloc: _tagCubit,
      builder: (context, state) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: tags,
        );
      },
    );
  }

  List<Widget> setTags() {
    final cmsProductTags =
        _tagCubit.getProductsTagsByProduct(product: widget.product, isEtc: widget.isEtc);

    return _buildCmsTags(cmsProductTags);
  }

  List<Widget> _buildCmsTags(
    List<AzzasProductsTagsModel> tags,
  ) {
    final tagsList = tags.map((tag) {
      final productTagTheme = AzzasCmsThemeProvider.of(context).productTag;
      void redirectToSearch() {
        final search = Search(
          filterCategoryOrCluster: tag.filterCategoryOrCluster,
          orderBy: tag.orderByIntelligentSearch != null
              ? Search.getOrderByString(
                  tag.orderByIntelligentSearch!,
                )
              : null,
          title: tag.title,
        );
        Modular.to.pushNamed('/searchResult', arguments: search);
      }

      return Padding(
        padding: EdgeInsets.only(right: 8),
        child: CmsAzzasTag(
          tagHeight: widget.isPLP ? CmsTagHeight.small : CmsTagHeight.medium,
          onTap: widget.isPLP ? redirectToSearch : null,
          text: tag.title,
          textColor: tag.titleColor,
          backgroundColor: tag.backgroundColor,
          textStyle: productTagTheme?.textStyle,
          borderColor: tag.borderColor,
          textToIconSpacing: productTagTheme?.textToIconSpacing,
          leftPadding: productTagTheme?.leftPadding,
          rightPadding: productTagTheme?.rightPadding,
          borderRadius: 0,
        ),
      );
    }).toList();
    return tagsList;
  }
}
