import 'package:azzas_cms/azzas_cms.dart';

class StampTagCmsComponent extends CmsComponent {
  final String tagText;
  final String filterCategoryOrCluster;
  final CmsTagColors colors;

  const StampTagCmsComponent({
    required super.id,
    required super.componentType,
    required this.tagText,
    required this.filterCategoryOrCluster,
    required this.colors,
  });

  factory StampTagCmsComponent.fromJson(Map<String, dynamic> json) {
    return StampTagCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      tagText: json['tag_text'],
      filterCategoryOrCluster: json['filter_category_or_cluster'],
      colors: CmsTagColors.fromJson(json['colors']),
    );
  }
}
