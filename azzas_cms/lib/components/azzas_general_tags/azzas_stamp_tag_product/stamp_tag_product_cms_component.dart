import 'package:azzas_cms/azzas_cms.dart';

class StampTagProductCmsComponent extends CmsComponent {
  final String title;
  final CmsTagColors colors;
  final List<String> productId;

  const StampTagProductCmsComponent({
    required super.id,
    required super.componentType,
    required this.title,
    required this.colors,
    required this.productId,
  });

  factory StampTagProductCmsComponent.fromJson(Map<String, dynamic> json) {
    List<String> productsList = (json['products_ids'] as String)
    .split(';')
    .map<String>((id) => id.trim())
    .toList();
    return StampTagProductCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['tag_text'],
      colors: CmsTagColors.fromJson(json['colors']),
      productId: productsList,
    );
  }
}
