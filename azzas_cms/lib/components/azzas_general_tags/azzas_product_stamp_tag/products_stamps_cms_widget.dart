import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AzzasProductsStamps extends StatelessWidget {
  final Product product;

  const AzzasProductsStamps({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final tagCubit = Modular.get<TagCubit>();
    return BlocBuilder<TagCubit, TagState>(
        bloc: tagCubit,
        builder: (context, state) {
          return StreamBuilder<List<ProductStampCmsComponent>>(
            stream: tagCubit.getProductsStampsByProduct(product: product),
            builder: (context, snapshot) {
              final stamps = snapshot.data;
              if (stamps == null || stamps.isEmpty) {
                return const SizedBox.shrink();
              }
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: stamps
                    .map(
                      (e) => Container(
                        margin: const EdgeInsets.only(left: 8, top: 8),
                        height: 48,
                        width: 48,
                        child: e.icon.data.attributes.url.contains('.svg')
                            ? SvgPicture.network(e.icon.data.attributes.url)
                            : AzzasCachedNetworkingImage(
                                imageUrl: e.icon.data.attributes.url,
                              ),
                      ),
                    )
                    .toList(),
              );
            },
          );
        });
  }
}
