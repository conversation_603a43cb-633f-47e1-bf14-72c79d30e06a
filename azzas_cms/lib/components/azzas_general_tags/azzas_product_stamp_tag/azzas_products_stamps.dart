import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AzzasProductsStamps extends StatelessWidget {
  final Product product;

  const AzzasProductsStamps({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final tagCubit = Modular.get<TagCubit>();

    return BlocBuilder<TagCubit, TagState>(
      bloc: tagCubit,
      builder: (context, state) {
        return StreamBuilder<List<ProductStampCmsComponent>>(
          stream: tagCubit.getProductsStampsByProduct(product: product),
          builder: (context, snapshot) {
            final stamps = snapshot.data;
            if (stamps == null || stamps.isEmpty) {
              return const SizedBox.shrink();
            }

            return Column(
              children: stamps
                  .map((stamp) => _buildStamp(
                        imageUrl: stamp.icon.data.attributes.url,
                      ))
                  .toList(),
            );
          },
        );
      },
    );
  }

  Widget _buildStamp({String? imageUrl}) {
    if (imageUrl == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(
        left: 8,
        top: 8,
      ),
      height: 48,
      width: 48,
      child: imageUrl.contains('.svg')
          ? SvgPicture.network(imageUrl)
          : AzzasCachedNetworkingImage(
              imageUrl: imageUrl,
              fit: BoxFit.contain,
            ),
    );
  }
}
