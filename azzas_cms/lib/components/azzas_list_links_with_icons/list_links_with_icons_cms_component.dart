import 'package:azzas_cms/azzas_cms.dart';

class ListLinksIconsCmsComponent extends CmsComponent {
  const ListLinksIconsCmsComponent({
    required super.id,
    required super.componentType,
    required this.items,
  });

  factory ListLinksIconsCmsComponent.fromJson(Map<String, dynamic> json) {
    return ListLinksIconsCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      items: (json['items'] as List).tryMap((j) => ListLinksIconsItemsCmsComponent.fromJson(j)).toList(),
    );
  }

  final List<ListLinksIconsItemsCmsComponent> items;
}

class ListLinksIconsItemsCmsComponent {
  const ListLinksIconsItemsCmsComponent({
    required this.title,
    required this.showNewTag,
    this.navigateTo,
    this.icon,
  });

  factory ListLinksIconsItemsCmsComponent.fromJson(Map<String, dynamic> json) {
    return ListLinksIconsItemsCmsComponent(
      title: json['title'],
      showNewTag: json['show_new_tag'],
      navigateTo: json['navigate_to'],
      icon: json['icon'] is Map && json['icon']['data'] is Map
          ? CmsMedia.fromJson(json['icon'])
          : null,
    );
  }

  final String title;
  final String? navigateTo;
  final CmsMedia? icon;
  final bool showNewTag;
}
