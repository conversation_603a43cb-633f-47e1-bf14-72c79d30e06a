import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_circular_text/circular_text.dart';

class ListLinksWithIconsCmsWidget extends StatelessWidget {
  final List<ListLinksIconsItemsCmsComponent> items;
  const ListLinksWithIconsCmsWidget({
    super.key,
    required this.items,
  });

  factory ListLinksWithIconsCmsWidget.fromComponent(
    ListLinksIconsCmsComponent component,
  ) {
    return ListLinksWithIconsCmsWidget(
      items: component.items,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer(
      child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          itemBuilder: (BuildContext context, int index) {
            return ListLinksIconsItem(
              title: items[index].title,
              showNewTag: items[index].showNewTag,
              icon: items[index].icon,
              navigateTo: items[index].navigateTo,
            );
          }),
    );
  }
}

class ListLinksIconsItem extends StatelessWidget {
  final String? navigateTo;
  final CmsMedia? icon;
  final String title;
  final bool showNewTag;

  const ListLinksIconsItem(
      {super.key,
      this.navigateTo,
      this.icon,
      required this.title,
      required this.showNewTag});

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).listLinksWithIconsTheme;
    final hasIcon = icon?.data.attributes.url.isNotEmpty ?? false;
    return AzzasCmsContentSpacer(
      child: Padding(
        padding:
            EdgeInsets.only(top: 8.0, bottom: 8.0, left: 16.0, right: 24.0),
        child: GestureDetector(
          onTap: navigateTo != null
              ? () => NavigatorDynamic.call(navigateTo!)
              : null,
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    if (hasIcon) ...[
                      Image.network(
                        icon!.data.attributes.url,
                        height: 32,
                        width: 32,
                      ),
                      SizedBox(
                        width: 8.0,
                      ),
                    ],
                    Text(
                      title,
                      style: theme.titleStyle,
                    ),
                    if (showNewTag) ...[
                      Transform.translate(
                        offset: const Offset(-8, 0),
                        child: CircularText(
                          children: [
                            TextItem(
                              text: Text(
                                "NOVO",
                                style: theme.tagStyle,
                              ),
                              space: 25,
                              startAngle: -50,
                              startAngleAlignment: StartAngleAlignment.center,
                              direction: CircularTextDirection.clockwise,
                            ),
                          ],
                          radius: 10,
                          position: CircularTextPosition.outside,
                          backgroundPaint: Paint()..color = Colors.transparent,
                        ),
                      )
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_right_alt_sharp,
                size: 32,
              )
            ],
          ),
        ),
      ),
    );
  }
}
