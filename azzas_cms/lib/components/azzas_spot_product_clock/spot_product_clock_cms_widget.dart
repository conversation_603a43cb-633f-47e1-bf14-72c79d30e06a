import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class SpotProductClockCmsWidget extends StatefulWidget {
  const SpotProductClockCmsWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.callToAction,
    required this.coupon,
    required this.filterCategoryOrCluster,
    required this.orderBy,
    required this.startTime,
    required this.endTime,
    required this.textColor,
    required this.backgroundColor,
    this.onTap,
  });

  final String? title;
  final String? subtitle;
  final String? callToAction;
  final String? coupon;
  final String filterCategoryOrCluster;
  final String? orderBy;
  final DateTime startTime;
  final DateTime endTime;
  final Color? textColor;
  final Color? backgroundColor;
  final ValueChanged<Product>? onTap;

  @override
  State<SpotProductClockCmsWidget> createState() =>
      _SpotProductClockCmsWidgetState();

  factory SpotProductClockCmsWidget.fromSpotProductClockComponent(
    SpotProductClockCmsComponent component, {
    ValueChanged<Product>? onTap,
  }) {
    return SpotProductClockCmsWidget(
      title: component.title,
      subtitle: component.subtitle,
      callToAction: component.callToAction,
      coupon: component.coupon,
      filterCategoryOrCluster: component.filterCategoryOrCluster,
      orderBy: component.orderBy,
      startTime: component.startTime,
      endTime: component.endTime,
      textColor: component.textColor,
      backgroundColor: component.backgroundColor,
    );
  }
}

class _SpotProductClockCmsWidgetState extends State<SpotProductClockCmsWidget> {
  final cubit = SpotProductClockCubit();

  @override
  void initState() {
    super.initState();
    cubit.startTimer(widget.endTime);
  }

  @override
  void dispose() {
    cubit.onDispose();
    super.dispose();
  }

  void _onTapProduct(Product product, int childPosition) {
    product.updateProductHero(SpotProductCmsWidget);
    widget.onTap?.call(product);
  }

  Search get search {
    return Search(
      title: widget.title,
      filterCategoryOrCluster: widget.filterCategoryOrCluster,
      orderBy: widget.orderBy != null
          ? Search.getOrderByString(widget.orderBy!)
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).spotProduct;
    final clockTheme = AzzasCmsThemeProvider.of(context).spotProductClock;

    final clock = clockTheme?.simpleClockBuilder;

    return BlocBuilder<SpotProductClockCubit, SpotProductClockState>(
      bloc: cubit,
      buildWhen: (previous, current) => previous.isActive != current.isActive,
      builder: (context, state) {
        final isActive = state.isActive ?? false;

        if (!isActive) {
          return const SizedBox.shrink();
        }

        if (widget.startTime.isAfter(DateTime.now())) {
          return const SizedBox.shrink();
        }

        return AzzasCmsContentSpacer(
          child: Container(
            margin: const EdgeInsets.only(top: 24),
            color: widget.backgroundColor ?? clockTheme?.backgroundColor,
            child: AzzasSpotProductCarousel(
              noMargin: true,
              onTapProduct: (Product product, int childPosition) {
                widget.onTap != null
                    ? _onTapProduct(product, childPosition)
                    : Modular.to.pushNamed('/pdp', arguments: product);
              },
              search: search,
              textPrimary: widget.title,
              textSecondary: widget.subtitle,
              callToAction: widget.callToAction,
              screenClass: SpotProductCmsWidget.screenClass,
              leftPaddingComponent: theme?.leftPaddingComponent ?? 24,
              hasFavoriteButton: true,
              ctaIsBelowHeader: true,
              clock: clock != null
                  ? BlocBuilder<SpotProductClockCubit, SpotProductClockState>(
                      bloc: cubit,
                      builder: (context, state) {
                        return clock(
                          remainingTime: state.timeStream ?? Duration.zero,
                          coupon: widget.coupon,
                          foregroundColor: widget.textColor,
                          backgroundColor: widget.backgroundColor,
                          isLoadingCoupon: cubit.state.isLoadingCoupon,
                          isCouponApplied:
                              cubit.isCouponApplied(widget.coupon ?? ''),
                          onTapCoupon: () async {
                            try {
                              await cubit.applyCoupon(widget.coupon ?? '');
                              if (!context.mounted) return;
                              if (cubit.isCouponApplied(widget.coupon ?? '')) {
                                _showAzzasSnackBar(
                                  context: context,
                                  message:
                                      "cupom aplicado com sucesso em sua sacola!",
                                );
                              }
                            } catch (e) {
                              if (!context.mounted) return;
                              _showAzzasSnackBar(
                                context: context,
                                message: "o cupom informado é inválido",
                                isError: true,
                              );
                            }
                          },
                        );
                      })
                  : const SizedBox.shrink(),
              textLeftSpacing: theme?.textLeftSpacing ?? 16,
              containerHeight: theme?.containerHeight,
              textTopSpacing: theme?.textTopSpacing ?? 24,
              textPrimaryStyle:
                  theme?.textPrimaryStyle ?? const TextStyle(fontSize: 20),
              textSecondaryStyle:
                  theme?.textSecondaryStyle ?? const TextStyle(fontSize: 16),
              topPadding: theme?.topCallToActionPadding,
              callToActionIcon: theme?.callToActionIcon,
              callToActionTextStyle: theme?.callToActionTextStyle,
              productTitleStyle: theme?.productTitleStyle,
              productTitleBottomSpacing: theme?.productTitleBottomSpacing,
              priceTextStyle: theme?.priceTextStyle,
              oldPriceTextStyle: theme?.oldPriceTextStyle,
              installmentsTextStyle: theme?.installmentsTextStyle,
              onTapCallToAction: () => NavigatorDynamic.call(
                  "plp/${widget.filterCategoryOrCluster}"),
              hasInstallmentsText: theme?.hasInstallmentsText ?? false,
            ),
          ),
        );
      },
    );
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    bool isError = false,
    required String message,
  }) {
    final clockTheme = AzzasCmsThemeProvider.of(context).spotProductClock;
    final Color? backgroundColor =
        isError ? clockTheme?.errorColor : clockTheme?.successColor;

    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
    );
  }
}
