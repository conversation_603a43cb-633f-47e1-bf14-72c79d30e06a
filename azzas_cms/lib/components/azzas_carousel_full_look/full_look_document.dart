import 'package:azzas_cms/azzas_cms.dart';

class FullLookCmsDocument {
  const FullLookCmsDocument({
    required this.id,
    required this.attributes,
  });
  final int id;
  final FullLookCmsDocumentAttributes attributes;

  factory FullLookCmsDocument.fromJson(Map<String, dynamic> json) {
    return FullLookCmsDocument(
      id: json['id'],
      attributes: FullLookCmsDocumentAttributes.fromJson(json['attributes']),
    );
  }
}

class FullLookCmsDocumentAttributes {
  const FullLookCmsDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    this.title,
    this.subtitle,
    required this.components,
  });

  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final String? title;
  final String? subtitle;
  final List<CmsComponent> components;

  factory FullLookCmsDocumentAttributes.fromJson(Map<String, dynamic> json) {
    List<CmsComponent> components = json['components'] != null
        ? (json['components'] as List)
            .tryMap((j) => CmsComponent.fromJson(j))
            .whereType<CmsComponent>()
            .toList()
        : [];

    return FullLookCmsDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      title: json['title'],
      subtitle: json['subtitle'],
      components: components,
    );
  }
}
