import 'package:azzas_app_commons/azzas_app_commons.dart';

class FullLookCarouselCmsComponent extends CmsComponent {
  FullLookCarouselCmsComponent({
    required super.id,
    required super.componentType,
    required this.items,
    required this.title,
    this.description,
    this.callToAction,
  });

  factory FullLookCarouselCmsComponent.fromJson(Map<String, dynamic> json) {
    return FullLookCarouselCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      items: (json['items'] as List)
          .tryMap((j) => FullLookCarouselCmsComponentItem.fromJson(j))
          .toList(),
      title: json['title'],
      description: json['subtitle'],
      callToAction: json['call_to_action'],
    );
  }

  final List<FullLookCarouselCmsComponentItem> items;
  final String title;
  final String? description;
  final String? callToAction;
}

class FullLookCarouselCmsComponentItem {
  FullLookCarouselCmsComponentItem({
    required this.id,
    required this.productIdList,
    required this.image,
    required this.bannerNameGA4,
    this.buttonText,
  });

  factory FullLookCarouselCmsComponentItem.fromJson(Map<String, dynamic> json) {
    final productIds = getProductIdList(json['product_ids']);
    return FullLookCarouselCmsComponentItem(
      id: json['id'],
      productIdList: productIds,
      image: CmsMedia.fromJson(json['image']),
      bannerNameGA4: BannerNameGA4.fromString(json['banner_name_ga4']),
      buttonText: json['button_text'],
    );
  }

  final int id;
  final List<String> productIdList;
  final CmsMedia image;
  final BannerNameGA4 bannerNameGA4;
  final String? buttonText;

  static List<String> getProductIdList(String? productIds) {
    if (productIds == null) return [];
    return productIds.split(";").map((e) => e.trim()).toList();
  }
}

class FullLookCarouselItem extends CmsComponent {
  FullLookCarouselItem({
    required super.id,
    required super.componentType,
    required this.productIdList,
    required this.image,
    required this.bannerNameGA4,
  });

  factory FullLookCarouselItem.fromJson(Map<String, dynamic> json) {
    final productIds = getProductIdList(json['product_ids']);
    return FullLookCarouselItem(
      id: json['id'],
      componentType: json['__component'],
      productIdList: productIds,
      image: CmsMedia.fromJson(json['image']),
      bannerNameGA4: BannerNameGA4.fromString(json['banner_name_ga4']),
    );
  }

  final List<String> productIdList;
  final CmsMedia image;
  final BannerNameGA4 bannerNameGA4;

  static List<String> getProductIdList(String? productIds) {
    if (productIds == null) return [];
    return productIds.split(";").map((e) => e.trim()).toList();
  }
}
