import 'package:flutter/material.dart';

class AzzasFullLookCarouselTheme {
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final TextStyle? callToActionStyle;
  final IconData? callToActionIcon;
  final Color? callToActionIconColor;
  final IconData? closeIcon;
  final FullLookBottomShetTheme? bottomSheetTheme;

  const AzzasFullLookCarouselTheme({
    this.titleStyle,
    this.descriptionStyle,
    this.callToActionStyle,
    this.callToActionIcon,
    this.callToActionIconColor,
    this.closeIcon,
    this.bottomSheetTheme,
  });
}

class FullLookBottomShetTheme {
  final TextStyle productNameStyle;
  final TextStyle productCurrentPriceStyle;
  final TextStyle productPriceStyle;
  final TextStyle productSizeStyle;

  final TextStyle? productInBagStyle;
  final IconData? productInBagIcon;
  final Color? productInBagIconColor;
  final Color? productInBagBackgroundColor;

  const FullLookBottomShetTheme({
    required this.productNameStyle,
    required this.productCurrentPriceStyle,
    required this.productPriceStyle,
    required this.productSizeStyle,
    this.productInBagStyle,
    this.productInBagIcon,
    this.productInBagIconColor,
    this.productInBagBackgroundColor,
  });
}
