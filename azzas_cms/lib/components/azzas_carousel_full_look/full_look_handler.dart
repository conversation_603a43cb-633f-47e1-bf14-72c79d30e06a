import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class FullLookHandler {
  static Future<List<Product>> getProductsByIds(
      List<String> productList) async {
    final productsByIdsUseCase = Modular.get<GetProductsByIdsUseCase>();
    try {
      final productIdIntList = productList.map((id) => int.parse(id)).toList();
      final response = await productsByIdsUseCase.call(ids: productIdIntList);
      return response;
    } catch (e, st) {
      debugPrint('Erro ao carregar produtos do full look carousel: $e');
      debugPrintStack(stackTrace: st);
      return [];
    }
  }
}
