import 'package:collection/collection.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';


class PromotionCardCmsComponent extends CmsComponent {
  const PromotionCardCmsComponent({
    required super.id,
    required super.componentType,
    required this.isActive,
    required this.promotionId,
    required this.title,
    required this.subtitle,
    required this.feedbackText,
    required this.feedbackSubtitle,
    required this.buttonText,
    required this.buttonNavigateTo,
    required this.showProgressBar,
    required this.icon,
    required this.themeColor,
  });

  factory PromotionCardCmsComponent.fromJson(Map<String, dynamic> json) {
    return PromotionCardCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      isActive: json['is_active'],
      promotionId: json['promotion_id'],
      title: json['title'],
      subtitle: json['subtitle'],
      feedbackText: json['feedback_text'],
      feedbackSubtitle: json['feedback_subtitle'],
      buttonText: json['button_text'],
      buttonNavigateTo: json['button_navigate_to'],
      showProgressBar: json['show_progress_bar'],
      themeColor: ThemeColor.fromString(json['theme']),
      icon: json['icon'] is Map && json['icon']['data'] is Map
          ? CmsMedia.fromJson(json['icon'])
          : null,
    );
  }

  final bool isActive;
  final String? promotionId;
  final String? title;
  final String? subtitle;
  final String? feedbackText;
  final String? feedbackSubtitle;
  final String? buttonText;
  final String? buttonNavigateTo;
  final bool? showProgressBar;
  final CmsMedia? icon;
  final ThemeColor? themeColor;
}

enum ThemeColor {
  light('claro'),
  dark('escuro');

  const ThemeColor(this.cmsName);

  final String cmsName;

  static ThemeColor? fromString(String? str) {
    return values.firstWhereOrNull((v) => v.cmsName == str);
  }
}
