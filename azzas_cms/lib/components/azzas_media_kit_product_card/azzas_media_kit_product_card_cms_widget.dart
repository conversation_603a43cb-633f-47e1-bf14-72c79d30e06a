import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class AzzasMediaKitProductCardCmsWidget extends StatefulWidget {
  final String imageUrl;
  final String? navigateTo;
  final int? position;
  final String? cta;
  final List<String>? productIdList;
  final Color? textColor;
  final Color? backgroundColor;

  /// Sobrescreve método executado ao clicar no botão
  final VoidCallback? onPressed;

  const AzzasMediaKitProductCardCmsWidget({
    super.key,
    required this.imageUrl,
    this.navigateTo,
    this.position,
    this.cta,
    this.productIdList,
    this.textColor,
    this.backgroundColor,
    this.onPressed,
  });

  AzzasMediaKitProductCardCmsWidget.fromCmsComponent(
      MediaKiProductCardCmsComponent component, int? index,
      {Key? key, VoidCallback? onPressed})
      : this(
          key: key,
          imageUrl: component.media.data.attributes.url,
          navigateTo: component.navigateTo,
          position: index,
          cta: component.cta,
          productIdList: component.productIdList,
          textColor: component.textColor,
          backgroundColor: component.backgroundColor,
          onPressed: onPressed,
        );

  @override
  State<AzzasMediaKitProductCardCmsWidget> createState() =>
      _AzzasMediaKitProductCardCmsWidgetState();
}

class _AzzasMediaKitProductCardCmsWidgetState
    extends State<AzzasMediaKitProductCardCmsWidget> {
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            AzzasCachedNetworkingImage(
              imageUrl: widget.imageUrl,
              width: double.infinity,
              height: constraints.hasBoundedHeight ? double.infinity : null,
            ),
            isLoading ? _buildLoadingOverlay() : _buildInkWell(),
            if (widget.cta != null &&
                (widget.productIdList?.isNotEmpty ?? false)) ...[
              Positioned(
                bottom: 24,
                left: 0,
                right: 0,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: AzzasPrimaryButton(
                    style: AzzasButtonStyle(
                      backgroundColor: widget.backgroundColor,
                      borderColor: widget.backgroundColor,
                    ),
                    size: ButtonSize.small,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.cta!,
                          style: TextStyle(
                            color: widget.textColor,
                          ),
                        ),
                        SizedBox(
                          width: 8.0,
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12.0,
                          color: widget.textColor,
                        )
                      ],
                    ),
                    onPressed: () {
                      if (widget.onPressed != null) {
                        widget.onPressed!();
                      } else {
                        Modular.to.pushNamed('/buy_look_page',
                            arguments: widget.productIdList);
                      }
                    },
                  ),
                ),
              )
            ]
          ],
        );
      },
    );
  }

  Widget _buildLoadingOverlay() {
    return Positioned.fill(
      child: ColoredBox(
        color: Colors.black.withOpacity(0.5),
        child: const Center(
          child: AzzasSpinner(),
        ),
      ),
    );
  }

  Widget _buildInkWell() {
    return Positioned.fill(
      child: Material(
        color: Colors.transparent,
        child: widget.navigateTo.isNotNullOrEmpty
            ? InkWell(
                onTap: () {
                  NavigatorDynamic.call(widget.navigateTo!);
                },
              )
            : const SizedBox.shrink(),
      ),
    );
  }
}
