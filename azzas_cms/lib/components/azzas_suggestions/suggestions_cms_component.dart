
import 'package:azzas_cms/azzas_cms.dart';

class SuggestionCmsComponent extends SuggestionsCmsComponent {
  const SuggestionCmsComponent({
    required this.cards,
    required super.id,
    required super.componentType,
  });

  factory SuggestionCmsComponent.fromJson(Map<String, dynamic> json) {
    return SuggestionCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      cards: SuggetionsCards.fromJson(json['Cards']),
    );
  }

  final SuggetionsCards cards;
}

class SuggetionsCards {
  SuggetionsCards({this.cards});

  factory SuggetionsCards.fromJson(json) {
    List<SuggestionsCard> formatedCards =
        SuggestionsCard.parseListInfos(json, SuggestionsCard.fromJson);

    return SuggetionsCards(
      cards: formatedCards,
    );
  }

  final List<SuggestionsCard>? cards;
}

class SuggestionsCard {
  final String title;
  final String subtitle;
  final List<String> productList;
  final String image;
  final String description;
  final String? videoThumb;
  final String? descriptionVideo;
  final String? videoUrl;

  SuggestionsCard({
    required this.title,
    required this.subtitle,
    required this.productList,
    required this.image,
    required this.description,
    this.videoThumb,
    this.descriptionVideo,
    this.videoUrl,
  });

  factory SuggestionsCard.fromJson(Map<String, dynamic> json) {
    List<String> listProducts = (json['product_list'] as String)
        .split(';')
        .map((String e) => e.trim())
        .whereType<String>()
        .toList();

    return SuggestionsCard(
      title: json['title'],
      subtitle: json['subtitle'],
      image: CmsMedia.fromJson(json['image']).data.attributes.url,
      productList: listProducts.where((element) => element.isNotEmpty).toList(),
      description: json['description'],
      videoThumb: json['video_thumb']['data'] != null
          ? CmsMedia.fromJson(json['video_thumb']).data.attributes.url
          : null,
      descriptionVideo: json['description_video'],
      videoUrl: json['video_url'],
    );
  }

  factory SuggestionsCard.fromJsonBenefits(Map<String, dynamic> json) {
    List<String> listProducts = (json['product_list'] as String)
        .split(';')
        .map((String e) => e.trim())
        .whereType<String>()
        .toList();
    return SuggestionsCard(
      title: json['title'],
      subtitle: json['subtitle'],
      image: CmsMedia.fromJson(json['image']).data.attributes.url,
      productList: listProducts.where((element) => element.isNotEmpty).toList(),
      description: json['description'],
      videoThumb: json['video_thumb']['data'] != null
          ? CmsMedia.fromJson(json['video_thumb']).data.attributes.url
          : null,
      descriptionVideo: json['description_video'],
      videoUrl: json['video_ulr'],
    );
  }

  static List<SuggestionsCard> parseListInfos(List<dynamic> cards,
      SuggestionsCard Function(Map<String, dynamic>) fromJsonMethod) {
    return cards.map((item) {
      return fromJsonMethod(item as Map<String, dynamic>);
    }).toList();
  }
}

class SuggestionsTitle extends SuggestionsCmsComponent {
  const SuggestionsTitle({
    required this.title,
    required super.id,
    required super.componentType,
  });

  factory SuggestionsTitle.fromJson(Map<String, dynamic> json) {
    return SuggestionsTitle(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
    );
  }

  final String title;
}
