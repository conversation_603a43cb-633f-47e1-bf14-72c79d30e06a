import 'package:azzas_cms/azzas_cms.dart';

class SuggetionsCmsDocument {
  const SuggetionsCmsDocument({
    required this.id,
    required this.attributes,
  });
  final int id;
  final SuggetionsCmsDocumentAttributes attributes;

  factory SuggetionsCmsDocument.fromJson(Map<String, dynamic> json) {
    return SuggetionsCmsDocument(
      id: json['id'],
      attributes: SuggetionsCmsDocumentAttributes.from<PERSON>son(json['attributes']),
    );
  }
}

class SuggetionsCmsDocumentAttributes {
  const SuggetionsCmsDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.components,
  });

  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final List<SuggestionsCmsComponent> components;

  factory SuggetionsCmsDocumentAttributes.fromJson(Map<String, dynamic> json) {
    List<SuggestionsCmsComponent> components = json['components'] != null
        ? (json['components'] as List)
            .tryMap((j) => SuggestionsCmsComponent.fromJson(j))
            .whereType<SuggestionsCmsComponent>()
            .toList()
        : [];

    return SuggetionsCmsDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      components: components,
    );
  }
}

abstract class SuggestionsCmsComponent extends CmsComponent {
  const SuggestionsCmsComponent({
    required super.id,
    required super.componentType,
  });

  static SuggestionsCmsComponent? fromJson(Map<String, dynamic> json) {
    var componentType = json['__component'];
    try {
      return switch (componentType) {
        'suggestions.suggestions-title' => SuggestionsTitle.fromJson(json),
        'suggestions.cards' => SuggestionCmsComponent.fromJson(json),
        _ => null,
      };
    } catch (e) {
      return null;
    }
  }
}
