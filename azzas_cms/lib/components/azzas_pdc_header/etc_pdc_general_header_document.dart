import 'package:azzas_cms/components/azzas_pdc_header/azzas_pdc_header_exp.dart';

class EtcPdcGeneralHeaderDocument {
  EtcPdcGeneralHeaderDocument({
    required this.id,
    required this.attributes,
  });

  factory EtcPdcGeneralHeaderDocument.fromJson(Map<String, dynamic> json) {
    return EtcPdcGeneralHeaderDocument(
      id: json['id'],
      attributes:
          EtcPdcGeneralHeaderDocumentAttributes.fromJson(json['attributes']),
    );
  }
  final int id;
  final EtcPdcGeneralHeaderDocumentAttributes attributes;

  EtcPdcHeaderCmsComponent get header => attributes.header;
}

class EtcPdcGeneralHeaderDocumentAttributes {
  const EtcPdcGeneralHeaderDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.header,
  });

  factory EtcPdcGeneralHeaderDocumentAttributes.fromJson(
      Map<String, dynamic> json) {
    return EtcPdcGeneralHeaderDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      header: EtcPdcHeaderCmsComponent.fromJson(json['Header']),
    );
  }

  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final EtcPdcHeaderCmsComponent header;
}
