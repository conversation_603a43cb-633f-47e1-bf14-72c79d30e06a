import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class EtcPdcHeaderCmsWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final List<CmsMedia>? medias;
  final CmsMedia? logo;

  const EtcPdcHeaderCmsWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.medias,
    required this.logo,
  });

  factory EtcPdcHeaderCmsWidget.fromComponent(
      EtcPdcHeaderCmsComponent component) {
    return EtcPdcHeaderCmsWidget(
      title: component.title,
      subtitle: component.subtitle,
      medias: component.medias,
      logo: component.logo,
    );
  }

  Widget _playPauseButtonBuilder(AnimationController controller) {
    return Container(
      padding: EdgeInsets.all(30.0),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.2),
      ),
      child: AnimatedIcon(
        progress: controller,
        icon: AnimatedIcons.pause_play,
        color: Colors.white,
      ),
    );
  }

  Widget _buildLogo() {
    if (logo == null) return SizedBox.shrink();

    return Positioned(
      bottom: -10.0,
      child: AzzasImage(
        image: NetworkImage(logo!.data.attributes.url),
        fit: BoxFit.cover,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final firstValidMediaHeight =
        medias?.firstWhere((media) => media.data.attributes.height != null);

    final mediaHeight = firstValidMediaHeight?.height ?? 0.0;

    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: 380.0,
          ),
          child: Container(
            height: mediaHeight.toDouble(),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: medias?.length ?? 0,
              addAutomaticKeepAlives: true,
              itemBuilder: (_, index) {
                final media = medias?[index];
                if (media == null) return SizedBox.shrink();

                final mediaType = media.data.attributes.type;
                final mediaUrl = media.data.attributes.url;

                if (mediaType == CmsMediaType.video) {
                  return AzzasVideo(
                    keepAlive: true,
                    videoType: VideoType.network,
                    mediaUrl: mediaUrl,
                    looping: true,
                    startVideo: false,
                    showButtonPlay: true,
                    autoHeight: true,
                    mixWithOthers: true,
                    autoWidth: true,
                    playPauseButtonBuilder: _playPauseButtonBuilder,
                    playPauseButtonPosition:
                        AzzasPlayPauseButtonPosition.center,
                    loadingBuilder: (_) => Padding(
                      padding: EdgeInsets.all(20.0),
                      child: AzzasSpinner(),
                    ),
                  );
                }
                return AzzasImage(
                  imageWidth: MediaQuery.of(context).size.width,
                  image: NetworkImage(mediaUrl),
                );
              },
            ),
          ),
        ),
        _buildLogo(),
      ],
    );
  }
}
