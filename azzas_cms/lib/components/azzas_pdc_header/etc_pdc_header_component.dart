import 'package:azzas_app_commons/azzas_app_commons.dart';

class EtcPdcHeaderCmsComponent extends CmsComponent {
  final String? title;
  final String? subtitle;
  final List<CmsMedia>? medias;
  final CmsMedia? logo;

  EtcPdcHeaderCmsComponent({
    this.title,
    this.subtitle,
    this.medias,
    this.logo,
    required super.id,
    required super.componentType,
  });

  factory EtcPdcHeaderCmsComponent.fromJson(Map<String, dynamic> json) {
    final mediasData = json['medias']?['data'] as List<dynamic>?;
    final medias = mediasData
        ?.map((media) => CmsMediaData.fromJson(media).toCmsMedia())
        .toList();
    final logo =
        json['logo']?['data'] != null ? CmsMedia.fromJson(json['logo']) : null;

    return EtcPdcHeaderCmsComponent(
      id: json['id'],
      componentType: json['__component'] ?? "",
      title: json['title'],
      subtitle: json['subtitle'],
      medias: medias,
      logo: logo,
    );
  }
}
