import 'package:flutter/material.dart';

class AzzasMenuCollectionsTheme {
  final TextStyle titleStyle;
  final TextStyle termsStyle;
  final Color? borderColor;
  final Color? buttonColor;
  final Color? lineColor;

  const AzzasMenuCollectionsTheme({
    required this.titleStyle,
    required this.termsStyle,
    this.borderColor,
    this.buttonColor,
    this.lineColor,
  });

  AzzasMenuCollectionsTheme copyWith({
    TextStyle? titleStyle,
    TextStyle? termsStyle,
    Color? borderColor,
    Color? buttonColor,
    Color? lineColor,
  }) {
    return AzzasMenuCollectionsTheme(
      titleStyle: titleStyle ?? this.titleStyle,
      termsStyle: termsStyle ?? this.termsStyle,
      borderColor: borderColor ?? this.borderColor,
      buttonColor: buttonColor ?? this.buttonColor,
      lineColor: lineColor ?? this.lineColor,
    );
  }
}
