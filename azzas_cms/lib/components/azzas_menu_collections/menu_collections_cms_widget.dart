import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class MenuCollectionsCmsWidget extends StatefulWidget {
  const MenuCollectionsCmsWidget({
    super.key,
    required this.document,
    required this.menuCubit,
    this.title,
  });
  final String document;
  final MenuCubit menuCubit;
  final String? title;

  @override
  State<MenuCollectionsCmsWidget> createState() =>
      _MenuCollectionsCmsWidgetState();
}

class _MenuCollectionsCmsWidgetState extends State<MenuCollectionsCmsWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).menuCollectionsTheme;

    return FadeInFutureBuilder<List<MenuCollectionsCmsComponentItem>?>(
      future: widget.menuCubit.getMenuCollections(widget.document),
      builder: (context, collections) {
        if (collections == null || collections.isEmpty) {
          return const SizedBox.shrink();
        }

        final hasImage = collections.any((c) => c.media != null);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AzzasLine(
              lineColor: theme?.lineColor,
              lineSize: AzzasLineSize.small,
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0, top: 24.0),
              child: Text(
                widget.title ?? 'Coleções',
                style: theme?.titleStyle,
              ),
            ),
            Container(
              constraints: BoxConstraints(maxHeight: hasImage ? 200 : 60),
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, index) => SizedBox(
                  width: 12,
                ),
                itemCount: collections.length,
                itemBuilder: (context, index) {
                  final collection = collections[index];

                  return GestureDetector(
                    onTap: () {
                      NavigatorDynamic.call(collection.navigateTo);
                    },
                    child: collection.media != null
                        ? CollectionWithImage(
                            callToAction: collection.callToAction,
                            imageUrl: collection.media!.data.attributes.url,
                            textStyle: theme?.termsStyle,
                            buttonColor: theme?.buttonColor,
                          )
                        : CollectionButtonOnly(
                            callToAction: collection.callToAction,
                            borderColor: theme?.borderColor,
                            textStyle: theme?.termsStyle,
                          ),
                  );
                },
              ),
            ),
            SizedBox(
              height: 24.0,
            ),
            AzzasLine(
              lineColor: theme?.lineColor,
              lineSize: AzzasLineSize.small,
            ),
          ],
        );
      },
    );
  }
}

class CollectionWithImage extends StatelessWidget {
  final String imageUrl;
  final String callToAction;
  final TextStyle? textStyle;
  final Color? buttonColor;

  const CollectionWithImage({
    super.key,
    required this.imageUrl,
    required this.callToAction,
    this.textStyle,
    this.buttonColor,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 22 / 30,
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(imageUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                bottom: 12,
                left: 4,
                right: 4,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: buttonColor,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: Text(
                      callToAction,
                      style: textStyle,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class CollectionButtonOnly extends StatelessWidget {
  final String callToAction;
  final TextStyle? textStyle;
  final Color? borderColor;

  const CollectionButtonOnly({
    super.key,
    required this.callToAction,
    this.textStyle,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minHeight: 0,
          maxHeight: 60,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: borderColor ?? Colors.black),
            borderRadius: BorderRadius.circular(100),
          ),
          child: Text(
            callToAction,
            style: textStyle,
          ),
        ),
      ),
    );
  }
}
