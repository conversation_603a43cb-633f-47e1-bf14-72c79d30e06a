import 'package:azzas_app_commons/azzas_app_commons.dart';

class MenuCollectionsCmsComponent extends CmsComponent {
  const MenuCollectionsCmsComponent({
    required super.id,
    required super.componentType,
    required this.collections,
  });

  factory MenuCollectionsCmsComponent.fromJson(Map<String, dynamic> json) {
    return MenuCollectionsCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      collections: (json['collections'] as List)
          .tryMap((j) => MenuCollectionsCmsComponentItem.fromJson(j))
          .toList(),
    );
  }

  final List<MenuCollectionsCmsComponentItem> collections;
}

class MenuCollectionsCmsComponentItem {
  const MenuCollectionsCmsComponentItem({
    required this.id,
    required this.callToAction,
    required this.navigateTo,
    this.media,
  });

  factory MenuCollectionsCmsComponentItem.fromJson(Map<String, dynamic> json) {
    return MenuCollectionsCmsComponentItem(
      id: json['id'],
      callToAction: json['call_to_action'],
      navigateTo: json['navigate_to'],
      media: json['media']['data'] != null
          ? CmsMedia.fromJson(json['media'])
          : null,
    );
  }

  final int id;
  final String callToAction;
  final String navigateTo;
  final CmsMedia? media;
}
