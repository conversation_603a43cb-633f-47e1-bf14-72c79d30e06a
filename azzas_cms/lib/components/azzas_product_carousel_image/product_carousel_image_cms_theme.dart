import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product.dart';
import 'package:flutter/material.dart';

class AzzasProductCarouselImageTheme {
  final TextStyle titleStyle;
  final TextStyle subtitleStyle;
  final TextStyle callToActionStyle;
  final SpotHeight spotHeight;
  final Color defaultTextColor;
  final Color defaultBackgroundColor;

  const AzzasProductCarouselImageTheme({
    required this.titleStyle,
    required this.subtitleStyle,
    required this.callToActionStyle,
    required this.spotHeight,
    required this.defaultTextColor,
    required this.defaultBackgroundColor,
  });

  AzzasProductCarouselImageTheme copyWith({
    TextStyle? titleStyle,
    TextStyle? subtitleStyle,
    TextStyle? callToActionStyle,
    TextStyle? priceTextStyle,
    SpotHeight? spotHeight,
    Color? defaultTextColor,
    Color? defaultBackgroundColor,
  }) {
    return AzzasProductCarouselImageTheme(
      titleStyle: titleStyle ?? this.titleStyle,
      subtitleStyle: subtitleStyle ?? this.subtitleStyle,
      callToActionStyle: callToActionStyle ?? this.callToActionStyle,
      spotHeight: spotHeight ?? this.spotHeight,
      defaultTextColor: defaultTextColor ?? this.defaultTextColor,
      defaultBackgroundColor:
          defaultBackgroundColor ?? this.defaultBackgroundColor,
    );
  }
}
