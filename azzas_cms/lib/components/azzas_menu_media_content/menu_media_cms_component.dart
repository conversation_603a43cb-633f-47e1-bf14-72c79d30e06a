import 'package:azzas_app_commons/azzas_app_commons.dart';

class MenuMediaCmsComponent extends CmsComponent {
  const MenuMediaCmsComponent({
    required super.id,
    required super.componentType,
    this.videoUrl,
    this.navigateTo,
    this.media,
  });
  factory MenuMediaCmsComponent.fromJson(Map<String, dynamic> json) {
    return MenuMediaCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      videoUrl: json['video_url'],
      media: json['image'] is Map && json['image']['data'] is Map
          ? CmsMedia.fromJson(json['image'])
          : null,
      navigateTo: json['navigate_to'],
    );
  }
  final String? videoUrl;
  final String? navigateTo;
  final CmsMedia? media;
}
