import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:flutter/material.dart';

class MenuMediaCmsWidget extends StatelessWidget {
  const MenuMediaCmsWidget({
    this.media,
    this.videoUrl,
    this.navigateTo,
    this.imageUrl,
  });

  final CmsMedia? media;
  final String? imageUrl;
  final String? videoUrl;
  final String? navigateTo;

  factory MenuMediaCmsWidget.fromComponent(MenuMediaCmsComponent component) {
    return MenuMediaCmsWidget(
      media: component.media,
      videoUrl: component.videoUrl,
      navigateTo: component.navigateTo,
    );
  }

  factory MenuMediaCmsWidget.fromAccordionItem(
      AccordionMenuEtcCmsComponentItem item) {
    return MenuMediaCmsWidget(
      imageUrl: item.imageUrl,
      videoUrl: item.videoUrl,
      navigateTo: item.navigateTo,
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: navigateTo.isNotNullOrEmpty
          ? () => NavigatorDynamic.call(navigateTo!)
          : () {},
      child: Container(
        padding: EdgeInsets.only(top: 16, bottom: 16),
        constraints: BoxConstraints(maxHeight: 298),
        child: videoUrl.isNotNullOrEmpty
            ? AzzasVideo(
                videoType: VideoType.network,
                mediaUrl: videoUrl!,
                looping: true,
                startVideo: true,
                showButtonPlay: false,
                volume: 0,
                fullScreen: true,
                mixWithOthers: true,
              )
            : AzzasCachedNetworkingImage(
                imageUrl: imageUrl ?? media?.data.attributes.url ?? '',
              ),
      ),
    );
  }
}
