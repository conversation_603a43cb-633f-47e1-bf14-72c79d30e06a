import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class VideoGalleryCmsWidget extends StatefulWidget {
  const VideoGalleryCmsWidget(
    this.component, {
    super.key,
  });

  final VideoGalleryCmsComponent component;

  @override
  State<VideoGalleryCmsWidget> createState() => _VideoGalleryState();
}

class _VideoGalleryState extends State<VideoGalleryCmsWidget> {
  List<VideoContent> videos = [];
  int activeItemIndex = 0;
  ScrollController videoScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    videos = widget.component.items
        .map(
          (video) => VideoContent(
            videoUrl: video.videoUrl,
            videoThumb: video.image?.data.attributes.url,
            videoTitle: video.title ?? '',
            videoDescription: video.description ?? '',
            navigateTo: video.navigateTo == null
                ? null
                : () => NavigatorDynamic.call(video.navigateTo!),
          ),
        )
        .toList();

    videoScrollController.addListener(() {
      int position = videoScrollController.position.pixels ~/ (50.0);
      _updatePaginationPosition(position);
    });
  }

  void _onTapVideoThumb(VideoContent video) {
    final videoUrl = video.videoUrl;
    if (videoUrl?.isNotEmpty == true) {
      Modular.to.pushNamed('/videocontent', arguments: video);
    }
  }

  _updatePaginationPosition(int page) {
    setState(() {
      activeItemIndex = page;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).videoGalleryTheme;
    return AzzasCmsContentSpacer(
      child: Column(
        children: [
          SizedBox(
            height: 24,
          ),
          _VideoGalleryHeader(
            theme: theme,
            title: widget.component.title ?? '',
            subtitle: widget.component.subtitle ?? '',
          ),
          Container(
            constraints: const BoxConstraints(maxHeight: 288.0),
            margin: EdgeInsets.only(
              top: 24,
            ),
            child: ListView.separated(
              controller: videoScrollController,
              padding: EdgeInsets.symmetric(horizontal: 24),
              scrollDirection: Axis.horizontal,
              itemCount: videos.length,
              itemBuilder: (context, index) {
                final video = videos[index];
                final videoTitle = videos[index].videoTitle;
                return AspectRatio(
                  aspectRatio: 16 / 29,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _VideoContentThumbnail(
                        theme: theme,
                        videoThumb: video.videoThumb,
                        onTapVideo: () => _onTapVideoThumb(video),
                      ),
                      Text(
                        videoTitle,
                        style: theme.videoTitleStyle,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) => SizedBox(
                width: 8,
              ),
            ),
          ),
          SizedBox(
            height: 24,
          ),
          _buildIndexIndicator(videos.length),
        ],
      ),
    );
  }

  Widget _buildIndexIndicator(int itemsCoun) {
    return Padding(
      padding: const EdgeInsets.only(left: 24.0),
      child: AzzasControllerCarrousel(
        itemsCount: itemsCoun,
        spaceBetweenDots: 8,
        activeItemIndex: activeItemIndex,
        activeItemColor: Colors.black,
        secondaryColor: Colors.grey,
      ),
    );
  }
}

class _VideoGalleryHeader extends StatelessWidget {
  const _VideoGalleryHeader({
    required this.title,
    required this.subtitle,
    required this.theme,
  });

  final String title;
  final String subtitle;
  final AzzasVideoGalleryTheme theme;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title.isNotEmpty) ...[
            Text(title, style: theme.titleHeaderStyle),
            SizedBox(height: 8),
          ],
          if (subtitle.isNotEmpty) ...[
            Text(
              subtitle,
              style: theme.subtitleHeaderStyle,
            ),
            SizedBox(height: 8),
          ],
        ],
      ),
    );
  }
}

class _VideoContentThumbnail extends StatelessWidget {
  const _VideoContentThumbnail({
    super.key,
    required this.videoThumb,
    required this.onTapVideo,
    required this.theme,
  });

  final String? videoThumb;
  final VoidCallback onTapVideo;
  final AzzasVideoGalleryTheme theme;

  @override
  Widget build(BuildContext context) {
    final decoration = BoxDecoration(borderRadius: BorderRadius.circular(0.0));

    return AspectRatio(
      aspectRatio: 8 / 13,
      child: Container(
        decoration: videoThumb != null
            ? decoration.copyWith(
                image: DecorationImage(
                  image: NetworkImage(videoThumb!),
                  fit: BoxFit.cover,
                ),
              )
            : decoration.copyWith(
                color: theme.thumbnailColor,
              ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (videoThumb == null)
                Text(
                  'imagem\nindisponível',
                  textAlign: TextAlign.center,
                  style: theme.thumbnailTitle,
                ),
              PausedVideoOverlay(
                theme: theme,
                onTapVideo: onTapVideo,
                coverOpacity: 0.15,
                playCircleSize: 40.0,
                playIconSize: 80.0,
                pausedButtonAppearance: VideoPausedAppearanceButton.dark,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum VideoPausedAppearanceButton { light, dark }

class PausedVideoOverlay extends StatelessWidget {
  const PausedVideoOverlay({
    super.key,
    required this.onTapVideo,
    required this.coverOpacity,
    required this.playCircleSize,
    required this.playIconSize,
    this.pausedButtonAppearance = VideoPausedAppearanceButton.light,
    required this.theme,
  });

  final VoidCallback onTapVideo;
  final double coverOpacity;
  final double playCircleSize;
  final double playIconSize;
  final VideoPausedAppearanceButton pausedButtonAppearance;
  final AzzasVideoGalleryTheme theme;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapVideo,
      child: Container(
        decoration: BoxDecoration(
          color: theme.pausedVideoOverlayDecoration
              .withValues(alpha: coverOpacity),
        ),
        child: Center(
          child: Padding(
            padding: EdgeInsets.only(left: playIconSize / 8),
            child: Icon(
              theme.pausedVideoOverlayIcon,
              color: theme.pausedVideoOverlayIconColor,
              size: playIconSize,
            ),
          ),
        ),
      ),
    );
  }
}

enum GalleryVideoAction {
  tapToWatch('Toque no video'),
  play('Play'),
  tapToInteract('');

  final String eventParam;

  const GalleryVideoAction(this.eventParam);
}
