import 'package:azzas_app_commons/azzas_app_commons.dart'
    show <PERSON><PERSON><PERSON><PERSON><PERSON>mer, AzzasVideo, NavigatorDynamic, VideoController;
import 'package:azzas_cms/components/azzzas_media/media_list_banner_cms_component.dart';
import 'package:azzas_cms/widgets/azzas_image.dart';
import 'package:azzas_cms/components/azzzas_media/azzas_media_list_foreground.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

typedef AzzasMediaListBackgroundCallBack = Future<void> Function(
    MediaListBannerCmsComponentItem item, int index);

class AzzasMediaListBackground extends StatefulWidget {
  const AzzasMediaListBackground({
    super.key,
    required this.mediaList,
    required this.videoController,
    this.onSearchTap,
    this.onItemSelected,
    this.onVisible,
  });

  final VideoController videoController;
  final List<MediaListBannerCmsComponentItem> mediaList;
  final VoidCallback? onSearchTap;
  final AzzasMediaListBackgroundCallBack? onItemSelected;
  final AzzasMediaListBackgroundCallBack? onVisible;

  @override
  State<AzzasMediaListBackground> createState() =>
      _AzzasMediaListBackgroundState();
}

class _AzzasMediaListBackgroundState extends State<AzzasMediaListBackground> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  final Set<int> _visibleItems = {};

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedMedia = widget.mediaList[_currentIndex];
    const height = 756.0;

    return SizedBox(
      height: height,
      child: InkWell(
        onTap: () async {
          if (widget.onItemSelected != null) {
            await widget.onItemSelected!(selectedMedia, _currentIndex);
          }
          _onCallToAction(selectedMedia.navigateTo!);
        },
        child: Stack(
          children: [
            PageView.builder(
              controller: _pageController,
              itemCount: widget.mediaList.length,
              itemBuilder: (context, index) {
                final item = widget.mediaList[index];
                Widget content;
                if (item.image?.data != null) {
                  content = ImageBackground(
                    image: NetworkImage(item.image!.data.attributes.url),
                  );
                } else {
                  content = VideoBackground(
                    videoUrl: item.url!,
                    controller: widget.videoController,
                  );
                }
                return VisibilityDetector(
                  key: Key('media_list_${item.id}'),
                  onVisibilityChanged: (info) {
                    if (info.visibleFraction >= 0.5 &&
                        !_visibleItems.contains(item.id)) {
                      _visibleItems.add(item.id);
                      widget.onVisible?.call(item, index);
                    }
                  },
                  child: content,
                );
              },
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                  _visibleItems.clear();
                });
              },
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: IgnorePointer(
                child: Container(
                  alignment: Alignment.bottomCenter,
                  height: height * 0.5,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: FractionalOffset.bottomCenter,
                      end: FractionalOffset.topCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.3),
                        Colors.black.withValues(alpha: 0.0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            AzzasListForeground(
              mediaList: widget.mediaList,
              onSearchTap: widget.onSearchTap,
              currentIndex: _currentIndex,
              onItemSelected: widget.onItemSelected,
            )
          ],
        ),
      ),
    );
  }

  void _onCallToAction(String navigateTo) {
    NavigatorDynamic.call(navigateTo.trim());
  }
}

class ImageBackground extends StatelessWidget {
  const ImageBackground({
    super.key,
    required this.image,
  });

  final ImageProvider image;

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: AzzasImage(
        loadingBuilder: (_, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          return const AzzasShimmer();
        },
        imageHeight: double.infinity,
        imageWidth: double.infinity,
        fit: BoxFit.cover,
        image: image,
      ),
    );
  }
}

class VideoBackground extends StatelessWidget {
  const VideoBackground({
    super.key,
    required this.videoUrl,
    this.controller,
  });

  final String videoUrl;
  final VideoController? controller;

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: AzzasVideo(
        controller: controller,
        loadingBuilder: (_) => const AzzasShimmer(),
        volume: 0, // TODO: Remover volume mudo
        startVideo: false,
        fullScreen: true,
        showButtonPlay: false,
        listenVisibilityChanges: true,
        mediaUrl: videoUrl,
      ),
    );
  }
}
