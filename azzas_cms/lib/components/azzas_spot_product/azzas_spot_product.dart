import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        GetSimilarProductsUseCase,
        ImageHelper,
        Modular,
        Product,
        TextScaleHelper;
import 'package:azzas_app_commons/components/azzas_favorite_button.dart';
import 'package:azzas_cms/components/azzas_cached_network_image/azzas_cached_image.dart';
import 'package:azzas_cms/components/azzas_general_tags/azzas_product_tag/azzas_product_cms_tag.dart';
import 'package:azzas_cms/components/azzas_general_tags/azzas_stamp_tag/stamp_tag_cms_widget.dart';
import 'package:azzas_cms/widgets/azzas_list_sku_pdc.dart';
import 'package:azzas_cms/widgets/azzas_tag.dart';
import 'package:flutter/material.dart';

const Map<String, int> vtexImageSize = {'width': 800, 'height': 1200};

enum SpotHeight { xsmall, small, medium, large, xlarge }

const Map<SpotHeight, double> spotHeightsMap = {
  SpotHeight.xsmall: 275.0,
  SpotHeight.small: 289.0,
  SpotHeight.medium: 331.0,
  SpotHeight.large: 418.0,
  SpotHeight.xlarge: 549.0,
};

const Map<SpotHeight, double> spotWidthsMap = {
  SpotHeight.xsmall: 178.0,
  SpotHeight.small: 187.0,
  SpotHeight.medium: 221.0,
  SpotHeight.large: 271.0,
  SpotHeight.xlarge: 375.0,
};

enum ButtonHeight { xLarge, large, medium, small, xSmall }

const Map buttonHeights = {
  ButtonHeight.xLarge: 72.0,
  ButtonHeight.large: 56.0,
  ButtonHeight.medium: 48.0,
  ButtonHeight.small: 40.0,
  ButtonHeight.xSmall: 32.0,
};

class AzzasCmsSpotProduct extends StatelessWidget {
  final int index;
  final SpotHeight spotHeight;
  final double? height;
  final double? width;
  final double? imageHeight;
  final double? imageWidth;
  final Product? product;
  final String productTitle;
  final bool isOnSale;
  final String heroTag;
  final String? productFullPrice;
  final String? productCurrentPrice;
  final double? priceTextSize;
  final double textLeftSpacing;
  final double? textTopSpacing;
  final String productImageUrl;
  final void Function() onTap;
  final void Function(Product)? onRemoveFromWishlist;
  final void Function()? onTapProductToBag;
  final String? textProductToBag;
  final Color? oldPriceTextColor;
  final Color? currentPriceTextColor;
  final TextStyle? oldPriceTextStyle;
  final TextStyle? currentPriceTextStyle;
  final double? textRightSpacing;
  final bool haveCallToAction;
  final String? favoriteAnimation;
  final bool hasFavoriteFeedbackText;
  final bool? showProductInformation;
  final bool isMiniature;
  final bool hasFavoriteButton;
  final bool wrapTagPrice;
  final bool isWishlistGridMode;
  final bool isTags;
  final String? installmentsText;
  final bool isPDPCarousel;
  final bool hasPlusButton;
  final Color? favoriteButtonBackgroundColor;
  final ButtonHeight? favoriteButtonHeight;
  final Function(int)? onChangeImage;
  final bool isSku;
  final void Function(Product)? onFavoriteCallback;
  final TextStyle? productTitleStyle;
  final double? productTitleBottomSpacing;
  final TextStyle? installmentsTextStyle;
  final EdgeInsets? paddingTextToBag;
  final TextStyle? textToBagStyle;
  final IconData? addToBagIcon;
  final bool useProductImageUrl;

  AzzasCmsSpotProduct({
    super.key,
    required this.index,
    this.spotHeight = SpotHeight.medium,
    this.height,
    this.width,
    this.imageHeight,
    this.imageWidth,
    this.priceTextSize = 12.0,
    this.textLeftSpacing = 0,
    required this.heroTag,
    required this.productImageUrl,
    this.isOnSale = true,
    required this.productTitle,
    required this.onTap,
    this.productFullPrice,
    this.productCurrentPrice,
    this.oldPriceTextColor,
    this.currentPriceTextColor,
    this.oldPriceTextStyle,
    this.currentPriceTextStyle,
    this.textRightSpacing,
    this.textTopSpacing,
    this.haveCallToAction = false,
    this.product,
    this.favoriteAnimation,
    this.onRemoveFromWishlist,
    this.hasFavoriteFeedbackText = false,
    this.hasFavoriteButton = true,
    this.showProductInformation = true,
    this.wrapTagPrice = false,
    this.isWishlistGridMode = false,
    this.isTags = false,
    this.installmentsText,
    this.isMiniature = false,
    this.onTapProductToBag,
    this.textProductToBag,
    this.isPDPCarousel = false,
    this.hasPlusButton = false,
    this.favoriteButtonBackgroundColor,
    this.favoriteButtonHeight,
    this.onChangeImage,
    this.isSku = false,
    this.onFavoriteCallback,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.installmentsTextStyle,
    this.paddingTextToBag,
    this.textToBagStyle,
    this.addToBagIcon,
    this.useProductImageUrl = false,
  }) {
    assert(
      spotHeightsMap.containsKey(spotHeight) &&
          spotWidthsMap.containsKey(spotHeight),
      'A spotHeight informada não é suportada.',
    );
  }

  @override
  Widget build(BuildContext context) {
    final height = spotHeightsMap[spotHeight]!;
    final width = spotWidthsMap[spotHeight]!;

    final productImageWidth = imageWidth ?? this.width ?? width;
    final productImageHeight = imageHeight ?? height;
    final oldPriceTextColor = this.oldPriceTextColor;
    final currentPriceTextColor =
        isOnSale ? this.currentPriceTextColor : this.currentPriceTextColor;
    final textRightSpacing = this.textRightSpacing;

    return SizedBox(
      width: this.width ?? width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: GestureDetector(
              onTap: onTap,
              child: Column(
                children: [
                  Stack(
                    children: <Widget>[
                      _ProductImage(
                        isPDPCarousel: isPDPCarousel,
                        heroTag: heroTag,
                        productImageHeight: productImageHeight,
                        productImageWidth: productImageWidth,
                        imageUrls: product?.images
                                .map((i) => i.imageUrl ?? "")
                                .toList() ??
                            [productImageUrl],
                        onChangeImage: onChangeImage,
                      ),
                      if (product != null) ...[
                        Positioned(
                          top: 8,
                          left: 0,
                          child: AzzasProductsCmsStampTag(
                            product: product!,
                          ),
                        ),
                        if (hasFavoriteButton)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: AzzasFavoriteButton(
                              product: product!,
                              index: index,
                              onFavoriteCallback: onFavoriteCallback,
                            ),
                          ),
                      ],
                    ],
                  ),
                  if (product != null && (showProductInformation ?? true))
                    _ProductInformation(
                      isOnSale: isOnSale,
                      isTags: isTags,
                      isWishlistGridMode: isWishlistGridMode,
                      productTitle: productTitle,
                      spotHeight: spotHeight,
                      textLeftSpacing: textLeftSpacing,
                      textRightSpacing: textRightSpacing ?? 4,
                      wrapTagPrice: wrapTagPrice,
                      currentPriceTextColor: currentPriceTextColor,
                      currentPriceTextStyle: currentPriceTextStyle,
                      installmentsText: installmentsText,
                      oldPriceTextColor: oldPriceTextColor,
                      oldPriceTextStyle: oldPriceTextStyle,
                      priceTextSize: priceTextSize,
                      product: product!,
                      productCurrentPrice: productCurrentPrice,
                      productFullPrice: productFullPrice,
                      textTopSpacing: textTopSpacing,
                      productTitleStyle: productTitleStyle,
                      productTitleBottomSpacing: productTitleBottomSpacing,
                      installmentsTextStyle: installmentsTextStyle,
                      isSku: isSku,
                    ),
                ],
              ),
            ),
          ),
          if (onTapProductToBag != null && textProductToBag != null)
            Flexible(
              child: Padding(
                padding: paddingTextToBag ?? const EdgeInsets.only(top: 18.0),
                child: GestureDetector(
                  onTap: onTapProductToBag,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Text(
                          textProductToBag!,
                          style: textToBagStyle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        addToBagIcon ?? Icons.arrow_right,
                        size: 15.0,
                      )
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ProductImage extends StatelessWidget {
  const _ProductImage({
    required this.isPDPCarousel,
    required this.heroTag,
    required this.productImageWidth,
    required this.productImageHeight,
    required this.imageUrls,
    this.onChangeImage,
  });

  final bool isPDPCarousel;
  final String heroTag;
  final double productImageWidth;
  final double productImageHeight;
  final List<String> imageUrls;
  final Function(int)? onChangeImage;

  @override
  Widget build(BuildContext context) {
    const isImageCarousel = true;
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);

    if (imageUrls.isEmpty) {
      return SizedBox(
        width: productImageWidth,
        height: productImageHeight,
      );
    }

    final imageUrl = resizeImageUrl(
      imageUrls.first,
      gridInheritedWidget?.gridDensity == 4,
      true,
    );

    if (isPDPCarousel) {
      return AzzasCachedNetworkingImage(
        imageUrl: imageUrl,
        width: productImageWidth,
        height: productImageHeight,
      );
    }
    if (!isImageCarousel || gridInheritedWidget?.gridDensity == null) {
      return Hero(
        tag: heroTag,
        child: AzzasCachedNetworkingImage(
          imageUrl: imageUrl,
          width: productImageWidth,
          height: productImageHeight,
        ),
      );
    }

    return ImageSlider(
      showAnimation: false,
      imageUrls: imageUrls,
      heroTag: heroTag,
      productImageHeight: productImageHeight,
      productImageWidth: productImageWidth,
      onChangeImage: onChangeImage,
    );
  }
}

class _ProductInformation extends StatelessWidget {
  const _ProductInformation({
    required this.textLeftSpacing,
    required this.textRightSpacing,
    required this.isTags,
    required this.product,
    required this.spotHeight,
    required this.isWishlistGridMode,
    this.textTopSpacing,
    required this.productTitle,
    required this.wrapTagPrice,
    required this.isOnSale,
    this.productFullPrice,
    this.oldPriceTextStyle,
    this.priceTextSize,
    this.oldPriceTextColor,
    this.productCurrentPrice,
    this.currentPriceTextStyle,
    this.currentPriceTextColor,
    this.installmentsText,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.installmentsTextStyle,
    required this.isSku,
  });

  final double textLeftSpacing;
  final double textRightSpacing;
  final bool isTags;
  final Product product;
  final SpotHeight spotHeight;
  final bool isWishlistGridMode;
  final double? textTopSpacing;
  final String productTitle;
  final bool wrapTagPrice;
  final bool isOnSale;
  final String? productFullPrice;
  final TextStyle? oldPriceTextStyle;
  final double? priceTextSize;
  final Color? oldPriceTextColor;
  final String? productCurrentPrice;
  final TextStyle? currentPriceTextStyle;
  final Color? currentPriceTextColor;
  final String? installmentsText;
  final TextStyle? installmentsTextStyle;
  final bool isSku;
  final TextStyle? productTitleStyle;
  final double? productTitleBottomSpacing;

  @override
  Widget build(BuildContext context) {
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);
    final zeroPrice = CurrencyHelper.format(amount: 0);
    final hasFullPrice =
        productFullPrice != null && productFullPrice != zeroPrice;
    final hasCurrentPrice =
        productCurrentPrice != null && productCurrentPrice != zeroPrice;

    return Padding(
      // padding: const EdgeInsets.only(left: 12, right: 20),
      padding: EdgeInsets.only(left: textLeftSpacing, right: textRightSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          if (isWishlistGridMode) ...[
            _ProductAvailableTag(product),
            _ProductLowStockTag(product),
          ],
          Padding(
            padding: isWishlistGridMode
                ? EdgeInsets.zero
                : EdgeInsets.only(
                    top: textTopSpacing ?? 4,
                    bottom: productTitleBottomSpacing ?? 0,
                  ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                productTitle,
                overflow: TextOverflow.ellipsis,
                textScaler: TextScaleHelper.clampTextScale(context,
                    maxScaleFactor: 1.50),
                softWrap: true,
                style: productTitleStyle ?? const TextStyle(fontSize: 12),
              ),
            ),
          ),
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.start,
            spacing: wrapTagPrice ? 0 : 4,
            direction: wrapTagPrice ? Axis.vertical : Axis.horizontal,
            children: [
              if (isOnSale && hasFullPrice)
                _ProductFullPrice(
                  productFullPrice: productFullPrice,
                  oldPriceTextColor: oldPriceTextColor,
                  oldPriceTextStyle: oldPriceTextStyle,
                  priceTextSize: priceTextSize,
                ),
              if (hasCurrentPrice)
                _ProductCurrentPrice(
                  productCurrentPrice: productCurrentPrice,
                  currentPriceTextColor: currentPriceTextColor,
                  currentPriceTextStyle: currentPriceTextStyle,
                  priceTextSize: priceTextSize,
                ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 4, bottom: 8),
            child: AzzasProductsCmsTags(
              product: product,
              isPLP: true,
            ),
          ),
          if (installmentsText != null)
            Text(
              installmentsText!,
              style: installmentsTextStyle,
            ),
          if (true && isSku)
            ListSku(
              product: product,
              gridType: gridInheritedWidget?.gridDensity ?? 0,
            ),
        ],
      ),
    );
  }
}

class _ProductFullPrice extends StatelessWidget {
  const _ProductFullPrice({
    this.productFullPrice,
    this.oldPriceTextStyle,
    this.priceTextSize,
    this.oldPriceTextColor,
  });

  final String? productFullPrice;
  final TextStyle? oldPriceTextStyle;
  final double? priceTextSize;
  final Color? oldPriceTextColor;

  @override
  Widget build(BuildContext context) {
    return Text(
      productFullPrice!,
      style: TextStyle(
        fontSize: 10,
        height: oldPriceTextStyle?.height ?? 1.33,
        color: const Color(0xFF666666),
        decoration: TextDecoration.lineThrough,
      ),
    );
  }
}

class _ProductCurrentPrice extends StatelessWidget {
  const _ProductCurrentPrice({
    this.productCurrentPrice,
    this.currentPriceTextStyle,
    this.priceTextSize,
    this.currentPriceTextColor,
  });

  final String? productCurrentPrice;
  final TextStyle? currentPriceTextStyle;
  final double? priceTextSize;
  final Color? currentPriceTextColor;

  @override
  Widget build(BuildContext context) {
    return Text(
      productCurrentPrice!,
      style: currentPriceTextStyle ??
          TextStyle(
            fontSize: 10,
            height: currentPriceTextStyle?.height ?? 1.33,
            color: currentPriceTextColor ?? const Color(0xFF000000),
          ),
    );
  }
}

class _ProductAvailableTag extends StatelessWidget {
  const _ProductAvailableTag(this.product);

  final Product product;

  @override
  Widget build(BuildContext context) {
    final isProductAvailable = product.isAvailable;
    const lowStockTagTextColor = Colors.black;
    const lowStockTagColor = Colors.white;

    if (!isProductAvailable) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          CmsAzzasTag.stock(
            margin: EdgeInsets.zero,
            text: 'sem estoque',
            textColor: lowStockTagTextColor,
            backgroundColor: lowStockTagColor,
          ),
          const SizedBox(height: 4),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}

class _ProductLowStockTag extends StatelessWidget {
  const _ProductLowStockTag(this.product);

  final Product product;

  @override
  Widget build(BuildContext context) {
    const lowStockTagTextColor = Colors.black;
    const lowStockTagColor = Colors.white;

    if (product.isLowStock) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          CmsAzzasTag.stock(
            margin: EdgeInsets.zero,
            text: "Low stock",
            textColor: lowStockTagTextColor,
            backgroundColor: lowStockTagColor,
          ),
        ],
      );
    }
    return const SizedBox(height: 4);
  }
}

class ImageSlider extends StatefulWidget {
  const ImageSlider({
    super.key,
    required this.imageUrls,
    required this.showAnimation,
    required this.heroTag,
    required this.productImageWidth,
    required this.productImageHeight,
    this.onChangeImage,
  });

  final String heroTag;
  final double productImageWidth;
  final double productImageHeight;
  final List<String> imageUrls;
  final bool showAnimation;
  final Function(int)? onChangeImage;

  @override
  State<ImageSlider> createState() => _ImageSliderState();
}

class _ImageSliderState extends State<ImageSlider>
    with TickerProviderStateMixin {
  PageController? _pageController;
  AnimationController? _animationController;

  @override
  void dispose() {
    _pageController?.dispose();
    _animationController?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    if (widget.showAnimation) {
      _animateScroll();
    }
  }

  @override
  void didUpdateWidget(covariant ImageSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showAnimation != oldWidget.showAnimation) {
      if (mounted && widget.showAnimation) {
        _animateScroll();
      }
    }
  }

  void _animateScroll() {
    if (!widget.showAnimation || !mounted || _pageController == null) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController = AnimationController(
        duration: const Duration(seconds: 1),
        vsync: this,
      );

      final double pageWidth = widget.productImageWidth;
      final double offset = pageWidth * 0.5;
      const Duration animationDuration = Duration(milliseconds: 500);

      _animationController!.forward().whenComplete(() {
        if (_pageController?.hasClients ?? false) {
          _pageController!.position
              .animateTo(
            _pageController!.offset + offset,
            duration: animationDuration,
            curve: Curves.easeInOut,
          )
              .whenComplete(() {
            if (_pageController?.hasClients ?? false) {
              _pageController!.position
                  .animateTo(
                _pageController!.offset - offset,
                duration: animationDuration,
                curve: Curves.easeInOut,
              )
                  .whenComplete(() {
                if (_pageController?.hasClients ?? false) {
                  _pageController!.jumpToPage(0);
                }
              });
            }
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);
    return SizedBox(
      width: widget.productImageWidth,
      height: widget.productImageHeight,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.horizontal,
        itemCount: widget.imageUrls.length,
        pageSnapping: true,
        onPageChanged: (index) => {
          if (widget.onChangeImage != null) {widget.onChangeImage!(index)}
        },
        itemBuilder: (context, index) {
          final imageUrl = resizeImageUrl(
            widget.imageUrls[index],
            gridInheritedWidget?.gridDensity == 4,
            true,
          );
          return Hero(
            tag: widget.heroTag,
            child: AzzasCachedNetworkingImage(
              imageUrl: imageUrl,
              width: widget.productImageWidth,
              height: widget.productImageHeight,
            ),
          );
        },
      ),
    );
  }
}

String resizeImageUrl(String image, bool isMiniature, bool resizeVtexImage) {
  if (isMiniature) {
    return ImageHelper.resizeVtexImage(
      imageUrl: image,
      vtexWidth: 0,
      vtexHeight: 600,
      position: 4,
    );
  }
  return resizeVtexImage
      ? ImageHelper.resizeVtexImage(
          imageUrl: image,
          vtexWidth: vtexImageSize['width']!,
          vtexHeight: vtexImageSize['height']!,
          position: 4,
        )
      : image;
}

class ListSku extends StatefulWidget {
  final Product product;
  final int gridType;

  const ListSku({
    super.key,
    required this.product,
    required this.gridType,
  });

  @override
  State<StatefulWidget> createState() => _ListSkus();
}

class _ListSkus extends State<ListSku> {
  List<Product> _similarProducts = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchSimilarProducts();
  }

  @override
  void didUpdateWidget(covariant ListSku oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.gridType != widget.gridType && mounted) {
      _fetchSimilarProducts();
    }
  }

  Future<void> _fetchSimilarProducts() async {
    final getSimularProducts = Modular.get<GetSimilarProductsUseCase>();

    if (widget.product.productId == null) return;

    try {
      List<Product> similarProducts = await getSimularProducts.call(
          productId: int.parse(widget.product.productId!));
      similarProducts.insert(0, widget.product);
      setState(() {
        _similarProducts = similarProducts;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Erro ao buscar produtos similares: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AzzasListSkuPdc(
      product: _similarProducts,
      gridType: widget.gridType,
    );
  }
}

class GridDensityInheritedWidget extends InheritedWidget {
  final int gridDensity;
  final bool? isOnTop;

  const GridDensityInheritedWidget({
    super.key,
    required this.gridDensity,
    this.isOnTop = false,
    required super.child,
  });

  static GridDensityInheritedWidget? of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<GridDensityInheritedWidget>();
  }

  @override
  bool updateShouldNotify(GridDensityInheritedWidget oldWidget) {
    return (oldWidget.gridDensity != gridDensity ||
        oldWidget.isOnTop != isOnTop);
  }
}
