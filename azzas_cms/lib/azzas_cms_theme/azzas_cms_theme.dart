import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_cms/components/components.dart';

class AzzasCmsTheme {
  final AzzasMediaListTheme media;
  final AzzasContentAreaTheme contentArea;
  final AzzasSignUpTheme signUp;
  final AzzasShowcaseBannerCarrouselTheme showcaseBannerCarrousel;
  final AzzasPersonalShowcaseTheme personalShowcase;
  final AzzasCategoriesMosaicTheme categories;
  final AzzasVideoBannerTheme videoBanner;
  final AzzasVideoGalleryTheme videoGalleryTheme;
  final AzzasCategoryBannerTheme? categoryBanner;
  final AzzasShowcaseListTheme showcaseListTheme;
  final AzzasCmsSpotProductTheme? spotProduct;
  final AzzasCouponBannerTheme couponBanner;
  final AzzasStoriesTheme stories;
  final AzzasCmsSpotProductClockTheme? spotProductClock;
  final AzzasTipBarTheme tipBar;
  final NotificationCenterTheme notificationCenter;
  final AzzasProductTagTheme? productTag;
  final AzzasGridCategoryListingTheme gridCategoryListingTheme;
  final AzzasBrandSloganTheme brandSloganTheme;
  final AzzasBannerContentTextTheme bannerContentTextTheme;
  final AzzasProductsGridFiltersTheme productsGridFilterTheme;
  final AzzasImageCompositionTheme imageCompositionTheme;
  final AzzasMediaContentTheme mediaContentTheme;
  final AzzasSearchTrendsTheme? searchTrendsTheme;
  final AzzasListLinksWithIconsTheme listLinksWithIconsTheme;
  final AzzasProductCarouselImageTheme productCarouselImageTheme;
  final AzzasFullLookCarouselTheme? fullLookCarouselTheme;
  final AzzasMenuCollectionsTheme? menuCollectionsTheme;

  AzzasCmsTheme({
    required this.media,
    required this.contentArea,
    required this.signUp,
    required this.showcaseBannerCarrousel,
    required this.personalShowcase,
    required this.categories,
    required this.videoBanner,
    required this.videoGalleryTheme,
    this.categoryBanner,
    required this.showcaseListTheme,
    this.spotProduct,
    required this.couponBanner,
    required this.stories,
    this.spotProductClock,
    required this.tipBar,
    required this.notificationCenter,
    this.productTag,
    required this.gridCategoryListingTheme,
    required this.brandSloganTheme,
    required this.bannerContentTextTheme,
    required this.productsGridFilterTheme,
    required this.imageCompositionTheme,
    required this.mediaContentTheme,
    this.searchTrendsTheme,
    required this.listLinksWithIconsTheme,
    required this.productCarouselImageTheme,
    this.fullLookCarouselTheme,
    this.menuCollectionsTheme,
  });
}
