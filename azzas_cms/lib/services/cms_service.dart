import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        Dio,
        LoginBackgroundModel,
        Options,
        ResponseType,
        TapumeModel,
        PaymentWarningModel;
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_component_parser.dart';
import 'package:azzas_cms/models/cms_document.dart';
import 'package:azzas_cms/models/cms_response.dart';
import 'package:flutter/foundation.dart';

class CmsService {
  const CmsService(this.brandName, this.baseUrl, this.accessToken);

  final String brandName;
  final String baseUrl;
  final String accessToken;

  Future<List<CmsComponent>?> loadComponentsForDocument(
    String documentType, {
    fullDepth = false,
    Map<String, dynamic>? filters,
  }) async {
    return loadDynamicComponentsForDocument<CmsComponent>(
      documentType,
      componentParser: CmsComponent.fromJson,
      fullDepth: fullDepth,
      filters: filters,
    );
  }

  Future<List<T>?> loadDynamicComponentsForDocument<T>(
    String documentType, {
    required ComponentParser<T> componentParser,
    fullDepth = false,
    Map<String, dynamic>? filters,
  }) async {
    return loadDocument<CmsDocument<T>>(
      documentType,
      documentParser: (json) => CmsDocument.fromJson(json, componentParser),
      fullDepth: fullDepth,
      filters: filters,
    ).then((document) => document?.attributes.components);
  }

  Future<T?> loadDocument<T>(
    String documentType, {
    required DocumentParser<T> documentParser,
    fullDepth = false,
    Map<String, dynamic>? filters,
  }) async {
    return loadDocuments<T>(
      documentType,
      documentParser: documentParser,
      fullDepth: fullDepth,
      filters: filters,
    ).then((d) => d.firstOrNull);
  }

  Future<List<T>> loadDocuments<T>(
    String documentType, {
    required DocumentParser<T> documentParser,
    fullDepth = false,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await Dio().get(
        '$baseUrl/$documentType',
        options: Options(
          responseType: ResponseType.json,
          headers: <String, dynamic>{
            'Content-Type': "application/json",
            'Accept': "application/json",
            'Authorization': 'Bearer $accessToken',
          },
        ),
        queryParameters: {
          if (filters != null) 'filters': filters,
          'populate': 'deep,${fullDepth ? 7 : 5}',
        },
      );
      final cmsResponse = CmsResponse<T>.fromJson(
        response.data,
        documentParser,
      );
      return cmsResponse.data;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      return [];
    }
  }

  Future<TapumeModel> getTapume(String document) async {
    try {
      final response = await Dio().get(
        '$baseUrl/$document',
        options: Options(
          responseType: ResponseType.json,
          headers: <String, dynamic>{
            'Content-Type': "application/json",
            'Accept': "application/json",
            'Authorization': 'Bearer $accessToken',
          },
        ),
        queryParameters: {
          'populate': 'deep,5',
        },
      );
      if (response.data['data'].length == 0) {
        return TapumeModel();
      }
      var tapume = response.data['data']?[0]?['attributes'];

      if (tapume != null) {
        return TapumeModel.fromJson(tapume);
      }

      return TapumeModel();
    } catch (e) {
      debugPrint(e.toString());
      if (e is Error) {
        debugPrintStack(stackTrace: e.stackTrace);
      }
      rethrow;
    }
  }

  Future<PaymentWarningModel?> getPaymentWarningComponent() async {
    try {
      final response = await Dio().get(
        '$baseUrl/payment-${brandName}',
        options: Options(
          responseType: ResponseType.json,
          headers: <String, dynamic>{
            'Content-Type': "application/json",
            'Accept': "application/json",
            'Authorization': 'Bearer $accessToken',
          },
        ),
        queryParameters: {
          'populate': 'deep,5',
        },
      );
      if (response.data['data'].length == 0) {
        return PaymentWarningModel();
      }
      var component = response.data['data']?[0]?['attributes'];
      if (component != null) {
        return PaymentWarningModel.fromJson(component['paymentWarning']);
      }
      return null;
    } catch (e) {
      debugPrint(e.toString());
      if (e is Error) {
        debugPrintStack(stackTrace: e.stackTrace);
      }
      rethrow;
    }
  }

  Future<LoginBackgroundModel> getLoginBackground() async {
    try {
      final response = await Dio().get(
        '$baseUrl/login-background-${brandName}',
        options: Options(
          responseType: ResponseType.json,
          headers: <String, dynamic>{
            'Content-Type': "application/json",
            'Accept': "application/json",
            'Authorization': 'Bearer $accessToken',
          },
        ),
        queryParameters: {
          'populate': 'deep,5',
        },
      );
      if (response.data['data'].length == 0) {
        return LoginBackgroundModel();
      }

      final data = response.data['data']?[0]?['attributes'];

      if (data != null) {
        return LoginBackgroundModel.fromJson(data);
      }

      return LoginBackgroundModel();
    } catch (e) {
      debugPrint(e.toString());
      if (e is Error) {
        debugPrintStack(stackTrace: e.stackTrace);
      }
      rethrow;
    }
  }
}
