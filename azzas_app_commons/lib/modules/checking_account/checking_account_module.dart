import 'package:azzas_app_commons/azzas_app_commons.dart';

class CheckingAccountModule extends Module {
  final HttpClient httpClient;
  final Function(RouteManager r)? overrideRoutes;
  final Function(RouteManager r)? incrementRoutes;

  CheckingAccountModule({
    required this.httpClient,
    this.overrideRoutes,
    this.incrementRoutes,
  });

  @override
  void binds(Injector i) {
    i.addSingleton<AuthRepository>(
        () => AuthRepository(httpClient: httpClient));
    i.addSingleton<SecureStorage>(() => SecureStorage());
    i.addSingleton<AuthStorageService>(
      () => AuthStorageService(
        i.get<SecureStorage>(),
      ),
    );
    i.addSingleton<CheckingAccountStorage>(() => CheckingAccountStorage());
    i.addSingleton<CheckingAccountRepository>(
      () => CheckingAccountRepository(httpClient: httpClient),
    );
    i.add<ExpirationsUseCase>(ExpirationsUseCase.new);
    i.add<FinancialOverviewsUseCase>(FinancialOverviewsUseCase.new);
    i.add<TransactionsUseCase>(TransactionsUseCase.new);

    i.addSingleton<CheckingAccountCubit>(() => CheckingAccountCubit());
    super.binds(i);
  }

  @override
  void routes(r) {
    if (overrideRoutes != null) {
      overrideRoutes!.call(r);
      return;
    }

    if (incrementRoutes != null) {
      incrementRoutes!.call(r);
    }
  }
}
