import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final GetUserInfoUseCase _getUserInfoUseCase;
  final GetUserClusterDataUseCase _getUserClusterDataUseCase;
  final RefreshAuthTokenUseCase _refreshAuthTokenUseCase;
  final OrderFormCubit _orderFormCubit;
  final OrderClientHandler _orderClientHandler;
  final AuthStorageService storage;
  final DeleteAccountUseCase _deleteAccountUseCase;
  final ActivateBiometricAuthUseCase _activateBiometricAuthUseCase;
  final LoginWithBiometricsUseCase _loginWithBiometricsUseCase;
  final LocalAuthentication _localAuthentication = LocalAuthentication();
  final AzzasAppsflyer _azzasAppsflyer;

  AuthCubit(
    this._getUserInfoUseCase,
    this._refreshAuthTokenUseCase,
    this._orderFormCubit,
    this._orderClientHandler,
    this._deleteAccountUseCase,
    this._activateBiometricAuthUseCase,
    this._loginWithBiometricsUseCase,
    this._azzasAppsflyer,
    this._getUserClusterDataUseCase, {
    required this.storage,
  }) : super(AuthState.initial()) {
    _init();
  }

  Future<void> _init() async {
    final storageAuthToken =
        await storage.readAuthToken(AuthStorageService.authTokenKey);
    final storageRefreshToken =
        await storage.readAuthToken(AuthStorageService.refreshTokenKey);
    final accountAuthToken = await storage.readAuthToken(
      AuthStorageService.accountAuthTokenKey,
    );

    final biometricsToken = await storage.readBiometricsToken();
    final userName = await storage.readUserName();
    final canAuthenticateWithBiometrics =
        await _localAuthentication.canCheckBiometrics;
    final isDeviceSupported = await _localAuthentication.isDeviceSupported();

    final canAuthenticate = canAuthenticateWithBiometrics && isDeviceSupported;

    if (canAuthenticate) {
      emit(state.copyWith(
        isBiometricAvailable: true,
      ));
    }

    if (biometricsToken != null && biometricsToken.isNotEmpty) {
      emit(state.copyWith(
        biometricsToken: biometricsToken,
      ));
    }

    if (userName != null) {
      emit(state.copyWith(
        userName: userName,
      ));
    }

    final userInfo = await storage.readUserInfo();
    if (userInfo?.personEmail != null) {
      getUserClusterData(userInfo!.personEmail!);
    }
    if (storageRefreshToken != null &&
        storageAuthToken != null &&
        userInfo != null) {
      emit(state.copyWith(
        isLoggedIn: true,
        authToken: storageAuthToken,
        refreshToken: storageRefreshToken,
        localUserInfo: userInfo,
        accountAuthToken: accountAuthToken,
      ));
    }
  }

  String? get authorizationSoma {
    final authToken = state.authToken;
    if (authToken == null) return null;

    return '${authToken.name}=${authToken.value}';
  }

  Future<void> loginSuccess({
    required CreateAuthTokenResponse tokenResponse,
    required String userEmail,
    required LoginType loginType,
  }) async {
    try {
      await storage.saveAuthTokensInfo(tokenResponse);

      emit(state.copyWith(
        isLoading: true,
        authToken: tokenResponse.authCookie,
        refreshToken: tokenResponse.refreshAuthCookie,
        accountAuthToken: tokenResponse.accountAuthCookie,
        loginType: loginType,
      ));

      await getUserInfo();

      emit(state.copyWith(isLoggedIn: true));

      if (state.localUserInfo != null) {
        await _orderClientHandler.addClientProfileToOrderForm(
          phone: state.localUserInfo?.personTelephone,
          document: state.localUserInfo?.personDocumentCpf,
          email: state.localUserInfo?.personEmail ?? userEmail,
          firstName: state.localUserInfo?.personName,
          lastName: state.localUserInfo?.personSurname,
        );

        final userUpdatedEmail =
            _orderFormCubit.orderForm.clientProfileData?.email;

        if (state.localUserInfo?.personEmail == null &&
            userUpdatedEmail != null) {
          final userEmailFromOrderForm =
              _orderFormCubit.orderForm.clientProfileData?.email;
          _saveUserInfo(
            state.localUserInfo!.copyWith(personEmail: userEmailFromOrderForm),
          );
        }
      }
      final eventDispatcher = Modular.get<EventDispatcher>();
      if (state.localUserInfo?.personEmail != null) {
        final email = state.localUserInfo?.personEmail ?? '';
        FirebaseCrashlytics.instance.setUserIdentifier(email);
        _azzasAppsflyer.setCustomerUserId(email: email);
        InsiderService.instance.login(
          email: email,
          phone: state.localUserInfo?.personTelephone,
        );
        eventDispatcher.logAuthAction(authActionType: AuthActionType.login);
      } else {
        eventDispatcher.logAuthAction(authActionType: AuthActionType.signup);
      }

      await Modular.get<WishlistCommonCubit>().getWishlistCatalogAuthDynamic();
      await Modular.get<WishlistCommonCubit>().getWishlist();
      await Modular.get<UnselectProductsManagerCubit>()
          .syncUnselectedProducts();

      emit(state.copyWith(isLoading: false));
    } catch (e) {
      debugPrint("Error in loginSuccess: $e");
    }
  }

  Future<void> getUserInfo() async {
    try {
      final userInfo = await _getUserInfoUseCase();
      await _saveUserInfo(userInfo);
    } catch (e) {
      debugPrint("Error in _getUserInfo: $e");
    }
  }

  Future<void> getUserClusterData(String email) async {
    try {
      if (email.isEmpty) return;
      final userClusterData = await _getUserClusterDataUseCase(email);
      await _saveUserClusterData(userClusterData);
    } catch (e) {
      debugPrint("Error in getUserClusterData: $e");
    }
  }

  Future<CreateAuthTokenResponse?> getRefreshAuthCookie() async {
    final authToken = state.authToken ??
        await storage.readAuthToken(AuthStorageService.authTokenKey);
    final refreshToken = state.refreshToken ??
        await storage.readAuthToken(AuthStorageService.refreshTokenKey);
    final accountAuthToken = state.accountAuthToken ??
        await storage.readAuthToken(AuthStorageService.accountAuthTokenKey);

    if (authToken == null || refreshToken == null || accountAuthToken == null) {
      return null;
    }

    try {
      final response = await _refreshAuthTokenUseCase(
        refreshToken: refreshToken,
        accountAuthToken: accountAuthToken,
        authToken: authToken,
      );

      if (response.accountAuthCookie == null &&
          response.authCookie == null &&
          response.refreshAuthCookie == null) {
        return null;
      }

      final authTokensInfo = CreateAuthTokenResponse(
        authCookie: response.authCookie ?? authToken,
        accountAuthCookie: response.accountAuthCookie ?? accountAuthToken,
        refreshAuthCookie: response.refreshAuthCookie ?? refreshToken,
      );

      return authTokensInfo;
    } catch (e) {
      debugPrint("Error in _getRefreshAuthCookie: $e");
      return null;
    }
  }

  Future<void> updateAuthTokens(CreateAuthTokenResponse tokensInfo) async {
    await storage.saveAuthTokensInfo(tokensInfo);
    emit(
      state.copyWith(
        authToken: tokensInfo.authCookie,
        refreshToken: tokensInfo.refreshAuthCookie,
        accountAuthToken: tokensInfo.accountAuthCookie,
      ),
    );
  }

  Future<void> _saveUserInfo(UserInfo userInfo) async {
    await storage.saveUserInfo(userInfo);
    emit(state.copyWith(localUserInfo: userInfo));
  }

  Future<void> _saveUserClusterData(clusterData) async {
    emit(state.copyWith(userClusterData: clusterData));
  }

  Future<void> logOut() async {
    try {
      if (state.hasBiometrics) {
        emit(state.copyWith(userName: state.localUserInfo?.personName ?? ""));
      }
      emit(state.copyWith(isLoading: true));
      await storage.clearUserInfo();
      await storage.clearAuthTokenInfo();
      FirebaseAuth.instance.signOut();
      Modular.get<PaymentCubit>().clearCreditCardInfo();
      await Modular.get<UnselectProductsManagerCubit>().onLogout();
      Modular.get<BagPageCubit>().clearBagPage();

      emit(AuthState.initial().copyWith(
        biometricsToken: state.biometricsToken,
        isBiometricAvailable: state.isBiometricAvailable,
        userName: state.localUserInfo?.personName ?? '',
      ));

      final isGoogleSignedIn = await GoogleSignIn().isSignedIn();
      if (isGoogleSignedIn) {
        await GoogleSignIn().signOut();
      }
      await Modular.get<WishlistCommonCubit>().onLogout();
      await _orderFormCubit.clearAndCreateOrderForm();
    } catch (e) {
      debugPrint("Error in logOut: $e");
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> deleteAccount() async {
    try {
      emit(state.copyWith(isLoadingDelete: true));
      await _deleteAccountUseCase.call();
      logOut();
      Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(deleteSuccess: true));
      });
    } catch (e) {
      if (e is DioException &&
          e.response?.statusCode == 404 &&
          e.response?.data?['message'] == 'Usuário não encontrado') {
        emit(state.copyWith(deleteSuccess: false, isLoadingDelete: false));
        return;
      }
    } finally {
      emit(state.copyWith(isLoadingDelete: false));
    }
  }

  Future<void> activateBiometrics() async {
    try {
      await _didAuthenticate();

      final response = await _activateBiometricAuthUseCase.call();

      await storage.saveBiometricsToken(response.biometricsToken);
      await storage.saveUserName(state.localUserInfo?.personName ?? '');
      emit(state.copyWith(biometricsToken: response.biometricsToken));
    } catch (e) {
      debugPrint("Error in activateBiometrics: $e");
      rethrow;
    }
  }

  Future<void> _didAuthenticate() async {
    try {
      if (!state.isBiometricAvailable) {
        throw Exception('Biometria não disponível');
      }

      final didAuthenticate = await _localAuthentication.authenticate(
        localizedReason: 'Ative a biometria para facilitar o login',
        options: const AuthenticationOptions(
          sensitiveTransaction: true,
          useErrorDialogs: true,
        ),
      );

      if (!didAuthenticate) throw Exception('Erro ao ativar biometria');
    } catch (e) {
      debugPrint('Error in _didAuthenticate: $e');
      rethrow;
    }
  }

  Future<void> deactivateBiometrics() async {
    try {
      await storage.removeBiometricsToken();
      await storage.clearUserName();
      emit(state.clearBiometricsToken());
    } catch (e) {
      debugPrint("Error in deactivateBiometrics: $e");
      rethrow;
    }
  }

  Future<CreateAuthTokenResponse?> loginWithBiometrics() async {
    try {
      if (state.biometricsToken.isNullOrEmpty) {
        throw Exception('Biometria não disponível');
      }

      emit(state.copyWith(isLoading: true));

      final didAuthenticate = await _localAuthentication.authenticate(
        localizedReason: 'Faça login com sua biometria',
        options: const AuthenticationOptions(
          useErrorDialogs: true,
        ),
      );

      if (didAuthenticate) {
        final response =
            await _loginWithBiometricsUseCase.call(state.biometricsToken!);
        final tokensInfo = CreateAuthTokenResponse(
          authCookie: response.authCookie,
          accountAuthCookie: response.accountAuthCookie,
          refreshAuthCookie: response.refreshAuthCookie,
        );
        return tokensInfo;
      }

      return null;
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      debugPrint('Error in loginWithBiometrics: $e');
      rethrow;
    }
  }
}
