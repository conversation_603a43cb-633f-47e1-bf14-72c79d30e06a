import 'package:adyen_checkout/adyen_checkout.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_core/repositories/checkout/models/apple_pay/apple_pay.dart';
import 'package:flutter/material.dart';
import 'package:azzas_core/repositories/checkout/models/apple_pay/apple_pay_model.dart'
    as adyen;

class PaymentCubit extends Cubit<PaymentState> {
  final _orderPaymentHandler = Modular.get<OrderPaymentHandler>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _queryCepUseCase = Modular.get<QueryCepUseCase>();
  final _paymentSelectUseCase = Modular.get<PaymentSelectUseCase>();
  final _cmsService = Modular.get<CmsService>();
  final _paymentTransactionApplePayUseCase =
      Modular.get<PaymentTransactionApplePayUseCase>();
  final _applePayPaymentsUseCase = Modular.get<ApplePayPaymentsUseCase>();
  final _handleApplePayUserCancelUseCase =
      Modular.get<HandleApplePayUserCancelUseCase>();
  final _appConfig = Modular.get<AppConfig>();

  PaymentCubit() : super(PaymentState.initial()) {
    _init();
  }

  Future<RecaptchaResult> getRecaptchaChecker(String action) async {
    final recaptchaResult = await RecaptchaChecker(
      _appConfig.recaptchaConfig.iosKey,
      _appConfig.recaptchaConfig.androidKey,
    ).check(
      action,
    );
    return recaptchaResult;
  }

  /// Inicializa o [PaymentCubit] com as informações que já vieram preenchidas do [OrderForm] do usuário.
  /// - [firstSelectedPayment] : o primeiro pagamento selecionado no [OrderForm].
  /// - [hasCreditPaymentData] : se o pagamento selecionado é um cartão de crédito.
  /// - [accountInfo] : as informações da conta do cartão de crédito.
  /// - [isPixSelected] : se o pagamento selecionado é um Pix.
  /// - [installment] : as informações da parcela selecionada.
  /// - [creditCardInfo] : as informações do cartão de crédito.
  void _init() {
    final firstSelectedPayment = _orderFormCubit.orderForm.firstSelectedPayment;
    final hasCreditPaymentData =
        _orderFormCubit.orderForm.isCreditCardSelected &&
            firstSelectedPayment != null &&
            firstSelectedPayment.accountId != null &&
            firstSelectedPayment.bin != null;
    firstSelectedPayment?.installments != null &&
        firstSelectedPayment!.installments > 0;
    final accountInfo = _orderFormCubit.orderForm.getAvailableAccounts
        .firstWhereOrNull(
            (element) => element.accountId == firstSelectedPayment?.accountId);

    final isPixSelected = _orderFormCubit.orderForm.isPixSelected;

    if (hasCreditPaymentData) {
      final installment = _orderFormCubit.orderForm
          .getInstallmentOptionsByPaymentSystemId(
              firstSelectedPayment.paymentSystem)
          .firstWhereOrNull(
              (element) => element.count == firstSelectedPayment.installments);

      final creditCardInfo = CreditCardInfoData(
        isNew: false,
        cardAccountId: accountInfo?.accountId,
        bin: accountInfo?.bin,
        selectedPaymentSystemId: accountInfo?.paymentSystem,
        cardNumber: accountInfo?.cardNumber,
        expirationDate: accountInfo?.expirationDate,
        selectedInstallment: installment,
      );

      copyCreditCardInfo(creditCardInfo);
    }

    if (isPixSelected) {
      emit(state.copyWith(
        selectedPaymentType: SelectedPaymentType.pix,
      ));
    }
    setAddressFromOrderForm();
  }

  CreditCardInfoData? getCardSelected() {
    final orderForm = _orderFormCubit.state.orderForm;
    if (orderForm?.paymentType != PaymentType.creditCard) {
      return null;
    }
    final payments = orderForm?.paymentData?.payments;
    final availableAccounts = orderForm?.getAvailableAccounts;
    final selectedPayment = payments?.firstWhereOrNull(
      (payment) =>
          availableAccounts
              ?.any((card) => card.accountId == payment.accountId) ??
          false,
    );
    final selectedCardFromOrderForm = availableAccounts?.firstWhereOrNull(
      (card) => card.accountId == selectedPayment?.accountId,
    );
    if (selectedCardFromOrderForm != null) {
      final installment = InstallmentOrderForm(
        count: selectedPayment?.installments,
        value: selectedPayment?.value,
      );
      final card = selectedCardFromOrderForm
          .toCreditCardInfoData()
          .copyWith(selectedInstallment: installment);

      return card.copyWith(cvv: state.creditCardInfo.cvv);
    }
    return null;
  }

  void clearCreditCardInfo() {
    emit(state.copyWith(
      creditCardInfo: CreditCardInfoData.initial(),
    ));
  }

  void clearSelectedPayment() {
    state.copyWith(clearSelectedPaymentType: true, selectedPaymentType: null);
  }

  Future<void> getPaymentWarningComponent() async {
    try {
      final response = await _cmsService.getPaymentWarningComponent();
      emit(state.copyWith(
        paymentWarning: response,
      ));
    } catch (e) {
      debugPrint('Error in getPaymentWarningComponent $e');
      emit(state.copyWith(
        paymentWarning: PaymentWarningModel(),
      ));
    }
  }

  /// Seleciona o Pix como pagamento no OrderForm.
  Future<void> selectPixInstallmentPayment() async {
    try {
      await _orderPaymentHandler.selectPixInstallmentPayment();

      if (_orderFormCubit.state.orderForm?.isPixInstallmentsSelected == true) {
        return emit(state.copyWith(
          selectedPaymentType: SelectedPaymentType.pixInstallment,
        ));
      }

      throw Exception(
          "O Pix parcelado não foi selecionado como método de pagamento no OrderForm.");
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    }
  }

  /// Seleciona o Pix como pagamento no OrderForm.
  Future<void> selectPixPayment() async {
    try {
      await _orderPaymentHandler.selectPixPayment();

      if (_orderFormCubit.state.orderForm?.isPixSelected == true) {
        return emit(state.copyWith(
          selectedPaymentType: SelectedPaymentType.pix,
        ));
      }

      throw Exception(
          "O Pix não foi selecionado como método de pagamento no OrderForm.");
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    }
  }

  /// Seleciona um cartão de crédito recorrente (já salvo anteriormente) como pagamento no OrderForm.
  /// - [creditCardInfo] : as informações do cartão de crédito a serem utilizadas.
  /// - [installments] : o número de parcelas a serem selecionadas.
  Future<void> selectCreditCardPayment({
    required CreditCardInfoData creditCardInfo,
    required InstallmentOrderForm installment,
  }) async {
    try {
      emit(state.copyWith(
        isLoading: false,
      ));
      if (creditCardInfo.selectedPaymentSystemId == null) {
        throw Exception("O sistema de pagamento não foi selecionado.");
      }
      if (creditCardInfo.cardAccountId == null &&
          creditCardInfo.isNew == false) {
        throw Exception("O accountId não foi selecionado.");
      }

      if (creditCardInfo.bin == null && creditCardInfo.isNew == false) {
        throw Exception("O bin não foi selecionado.");
      }

      final paymentSelect = PaymentSelect(payments: [
        PaymentSelectOption(
          accountId: creditCardInfo.isNew ? null : creditCardInfo.cardAccountId,
          bin: creditCardInfo.isNew ? null : creditCardInfo.bin,
          paymentSystem: creditCardInfo.selectedPaymentSystemId,
          installments: installment.count,
          installmentsInterestRate: 0,
          installmentsValue: installment.value,
          value: _orderFormCubit.orderForm.getValue(),
          referenceValue: _orderFormCubit.orderForm.getValue(),
        ),
      ], giftCards: _orderFormCubit.orderForm.getGiftCardsInUse());

      await _orderPaymentHandler.selectCreditCardPayment(
        paymentSelect: paymentSelect,
      );

      if (_orderFormCubit.orderForm.isCreditCardSelected != true) {
        throw Exception(
            "O cartão de crédito não foi selecionado como método de pagamento no OrderForm.");
      }
      creditCardInfo = creditCardInfo.copyWith(
        selectedInstallment: installment,
      );
      setCreditCardInfo(creditCardInfo);

      emit(state.copyWith(
        selectedPaymentType: SelectedPaymentType.creditCard,
      ));
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    } finally {
      emit(state.copyWith(
        isLoading: false,
      ));
    }
  }

  /// Obtém um [Address] a partir de um CEP
  ///
  /// - [cep] : o CEP a ser consultado
  Future<Address> getAddressFromCep(String cep) async {
    try {
      emit(state.copyWith(isLoadingAddress: true));
      final response = await _queryCepUseCase.call(cep: cep);

      final address = Address.fromQueryCep(response);
      return address;
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    } finally {
      emit(state.copyWith(isLoadingAddress: false));
    }
  }

  /// Adiciona um [Address] de faturamento ao cartão de crédito no [state.creditCardInfo]
  void addBillingAddressToCard(Address address) {
    final billingAddresses =
        state.creditCardInfo.availableBillingAddresses ?? [];
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(
        availableBillingAddresses: [...billingAddresses, address],
      ),
    ));
  }

  /// Seleciona um cartão de crédito recorrente no [state]
  void setRecurrentCreditCardPayment() {
    emit(state.copyWith(
      selectedPaymentType: SelectedPaymentType.creditCard,
      creditCardInfo: state.creditCardInfo.copyWith(isNew: false),
    ));
  }

  /// Seleciona um novo cartão de crédito no [state]
  void selectNewCreditCardPayment() {
    emit(
      state.copyWith(
        selectedPaymentType: SelectedPaymentType.creditCard,
        creditCardInfo: state.creditCardInfo.copyWith(isNew: true),
      ),
    );
  }

  /// Seleciona um endereço de pagamento no [state.creditCardInfo]
  void setSelectedBillingAddress(Address address) {
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(
        currentSelectedBillingAddress: address,
      ),
    ));
  }

  /// Seleciona um [paymentSystemId] no [state.creditCardInfo]
  void setPaymentSystemId(String paymentSystemId) {
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(
        selectedPaymentSystemId: paymentSystemId,
      ),
    ));
  }

  /// Seleciona um [InstallmentOrderForm] no [state.creditCardInfo]
  void setSelectedInstallment(InstallmentOrderForm installment) {
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(
        selectedInstallment: installment,
      ),
    ));
  }

  /// Configura novas informações de um cartão de crédito.
  /// Copiando as informações novas para o [state.creditCardInfo].
  void copyCreditCardInfo(CreditCardInfoData creditCardInfo) {
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(
        isNew: creditCardInfo.isNew,
        cardNumber: creditCardInfo.cardNumber,
        cardHolderName: creditCardInfo.cardHolderName,
        expirationDate: creditCardInfo.expirationDate,
        cvv: creditCardInfo.cvv,
        selectedInstallment: creditCardInfo.selectedInstallment,
        selectedPaymentSystemId: creditCardInfo.selectedPaymentSystemId,
        availableBillingAddresses: creditCardInfo.availableBillingAddresses,
        bin: creditCardInfo.bin,
        cardAccountId: creditCardInfo.cardAccountId,
        currentSelectedBillingAddress:
            creditCardInfo.currentSelectedBillingAddress,
      ),
    ));
  }

  /// Sobreescreve todas as informações do cartão de crédito no [state.creditCardInfo].
  void setCreditCardInfo(CreditCardInfoData creditCardInfo) {
    emit(state.copyWith(
      creditCardInfo: creditCardInfo,
    ));
  }

  /// Sobreescreve todas as informações do cartão de crédito no [state.creditCardInfo].
  void setCreditCardInfoCvv(String cvv) {
    emit(state.copyWith(
      creditCardInfo: state.creditCardInfo.copyWith(cvv: cvv),
    ));
  }

  /// Atualiza o [Address] de faturamento com as informações do OrderForm.
  void setAddressFromOrderForm() {
    final deliveryAddress =
        _orderFormCubit.orderForm.shippingData?.firstSelectedAddress;

    final billingAddresses =
        state.creditCardInfo.availableBillingAddresses ?? [];

    if (deliveryAddress != null) {
      bool shouldAdd = deliveryAddress.addressId == null ||
          !billingAddresses
              .any((address) => address.addressId == deliveryAddress.addressId);

      setSelectedBillingAddress(deliveryAddress);

      List<Address> updatedBillingAddresses =
          shouldAdd ? [...billingAddresses, deliveryAddress] : billingAddresses;

      copyCreditCardInfo(
        CreditCardInfoData(availableBillingAddresses: updatedBillingAddresses),
      );
    }
  }

  /// Retorna as opções de parcelamento no [OrderForm] para determinado cartão de crédito.
  List<InstallmentOrderForm> getPaymentInstallmentsForCreditCard(
      String paymentSystemId) {
    return _orderFormCubit.orderForm
        .getInstallmentOptionsByPaymentSystemId(paymentSystemId);
  }

  /// "Seta" no estado as informações do cartão de crédito que foram invalidadas.
  void setCreditCardError(CreditCardInfoData invalidCreditCardInfo) {
    emit(state.copyWith(
      creditCardErrorInfo: invalidCreditCardInfo,
    ));
  }

  /// "Limpa" no estado as informações do cartão de crédito que foram invalidadas anteriormente.
  void clearCreditCardError() {
    emit(state.copyWith(
      creditCardErrorInfo: null,
    ));
  }

  /// "Limpa" o CVV do cartão de crédito no [state.creditCardInfo].
  void clearCvv() {
    final creditCardInfo = state.creditCardInfo.clearCvv();

    emit(state.copyWith(
      creditCardInfo: creditCardInfo,
    ));
  }

  /// Retorna se o pagamento por Pix parcelado está selecionado, tanto no [OrderForm] quanto no [state].
  bool get isPixInstallmentsSelected =>
      state.selectedPaymentType == SelectedPaymentType.pixInstallment &&
      _orderFormCubit.orderForm.isPixInstallmentsSelected == true;

  /// Retorna se o pagamento por Pix está selecionado, tanto no [OrderForm] quanto no [state].
  bool get isPixSelected =>
      state.selectedPaymentType == SelectedPaymentType.pix &&
      _orderFormCubit.orderForm.isPixSelected == true;

  bool get isApplePaySelected =>
      state.selectedPaymentType == SelectedPaymentType.applePay &&
      _orderFormCubit.orderForm.isApplePaySelected == true;

  /// Retorna se o [state] do OrderForm está carregando.
  bool get isLoading => _orderFormCubit.state.isLoading;

  /// Retorna se o pagamento por Pix está disponível no [OrderForm].
  bool get acceptsPixPayment => _orderFormCubit.orderForm.acceptsPixPayment;

  /// Retorna se o pagamento por cartão de crédito está disponível no [OrderForm].
  bool get acceptsCreditCardPayment =>
      _orderFormCubit.orderForm.acceptsCreditCardPayment;

  /// Retorna se o pagamento por cartão de crédito está selecionado, tanto no [OrderForm] quanto no [state].
  bool get isRecurrentCreditCardSelected =>
      state.selectedPaymentType == SelectedPaymentType.creditCard &&
      state.isNewCreditCardSelected == false &&
      _orderFormCubit.orderForm.isCreditCardSelected == true;

  /// Retorna se as parcelas estão selecionadas, tanto no [OrderForm] quanto no [state].
  bool get hasSelectedInstallments =>
      state.creditCardInfo.selectedInstallment != null &&
      _orderFormCubit.orderForm.hasInstallmentOptionSelected;

  bool get isGiftCardSelected =>
      _orderFormCubit.orderForm.isGiftCardSelected == true;

  PaymentSelectOption? _getPaymentSelected() {
    if (_orderFormCubit.orderForm.value != null &&
        _orderFormCubit.orderForm.firstSelectedPayment != null) {
      int installmentTotalValue = 0;
      if ((_orderFormCubit.orderForm.merchantSellerPayments.length) > 0) {
        installmentTotalValue = _orderFormCubit.orderForm.merchantSellerPayments
            .map((e) => e.installmentValue)
            .reduce((p, e) => p + e);
      }
      return PaymentSelectOption(
          paymentSystem:
              _orderFormCubit.orderForm.firstSelectedPayment?.paymentSystem,
          bin: _orderFormCubit.orderForm.firstSelectedPayment?.bin,
          accountId: _orderFormCubit.orderForm.firstSelectedPayment?.accountId,
          tokenId: _orderFormCubit.orderForm.firstSelectedPayment?.tokenId,
          installments:
              _orderFormCubit.orderForm.firstSelectedPayment?.installments,
          installmentsValue: installmentTotalValue,
          referenceValue:
              _orderFormCubit.orderForm.firstSelectedPayment?.referenceValue,
          value: _orderFormCubit.orderForm.firstSelectedPayment?.value);
    }

    return null;
  }

  Future<void> addGiftCardCollection(
      {required GiftCardCollection collection,
      required bool isSpecialCard}) async {
    List<GiftCard> giftCards = collection.calculateUsage();
    var payment = _getPaymentSelected();

    if (_orderFormCubit.orderForm.paymentData != null &&
        _orderFormCubit.orderForm.paymentData!.giftCards != null) {
      for (var item in _orderFormCubit.orderForm.paymentData!.giftCards!) {
        if (!collection.contains(item.giftCardId)) {
          giftCards.add(item);
        }
      }
    }

    var response = await _paymentSelect(
        paymentMethod: PaymentSelect(
      payments: payment != null ? [payment] : null,
      giftCards: giftCards,
    ));

    if (response!.messages!.isNotEmpty) {
      if (response.messages?.first.code == 'noFundsGiftCard' ||
          response.messages?.first.code == 'giftCardCommunicationError' ||
          response.messages?.first.code == 'invalidGiftCard' &&
              response.messages?.first.status == 'error') {
        return;
      }
    }

    if (isSpecialCard) {
      await _paymentSelect(
          paymentMethod: PaymentSelect(
        payments: response.paymentData?.payments
            ?.map((p) => PaymentSelectOption().fromPaymentOrderForm(p))
            .toList(),
        giftCards: response.paymentData?.giftCards,
      ));
    }
  }

  Future<void> addGiftCard({
    required GiftCard giftCard,
    required bool isSpecialCard,
  }) async {
    emit(state.copyWith(isLoadingGiftCard: true));
    List<GiftCard> giftCards = [];
    giftCards.add(giftCard);
    var payment = _getPaymentSelected();

    if (_orderFormCubit.orderForm.paymentData != null &&
        _orderFormCubit.orderForm.paymentData!.giftCards != null) {
      for (var item in _orderFormCubit.orderForm.paymentData!.giftCards!) {
        if (item.redemptionCode != giftCard.redemptionCode) {
          item.value = giftCard.value;
          item.inUse = true;

          giftCards.add(item);
        }
      }
    }

    var response = await _paymentSelect(
        paymentMethod: PaymentSelect(
      payments: payment != null ? [payment] : null,
      giftCards: giftCards,
    ));

    if (response!.messages!.isNotEmpty) {
      if (response.messages?.first.code == 'noFundsGiftCard' ||
          response.messages?.first.code == 'giftCardCommunicationError' ||
          response.messages?.first.code == 'invalidGiftCard' &&
              response.messages?.first.status == 'error') {
        return;
      }
    }

    if (isSpecialCard) {
      await _paymentSelect(
          paymentMethod: PaymentSelect(
        payments: response.paymentData?.payments
            ?.map((p) => PaymentSelectOption().fromPaymentOrderForm(p))
            .toList(),
        giftCards: response.paymentData?.giftCards,
      ));
    }
  }

  Future<void> deleteGiftCard({required GiftCard giftCard}) async {
    List<GiftCard> giftCards = [];
    var payment = _getPaymentSelected();
    emit(state.copyWith(isLoadingGiftCardDelete: true));
    if (_orderFormCubit.orderForm.paymentData != null &&
        _orderFormCubit.orderForm.paymentData!.giftCards != null) {
      for (var item in _orderFormCubit.orderForm.paymentData!.giftCards!) {
        if (item.redemptionCode != null &&
            item.redemptionCode != giftCard.redemptionCode) {
          if (item.redemptionCode == giftCard.redemptionCode) {
            item.value = null;
            item.inUse = false;
          }

          giftCards.add(item);
        }
      }
    }
    await _paymentSelect(
        paymentMethod: PaymentSelect(
      payments: payment != null ? [payment] : null,
      giftCards: giftCards,
    ));
  }

  Future<void> deleteGiftCardCollection(
      {required GiftCardCollection collection}) async {
    List<GiftCard> giftCards =
        _orderFormCubit.orderForm.paymentData?.giftCards ?? [];
    var payment = _getPaymentSelected();

    await _paymentSelect(
        paymentMethod: PaymentSelect(
      payments: payment != null ? [payment] : null,
      giftCards: giftCards
          .whereNot((g) => collection.contains(g.giftCardId!))
          .toList(),
    ));
  }

  Future<OrderForm?> _paymentSelect(
      {required PaymentSelect paymentMethod}) async {
    try {
      if (_orderFormCubit.orderForm.orderFormId != null) {
        var response = await _paymentSelectUseCase(
          orderFormId: _orderFormCubit.orderForm.orderFormId!,
          paymentSelect: paymentMethod,
        );

        _orderFormCubit.updateOrderForm(orderForm: response);

        if (_orderFormCubit.orderForm.getGiftCardValues() >=
            _orderFormCubit.orderForm.value!.toDouble()) {
          emit(state.copyWith(
              selectedPaymentType: SelectedPaymentType.giftCard));
        } else if (_orderFormCubit.orderForm.getGiftCardValues() == 0 &&
            state.selectedPaymentType == SelectedPaymentType.giftCard) {
          emit(state.clearSelectedPayment());
        }
        emit(state.copyWith(
            isLoadingGiftCardDelete: false, isLoadingGiftCard: false));
        return response;
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  List<TransactionPaymentGiftCard> getGiftcardsTransaction(
      TransactionPaymentTransaction? transaction) {
    List<TransactionPaymentGiftCard> giftCards = [];

    final orderForm = this._orderFormCubit.orderForm.value;
    if (orderForm != null &&
        _orderFormCubit.orderForm.getGiftCardsInUse().isNotEmpty) {
      for (var item in _orderFormCubit.orderForm.getGiftCardsInUse()) {
        giftCards.add(TransactionPaymentGiftCard(
            paymentSystem: PaymentSystemIds.giftCard,
            value: item.value ?? 0,
            referenceValue: item.value ?? 0,
            installmentsValue: item.value ?? 0,
            installments: 1,
            fields: item,
            transaction: transaction));
      }
    }

    return giftCards;
  }

  /// Seleciona o Apple pay como pagamento no OrderForm.
  Future<void> selectApplePay() async {
    try {
      await _orderPaymentHandler.selectApplePayPayment();

      if (_orderFormCubit.state.orderForm?.isApplePaySelected == true) {
        return emit(state.copyWith(
          selectedPaymentType: SelectedPaymentType.applePay,
        ));
      }

      throw Exception(
          "O Apple pay não foi selecionado como método de pagamento no OrderForm.");
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    }
  }

  Future<adyen.ApplePayModel?> applePayTransaction(BuildContext context) async {
    emit(state.copyWith(isLoading: true));
    try {
      if (_orderFormCubit.orderForm.orderFormId != null) {
        final payment = _orderFormCubit.orderForm.firstSelectedPayment;
        List<TransactionPayment> params = [];
        final giftCards = getGiftcardsTransaction(null);
        final applePay = TransactionPaymentApplePay(
          paymentSystem: payment?.paymentSystem ?? '',
          value: payment?.value ?? 0,
          referenceValue: payment?.referenceValue ?? 0,
          installments: 1,
          merchantSellerPayments: [
            MerchantSellerPayment(
              id: payment?.merchantSellerPayments.first.id ?? '',
              installments: 1,
              referenceValue: payment?.referenceValue ?? 0,
              value: payment?.value ?? 0,
              interestRate: 0,
              installmentValue:
                  payment?.merchantSellerPayments.first.installmentValue ?? 0,
            )
          ],
        );
        params.add(TransactionPayment(
          applePay: applePay,
          giftCard: giftCards,
        ));

        final recaptchaResult = await getRecaptchaChecker(
          'apple_pay_transaction',
        );

        var response = await _paymentTransactionApplePayUseCase.call(
          orderFormId: _orderFormCubit.orderForm.orderFormId!,
          transactionPaymentaymentSelect: params,
          recaptchaKey: recaptchaResult.key,
          recaptchaToken: recaptchaResult.token,
        );

        emit(state.copyWith(applepayId: response?.paymentId));

        return response;
      } else {
        return null;
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        if (e.response?.data?['error'] != null) {
          final resp = PaymentErrorResponse.fromJson(e.response?.data);
          if (resp.error.code == 'CHK0082') {
            throw RecaptchaValidationException(
              'Erro ao validar recaptcha na Vtex. Code ${resp.error.code}. Message ${resp.error.message}',
              cause: e,
            );
          } else if (resp.error.code == 'CHK0328') {
            throw TooManyOrderRequestsException(
              cause: e,
            );
          }
          throw (resp.toString());
        } else if (e.response?.data?['messages'] is List) {
          final messages = e.response?.data?['messages'] as List;
          throw (messages.map((message) {
            return message['text'];
          }).join('\n'));
        }
      }
    } catch (e) {
      throw (e.toString());
    } finally {
      emit(state.copyWith(isLoading: false));
    }
    return null;
  }

  Future<PaymentEvent> onSubmit(
    Map<String, dynamic> data, [
    Map<String, dynamic>? extra,
  ]) async {
    final String? applePayToken = data['paymentMethod']?['applePayToken'];

    final response = await _applePayPaymentsUseCase.call(
      paymentId: state.applepayId ?? '',
      applePayToken: applePayToken ?? '',
    );
    final PaymentEventHandler paymentEventHandler = PaymentEventHandler();
    return paymentEventHandler.handleResponse(response);
  }

  Future<PaymentEvent> onAdditionalDetailsMock(
          Map<String, dynamic> additionalDetailsJson) =>
      Future.error(
          "Additional details call is not required for the Apple Pay component.");

  Future<void> handleApplePayUserCancel(
    String paymentId,
  ) async {
    try {
      await _handleApplePayUserCancelUseCase.call(paymentId: paymentId);
    } catch (e) {}
  }
}
