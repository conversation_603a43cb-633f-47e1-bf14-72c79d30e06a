import 'package:azzas_app_commons/azzas_app_commons.dart';

class TagCubit extends Cubit<TagState> {
  TagCubit() : super(TagState());

  final _cmsService = Modular.get<CmsService>();

  Future<void> loadCmsTagComponents() async {
    final appConfig = Modular.get<AppConfig>();
    final brandName = _cmsService.brandName;

    final mainResponse =
        await _cmsService.loadComponentsForDocument('tag-$brandName');
    final productsTagsList = _extractProductTags(mainResponse);

    List<StampTagCmsComponent> stampTagsList = [];
    List<StampTagProductCmsComponent> stampTagProductList = [];
    List<AzzasProductsTagsModel> etcProductsTagsList = [];
    List<ProductStampCmsComponent> productStampList = [];

    if (appConfig.brand == Brand.farm) {
      final etcResponse =
          await _cmsService.loadComponentsForDocument('tag-farm-etc');
      if (etcResponse != null) {
        stampTagsList = _extractComponents<StampTagCmsComponent>(etcResponse);
        stampTagProductList =
            _extractComponents<StampTagProductCmsComponent>(etcResponse);
        etcProductsTagsList = _extractProductTags(etcResponse);
        productStampList =
            _extractComponents<ProductStampCmsComponent>(etcResponse);
      }

      final productsTagsList = response
          .whereType<ProductTagCmsComponent>()
          .map((e) => AzzasProductsTagsModel.fromProductTagCmsComponent(e))
          .toList();

      final productsStampsList =
          response.whereType<ProductStampCmsComponent>().toList();

      final stampTagsList = response.whereType<StampTagCmsComponent>().toList();

      emit(state.copyWith(
        productsTags: productsTagsList,
        productsStamps: productsStampsList,
        stampTags: stampTagsList,
      ));
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
    }

    if (mainResponse == null &&
        stampTagsList.isEmpty &&
        stampTagProductList.isEmpty &&
        etcProductsTagsList.isEmpty &&
        productStampList.isEmpty) {
      return;
    }

    emit(state.copyWith(
      productsTags: productsTagsList,
      stampTags: stampTagsList,
      stampTagsProduct: stampTagProductList,
      etcProductsTagsList: etcProductsTagsList,
      productStamp: productStampList.isNotEmpty
          ? [...productStampList, ...productStampList]
          : null,
    ));
  }

  List<AzzasProductsTagsModel> _extractProductTags(
      List<CmsComponent>? response) {
    return response
            ?.whereType<ProductTagCmsComponent>()
            .map(AzzasProductsTagsModel.fromProductTagCmsComponent)
            .toList() ??
        [];
  }

  List<T> _extractComponents<T>(List<CmsComponent> response) {
    return response.whereType<T>().toList();
  }

  /// Verifica se  tem stamptag disponivel
  bool hasStampTagValid(Product product) {
    final stamptag = getStampTagsProductByProduct(product: product);
    final stampTagProduct = getStampTagsByProduct(product: product);
    return stamptag != null || stampTagProduct != null;
  }

  ///  stampTagProduct tem maior prioridade do que a stampTag
  AzzasProductsTagsModel? getPriorityStampTag(Product product) {
    AzzasProductsTagsModel? stampTabProduct =
        getStampTagsProductByProduct(product: product);
    if (stampTabProduct != null) return stampTabProduct;
    final teste = getStampTagsByProduct(product: product);
    return teste;
  }

  List<AzzasProductsTagsModel> getProductsTagsByProduct(
      {required Product product, bool isEtc = false}) {
    final effectiveList =
        isEtc ? state.etcProductsTagsList : state.productsTags;
    final filteredTags = effectiveList?.where((tag) {
      return product
          .isFromCategoriesOrClusters([tag.filterCategoryOrCluster ?? '']);
    }).toList();

    return filteredTags!.length > 2 ? filteredTags.sublist(0, 2) : filteredTags;
  }

  AzzasProductsTagsModel? getStampTagsByProduct({required Product product}) {
    final filteredTags = state.stampTags?.where((tag) {
      return product.isFromCategoriesOrClusters([tag.filterCategoryOrCluster]);
    }).toList();
    if (filteredTags == null || filteredTags.isEmpty) return null;
    return AzzasProductsTagsModel.fromStampTagCmsComponent(filteredTags.first);
  }

  AzzasProductsTagsModel? getStampTagsProductByProduct({
    required Product product,
  }) {
    final filteredTags = state.stampTagsProduct?.where((tag) {
      return tag.productId.contains(product.productId);
    }).toList();

    if (filteredTags == null || filteredTags.isEmpty) return null;

    return AzzasProductsTagsModel.fromStampTagProductCmsComponent(
      filteredTags.first,
    );
  }

  List<ProductStampCmsComponent>? getProductsStampsByProduct({
    required Product product,
  }) {
    final filteredProductsStamps = state.productStamp
            ?.where(
              (tag) => product
                  .isFromCategoriesOrClusters([tag.filterCategoryOrCluster]),
            )
            .toList() ??
        [];

    return filteredProductsStamps.length > 2
        ? filteredProductsStamps.sublist(0, 3)
        : filteredProductsStamps;
  }
  
  Stream<List<ProductStampCmsComponent>> getProductsStampsByProduct(
      {required Product product}) {
    final filteredStamps = state.productsStamps?.where((stamp) {
          return product
              .isFromCategoriesOrClusters([stamp.filterCategoryOrCluster]);
        }).toList() ??
        [];

    return Stream.value(filteredStamps.length > 3
        ? filteredStamps.sublist(0, 3)
        : filteredStamps);
  }

  Stream<List<StampTagCmsComponent>> getStampTagsByProduct(
      {required Product product}) {
    final filteredStampTags = state.stampTags?.where((stampTag) {
          return product
              .isFromCategoriesOrClusters([stampTag.filterCategoryOrCluster]);
        }).toList() ??
        [];

    return Stream.value(filteredStampTags.length > 3
        ? filteredStampTags.sublist(0, 3)
        : filteredStampTags);
  }
}
