import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/utils/transparent_pointer.dart';
import 'package:flutter/material.dart';

/// Função auxiliar para construir o widget de reloginhos do header, usando o
/// [HeaderClocksBuilder].
/// Ela recebe o [ClockPage] para identificar a página atual e o [ClocksCubit]
/// para acessar o estado dos reloginhos.
/// Se desejar, você pode passar um [customClockBuilder] para personalizar o widget do reloginho.
/// Se não for passado, o widget padrão do [HeaderClockCmsWidget] será usado.
Widget? buildClockHeaderFor(
  ClockPage page, {
  required ClocksCubit clocksCubit,
  Widget Function()? customClockBuilder,
  Search? search,
}) {
  final activeClock = clocksCubit.getActiveClockFor(page, search: search);

  if (activeClock == null) return null;

  return HeaderClocksBuilder(
    customClockBuilder: customClockBuilder,
    clockComponent: activeClock,
  );
}

/// Widget responsável por usar o BlocBuilder para exibição dos reloginhos do
/// header, bem como englobar o reloginho com um [AzzasSliverAppBarThemeOverride] para
/// aplicar o tema correto na AppBar.
class HeaderClocksBuilder extends StatelessWidget {
  final ClockHeaderCmsComponent clockComponent;
  final Widget Function()? customClockBuilder;

  const HeaderClocksBuilder({
    super.key,
    required this.clockComponent,
    this.customClockBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return AzzasSliverAppBarThemeOverride(
      collapsedTheme:
          AppBarTheme(backgroundColor: clockComponent.clock.backgroundColor),
      child: customClockBuilder?.call() ??
          AzzasCountdownBuilder(
              endTime: clockComponent.clock.endTime,
              builder: (_, remainingTime) {
                return HeaderClockCmsWidget.fromComponent(
                  component: clockComponent,
                  remainingTime: remainingTime,
                );
              }),
    );
  }
}

/// Builder que tem a responsabilidade de renderizar o reloginho nas vitrines
/// da PDC, sobrepondo-se ao conteúdo da vitrine.
/// Ele recebe o [ClocksCubit] para acessar o estado dos reloginhos e o [Product]
/// para identificar o reloginho ativo para determinado produto.
Widget? buildSpotProductOverlayClock({
  Widget Function()? customClockBuilder,
  required ClocksCubit clocksCubit,
  required Product product,
  required double imageSize,
}) {
  final activeClock = clocksCubit.getFirstActiveClockProductFor(product);

  if (activeClock == null) return null;

  return Positioned(
    right: 0,
    left: 0,
    top: imageSize,
    child: FractionalTranslation(
      translation: const Offset(0, -1), // Movimenta o reloginho para cima
      child: AzzasCountdownBuilder(
          endTime: activeClock.endTime,
          builder: (_, remainingTime) {
            return customClockBuilder?.call() ??
                TransparentPointer(
                  child: SpotProductOverlayClockCmsWidget.fromComponent(
                    activeClock,
                    remainingTime: remainingTime,
                  ),
                );
          }),
    ),
  );
}

/// Função auxiliar para construir o reloginho simples que é exibido na PDP.
/// Ela recebe o [ClocksCubit] para acessar o estado dos reloginhos e o [Product]
/// para identificar o reloginho ativo para determinado produto.
Widget? buildSimpleClockForPdp({
  required ClocksCubit clocksCubit,
  required Product product,
}) {
  final activeClock = clocksCubit.getFirstActiveClockProductFor(product);

  if (activeClock == null) return null;

  return AzzasCountdownBuilder(
    endTime: activeClock.endTime,
    builder: (_, remainingTime) {
      return SimplePdpClockCmsWidget.fromComponent(
        activeClock,
        remainingTime: remainingTime,
      );
    },
  );
}

class AzzasCountdownBuilder extends StatefulWidget {
  const AzzasCountdownBuilder({
    super.key,
    required this.endTime,
    required this.builder,
    this.onEnd,
  });

  final DateTime? endTime;
  final VoidCallback? onEnd;
  final CountdownWidgetBuilder builder;

  @override
  State<AzzasCountdownBuilder> createState() => _AzzasCountdownBuilderState();
}

class _AzzasCountdownBuilderState extends State<AzzasCountdownBuilder> {
  final controller = ClockPromotionController();

  @override
  void initState() {
    super.initState();
    if (widget.endTime != null) {
      controller.startTimer(widget.endTime!, widget.onEnd);
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          final isActive = controller.isActive;
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (child, animation) => SizeTransition(
              sizeFactor: animation,
              child: child,
            ),
            child: isActive
                ? widget.builder(
                    context,
                    controller.timeNotifier.value ?? Duration.zero,
                  )
                : const SizedBox.shrink(),
          );
        });
  }
}

typedef CountdownWidgetBuilder = Widget Function(
  BuildContext context,
  Duration remainingTime,
);

enum ClockPage {
  wishlist('wishlist'),
  bag('sacola'),
  customLandingPage('hub de conteudo'),
  exploreMenu('menu'),
  occasion('ocasiões de uso'),
  home('home'),
  pdc('pdc'),
  pdp('pdp');

  final String cmsValue;

  const ClockPage(this.cmsValue);
}
