import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import 'package:url_launcher/url_launcher_string.dart' as url_launcher_string;

enum LaunchMode {
  externalApplication(url_launcher.LaunchMode.externalApplication),
  externalNonBrowserApplication(
      url_launcher.LaunchMode.externalNonBrowserApplication),
  platformDefault(url_launcher.LaunchMode.platformDefault);

  final url_launcher.LaunchMode _urlLauncherLaunchMode;

  const LaunchMode(this._urlLauncherLaunchMode);
}

class Browsable with Redirectable {
  final String url;
  const Browsable({required this.url});
  @override
  String get redirectArgument => url;

  @override
  RedirectType get redirectType => RedirectType.browser;
}

class Webview with Redirectable {
  final String url;
  const Webview({required this.url});
  @override
  String get redirectArgument => url;

  @override
  RedirectType get redirectType => RedirectType.externalLink;
}

class RedirectUtils {
  const RedirectUtils();

  // ignore: deprecated_member_use
  Future<bool> canLaunch(String uri) => url_launcher.canLaunch(uri);

  Future<bool> canLaunchString(String str) =>
      url_launcher_string.canLaunchUrlString(str);

  Future<bool> canLaunchUrl(String uri) =>
      url_launcher.canLaunchUrl(Uri.parse(uri));

  Future<bool> launchUri(
    Uri uri, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) =>
      url_launcher.launchUrl(uri, mode: mode._urlLauncherLaunchMode);

  Future<bool> launchString(String str) =>
      url_launcher_string.launchUrlString(str);

  Future<void> executeRedirect(Redirectable redirectable) async {
    switch (redirectable.redirectType) {
      case RedirectType.browser:
        if (!kIsWeb && await canLaunchString(redirectable.redirectArgument)) {
          await launchUri(Uri.parse(redirectable.redirectArgument));
        } else {
          await canLaunchString(redirectable.redirectArgument);
        }
        break;
      default:
        await Modular.to.pushNamed('/');
    }
  }

  void launchWhatsapp(Whatsapp whatsappConfig) async {
    final phone = whatsappConfig.phone;
    String? message = whatsappConfig.text;
    String webURL =
        "https://wa.me/$phone${message != null ? "?text=${Uri.encodeComponent(message)}" : ""}";

    if (PlatformUtils.isAndroid && await canLaunchUrl(webURL)) {
      try {
        await launchUri(Uri.parse(webURL));
      } catch (_) {
        return;
      }
    } else if (PlatformUtils.isIOS && await canLaunchUrl(webURL)) {
      await launchUri(Uri.parse(webURL));
    } else {
      await canLaunchUrl(webURL);
    }
  }
}

mixin Redirectable {
  RedirectType get redirectType;
  String get redirectArgument;
  String? get title => null;
  String? get filterCategoryOrCluster => null;
  String? get filterColor => null;
  String? get filterSize => null;
  String? get query => null;
  String? get orderByIntelligentSearch => null;

  Search getSearch() {
    return Search(
      title: title,
      filterCategoryOrCluster: filterCategoryOrCluster,
      filterColor: filterColor,
      filterSize: filterSize,
      query: query,
      orderBy: orderByIntelligentSearch != null
          ? Search.getOrderByString(orderByIntelligentSearch!)
          : null,
    );
  }
}
