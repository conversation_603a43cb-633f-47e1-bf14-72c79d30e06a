import 'package:azzas_app_commons/azzas_app_commons.dart';

class NavigatorDynamic {
  NavigatorDynamic();

  static String _removeScheme(String route) {
    route = route.replaceAll('https://', '');
    route = route.replaceAll('http://', '');
    route = route.replaceAll(' ', '');
    return route;
  }

  static String _getRoute(List<String> segments) {
    List<String> localSegments = List<String>.from(segments);
    localSegments.removeAt(0);
    return localSegments.join('/');
  }

  static String _getRedirectLink(String route) {
    return _getRoute(
      _removeScheme(route).split('/'),
    );
  }

  static Future<void> call(
    String route, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) async {
    final uri = Uri.tryParse(route);
    final List<String> segments;
    if (uri != null && uri.pathSegments.isNotEmpty) {
      segments = uri.pathSegments;
    } else {
      segments = route.split('/');
    }

    final path = segments.first.toLowerCase();
    final authCubit = Modular.get<AuthCubit>();
    final transitionCubit = Modular.get<TransitionCubit>();
    String? currentRoute;
    dynamic currentArguments;
    final appConfig = Modular.get<AppConfig>();

    switch (path) {
      case 'home':
        switchTabByBrand(
          tab: MainPageTab.home,
          tabFarm: MainPageTabFarm.home,
        );
      case 'mochila':
        currentRoute = "/";
        switchTabByBrand(
          tab: MainPageTab.bag,
          tabFarm: MainPageTabFarm.bag,
        );
        break;
      case 'menu':
        currentRoute = "/";
        switchTabByBrand(
          tab: MainPageTab.menu,
          tabFarm: MainPageTabFarm.menu,
        );
        String? menuTab = segments.length > 1 ? segments[1] : null;
        currentArguments = {"initialTabIndex": menuTab};
        break;

      case 'plp':
        if (arguments != null) {
          currentRoute = "/pdc";
          currentArguments = arguments;
        } else if (segments.length > 1) {
          currentRoute = "/pdc";
          final search = segments.sublist(1).join('/');
          final orderBy = uri?.queryParameters['ordenacao']
              ?.mapNotEmpty(OrderBy.fromString);
          final title = uri?.queryParameters['titulo'] ?? parameters?['titulo'];
          final filterColor = uri?.queryParameters['filtro-de-cores'];
          final filterSize = uri?.queryParameters['filtro-de-tamanhos'];
          final filterCategories = uri?.queryParameters['filtro-de-categorias'];
          final filterPrice = uri?.queryParameters['filtro-de-preco'];

          if (!search.contains('/')) {
            currentArguments = Search(
              query: search,
              title: title ?? search,
              orderBy: orderBy,
              filterColor: filterColor,
              filterSize: filterSize,
              filterCategories: filterCategories,
              filterPrice: filterPrice,
            );
          } else {
            currentArguments = Search(
              filterCategoryOrCluster: '/$search',
              title: title,
              orderBy: orderBy,
              filterColor: filterColor,
              filterSize: filterSize,
              filterCategories: filterCategories,
              filterPrice: filterPrice,
            );
          }
        }
        break;

      case 'pdp':
        currentRoute = '/pdp';
        String? productId = segments[1];
        final intelligentSearchUseCase =
            Modular.get<IntelligentSearchUseCase>();
        Product? result =
            await intelligentSearchUseCase.getProductByRefOrId(productId);
        if (result == null) return;

        ///Adicionar verificação se é um produto da Farm Etc, para redirecionar para a página correspondente.
        ///É através dessa verificação que o deeplink sabe para qual PDP direcionar, pois ele utiliza a navegação dinâmica.
        if (result.brand == "Farm Etc") {
          currentRoute = '/pdp_etc';
        }
        currentArguments = result;

        break;

      case 'home_etc':
        Modular.to.pushNamed('/transaction', arguments: transitionCubit);
        break;

      case 'menu_etc':
        final cubit = Modular.get<MainPageCubit>();
        currentRoute = "/";
        transitionCubit.modeEtc(true);
        cubit.switchTabFarm(MainPageTabFarm.menu);
        String? menuTab = segments.length > 1 ? segments[1] : null;
        currentArguments = {"initialTabIndex": menuTab};
        break;

      case 'pdp_etc':
        currentRoute = '/pdp_etc';
        String? productId = segments[1];
        final intelligentSearchUseCase =
            Modular.get<IntelligentSearchUseCase>();
        Product? result =
            await intelligentSearchUseCase.getProductByRefOrId(productId);
        if (result == null) return;
        currentArguments = result;
        break;

      case 'plp_etc':
        if (arguments != null) {
          currentRoute = "/pdc_etc";
          currentArguments = arguments;
        } else if (segments.length > 1) {
          currentRoute = "/pdc_etc";
          final search = segments.sublist(1).join('/');
          final orderBy = uri?.queryParameters['ordenacao']
              ?.mapNotEmpty(OrderBy.fromString);
          final title = uri?.queryParameters['titulo'] ?? parameters?['titulo'];
          final filterColor = uri?.queryParameters['filtro-de-cores'];
          final filterSize = uri?.queryParameters['filtro-de-tamanhos'];
          final filterCategories = uri?.queryParameters['filtro-de-categorias'];
          final filterPrice = uri?.queryParameters['filtro-de-preco'];

          if (!search.contains('/')) {
            currentArguments = Search(
              query: search,
              title: title ?? search,
              orderBy: orderBy,
              filterColor: filterColor,
              filterSize: filterSize,
              filterCategories: filterCategories,
              filterPrice: filterPrice,
            );
          } else {
            currentArguments = Search(
              filterCategoryOrCluster: '/$search',
              title: title,
              orderBy: orderBy,
              filterColor: filterColor,
              filterSize: filterSize,
              filterCategories: filterCategories,
              filterPrice: filterPrice,
            );
          }
        }
        break;
      case 'sugestoes':
      case 'suggestions':
        if (uri!.path.isNotEmpty) {
          var path = uri.path.split("/");

          String? identifier = Uri.decodeFull(path.last);

          if (identifier.isNullOrEmpty || identifier == "sugestoes") {
            Modular.to.pushNamed('/wishlist', arguments: 1);
          } else {
            Modular.to.pushNamed('/suggetions_page',
                arguments: SuggetionsPageParams(
                  title: identifier,
                  byDeepLink: true,
                  description: '',
                  productList: [],
                ));
          }
        }

        break;

      case 'webview':
        currentRoute = "/webview";
        String url = 'https://${_getRedirectLink(route)}';
        currentArguments = (arguments as WebViewParams?) ??
            WebViewParams(
              url: url,
            );
        break;
      case 'external-link':
        String url =
            'https://${_getRedirectLink(route.replaceAll("external-link/", ''))}';
        final redirectUtils = Modular.get<RedirectUtils>();
        redirectUtils.executeRedirect(Browsable(url: url));
        break;

      case 'full-look':
        currentRoute = '/full-look';
        break;

      case 'wishlist':
        if (appConfig.brand == Brand.farm) {
          Modular.to.pushNamed('/account/wishlist');
        } else {
          currentRoute = "/";
          switchTabByBrand(
            tab: MainPageTab.wishlist,
          );
        }
        break;
      case 'login':
        currentRoute = '/account/login_email_password';
        currentArguments = {"register": false};

        break;
      case 'perfil':
        currentRoute = "/";
        switchTabByBrand(
          tab: MainPageTab.myAccount,
          tabFarm: MainPageTabFarm.myAccount,
        );
        break;
      case 'carteira-cashback':
        currentRoute =
            currentRoute = authCubit.state.isLoggedIn ? '/checking' : "/";
        switchTabMyAccount();
        break;
      case 'pedidos':
        currentRoute = currentRoute =
            authCubit.state.isLoggedIn ? "/account/my_orders" : "/";
        switchTabMyAccount();
        break;
      case 'order-detail':
        currentRoute = '/account/orders_detail';
        currentArguments = arguments;
        switchTabMyAccount();
        break;
      case 'dados-pessoais':
        currentRoute =
            authCubit.state.isLoggedIn ? "/account/personal_data" : "/";
        switchTabMyAccount();
        break;
      case 'central-notificacoes':
        currentRoute = currentRoute =
            authCubit.state.isLoggedIn ? "/notification-center" : "/";
        switchTabMyAccount();
        break;

      default:
        switchTabByBrand(
          tab: MainPageTab.home,
          tabFarm: MainPageTabFarm.home,
        );
    }

    if (currentRoute?.isNotEmpty == true) {
      Modular.to.pushNamed(currentRoute!, arguments: currentArguments);
    }
  }

  static void switchTabByBrand({
    MainPageTab? tab,
    MainPageTabFarm? tabFarm,
  }) {
    final cubit = Modular.get<MainPageCubit>();
    final appConfig = Modular.get<AppConfig>();
    switch (appConfig.brand) {
      case Brand.farm:
        if (tabFarm != null) cubit.switchTabFarm(tabFarm);
        break;
      default:
        if (tab != null) cubit.switchTab(tab);
        break;
    }
  }

  static void switchTabMyAccount() {
    switchTabByBrand(
      tab: MainPageTab.myAccount,
      tabFarm: MainPageTabFarm.myAccount,
    );
  }

  static String? getFilterCategoryOrCluster(String? path) {
    if (path != null && path.startsWith('plp/')) {
      final uri = Uri.parse(path);
      final result = uri.pathSegments.skip(1).join('/').trim();
      return result.isNotEmpty ? '/$result' : null;
    }
    return null;
  }
}
