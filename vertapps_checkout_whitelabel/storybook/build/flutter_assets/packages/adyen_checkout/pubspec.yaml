name: adyen_checkout
description: Adyen checkout library for Flutter. Accept payments with cards, wallets and local payment methods in your app using our Drop-in and Components.
version: 1.0.0
repository: https://github.com/Adyen/adyen-flutter
issue_tracker: https://github.com/Adyen/adyen-flutter/issues
documentation: https://docs.adyen.com/online-payments/build-your-integration

topics:
  - payments
  - payment
  - checkout
  - ecommerce
  - online-payments

environment:
  sdk: ^3.0.6
  flutter: ">=3.10.6"

dependencies:
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.1.7
  stream_transform: ^2.1.0
  pay: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: 3.0.1
  pigeon: 15.0.3

flutter:
  assets:
    - pubspec.yaml
  plugin:
    platforms:
      android:
        package: com.adyen.checkout.flutter
        pluginClass: AdyenCheckoutPlugin
      ios:
        pluginClass: AdyenCheckoutPlugin
