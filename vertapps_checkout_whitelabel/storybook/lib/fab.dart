import 'package:flutter/material.dart';
import 'brands_enum.dart';

class MyHomePage extends StatefulWidget {
  final void Function(Brand) onPressed;

  MyHomePage({required this.onPressed});

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  bool isOpened = false;

  final List<Brand> brands = [
    Brand.farm,
    Brand.crisbarros,
  ];

  final double spacing = 10.0; // Espaçamento entre os botões

  void toggle() {
    setState(() {
      isOpened = !isOpened;
    });
  }

  void actionAndClose() {
    toggle(); // Chama a função para fechar os FABs depois de uma ação
  }

  Widget toggleButton() {
    return FloatingActionButton(
      onPressed: toggle,
      backgroundColor: Colors.black,
      child: Icon(isOpened ? Icons.close : Icons.more_vert),
    );
  }

  List<Widget> buildSpeedDial() {
    List<Widget> children = [];

    for (int i = 0; i < brands.length; i++) {
      children.add(
        Padding(
          padding: EdgeInsets.only(bottom: spacing),
          child: FloatingActionButton(
            backgroundColor: Colors.transparent,
            heroTag: null,
            tooltip: brands[i].tooltip,
            onPressed: () {
              print('Pressed ${brands[i].tooltip}');
              widget.onPressed(brands[i]);
              actionAndClose(); // Executa uma ação e fecha o dial
            },
            child: SizedBox(
              height: 56,
              width: 56,
              child: Image.asset(
                fit: BoxFit.fitWidth,
                brands[i].imagePath,
                filterQuality: FilterQuality.high,
                isAntiAlias: true,
              ),
            ),
          ),
        ),
      );
    }

    return children;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: isOpened
              ? 56.0 * (brands.length + 1) + 10.0 * brands.length
              : 56.0,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: isOpened ? buildSpeedDial() : [],
          ),
        ),
        toggleButton(),
      ],
    );
  }
}
