import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'fab.dart';
import 'stories/components/components_story.dart';
import 'stories/foundations/foundations_story.dart';
import 'stories/templates/templates_story.dart';
import 'token_manager.dart';
import 'package:storybook_flutter/storybook_flutter.dart';

void main() {
  runApp(
    const StorybookApp(),
  );
}

class StorybookApp extends StatefulWidget {
  const StorybookApp({super.key});

  @override
  State<StorybookApp> createState() => _StorybookAppState();
}

class _StorybookAppState extends State<StorybookApp> {
  ThemeMode themeMode = ThemeMode.system;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TokenManager(),
      child: Consumer<TokenManager>(
        builder: (context, tokensManager, child) {
          const scrollBehavior = AppScrollBehavior();
          return MaterialApp(
            locale: const Locale('pt', 'BR'),
            supportedLocales: const [
              Locale('pt', 'BR'),
            ],
            scrollBehavior: scrollBehavior,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            themeMode: themeMode,
            theme: ThemeData(
              useMaterial3: false,
              scaffoldBackgroundColor: Colors.white,
            ),
            home: Storybook(
              plugins: [
                ...initializePlugins(enableThemeMode: false),
                ThemeModePlugin(
                  onThemeChanged: (value) {
                    setState(() {
                      themeMode = value;
                    });
                  },
                )
              ],
              initialLayout: Layout.expanded,
              initialStory: 'Foundations / Colors / Feedback',
              wrapperBuilder: (BuildContext context, Widget? child) {
                return Scaffold(
                  floatingActionButton: MyHomePage(
                    onPressed: (b) => tokensManager.changeBrand(b),
                  ),
                  body: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 48, bottom: 48),
                      child: Center(child: child),
                    ),
                  ),
                );
              },
              stories: [
                ...FoundationsStory().call(tokensManager.tokens),
                ...ComponentsStory().call(tokensManager.tokens),
                ...TemplatesStory().call(tokensManager.tokens),
              ],
            ),
          );
        },
      ),
    );
  }
}

class AppScrollBehavior extends MaterialScrollBehavior {
  const AppScrollBehavior();

  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}
