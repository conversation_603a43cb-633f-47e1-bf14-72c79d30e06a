import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/login_exp.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class LoginStory {
  List<Story> call(CheckoutDStokens tokens) {
    return [
      Story(
          name: 'Templates / Login / Login',
          builder: (context) {
            LoginTemplateAlignment align = context.knobs.options(
                label: 'Alinhamento',
                initial: LoginTemplateAlignment.middle,
                options: const [
                  Option(label: 'Down', value: LoginTemplateAlignment.down),
                  Option(label: 'Up', value: LoginTemplateAlignment.up),
                  Option(label: 'Middle', value: LoginTemplateAlignment.middle),
                ]);

            String title = context.knobs.text(
                initial: "Título de página com até 2 linhas.", label: 'Título');
            String subtitle = context.knobs.text(
                initial: "Subtítulo da página com até 3 linhas.",
                label: 'Subtítulo');

            LoginTemplateType types = context.knobs.options(
                label: 'Type',
                initial: LoginTemplateType.fullImage,
                options: const [
                  Option(
                      label: 'Full Image', value: LoginTemplateType.fullImage),
                  Option(
                      label: 'Image on bottom',
                      value: LoginTemplateType.imageOnBottom),
                ]);

            bool inverse = context.knobs.boolean(
              label: 'Inverse',
              initial: true,
            );

            String socialLoginFirstButtonText = context.knobs
                .text(initial: "Google", label: 'socialLoginFirstButtonText');
            String socialLoginSecondButtonText = context.knobs
                .text(initial: "Apple", label: 'socialLoginSecondButtonText');

            String signInFirstButtonText = context.knobs.text(
                initial: 'Entrar apenas com email',
                label: "signInFirstButtonText");
            String signInSecondButtonText = context.knobs.text(
                initial: 'Entrar com email e senha',
                label: "signInSecondButtonText");

            final config = LoginTemplateConfig(
              alignment: align,
              type: types,
              onPressSignUp: () {},
              title: title,
              subtitle: subtitle,
              isInverse: inverse,
              socialLoginFirstButtonOnPressed: () {},
              socialLoginFirstButtonText: socialLoginFirstButtonText,
              socialLoginSecondButtonOnPressed: () {},
              socialLoginSecondButtonText: socialLoginSecondButtonText,
              socialLoginFirstButtonIcon: tokens.icons.google!,
              socialLoginSecondButtonIcon: tokens.icons.apple!,
              signInFirstButtonText: signInFirstButtonText,
              signInFirstButtonOnPressed: () {},
              signInSecondButtonText: signInSecondButtonText,
              signInSecondButtonOnPressed: () {},
              onBackButton: () {},
              isSocialLoginLoading: false,
            );

            return CdsLoginTemplate(
              tokens: tokens,
              config: config,
              isStory: true,
            );
          })
    ];
  }
}
