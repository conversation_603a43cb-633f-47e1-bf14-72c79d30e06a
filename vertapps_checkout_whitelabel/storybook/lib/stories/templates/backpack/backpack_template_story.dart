import 'package:flutter/material.dart';
import 'package:storybook/stories/components/composition_components/cds_product_item/utils/show_snackbar.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/backpack/backpack.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class BackpackTemplateStory {
  List<Story> call(CheckoutDStokens tokens) {
    return [
      Story(
        name: 'Templates / Backpack / Com produtos',
        builder: (context) {
          final status = context.knobs.options<CdsProductStatus>(
            label: 'Estado do card de produto',
            initial: CdsProductStatus.available,
            options: const [
              Option(label: "available", value: CdsProductStatus.available),
              Option(label: "unavailable", value: CdsProductStatus.unavailable),
              Option(label: "lowStock", value: CdsProductStatus.lowStock),
              Option(
                label: "unavailableInThisZipCode",
                value: CdsProductStatus.unavailableInThisZipCode,
              ),
            ],
          );

          final size = context.knobs.text(label: "Tamanho", initial: 'M');
          final quantity =
              context.knobs.text(label: "Quantidade", initial: '2');

          final title = context.knobs.text(
            label: "Nome do produto",
            initial: 'Vestido Longo Manga Sonho De Mar',
          );

          final imageUrl = context.knobs.text(
            label: "Imagem do produto",
            initial:
                "https://lojafarm.vteximg.com.br/arquivos/ids/3543803-708-1062/337560_50383_1-VESTIDO-LONGO-MANGA-SONHO-DE-MAR.jpg?v=638762034153170000",
          );

          final isGiftProduct = context.knobs.boolean(
            label: 'Embalagem para presente',
            initial: true,
          );

          final isEnabled =
              context.knobs.boolean(label: "Checkbox ativado", initial: true);

          final isSelected = context.knobs
              .boolean(label: "Checkbox habilitado", initial: true);

          final fullPrice = context.knobs
              .sliderInt(label: "Preço cheio", initial: 10000, max: 1000000);

          final salePrice = context.knobs.sliderInt(
              label: "Preço com desconto", initial: 10000, max: 1000000);

          final showGifts =
              context.knobs.boolean(label: "Exibir brindes", initial: false);

          final applySellerCode = context.knobs
              .boolean(label: "Código de vendedora aplicado", initial: false);

          final products = [
            CdsProductModel(
              productId: '1',
              price: CdsProductPriceModel(
                fullPrice: fullPrice.toDouble(),
                salePrice: salePrice.toDouble(),
              ),
              quantity: quantity,
              size: size,
              title: title,
              shippingDate: 3,
              status: status,
              imageUrl: imageUrl,
              isGiftProduct: isGiftProduct,
              checkbox: CdsProductCheckboxModel(
                isEnabled: isEnabled,
                value: isSelected,
                onChanged: (newValue) {},
              ),
              ctas: CdsProductCtasModel(
                onSave: (
                    {required String itemId, required String productId}) async {
                  ShowSnackbar.show(
                      context, "Produto $productId salvo com sucesso!", tokens);
                },
                onRemove: ({required String itemId}) async {
                  ShowSnackbar.show(
                      context, "Produto $itemId removido.", tokens);
                },
                onWarning: ({required String productId}) async {
                  ShowSnackbar.show(
                      context, "Aviso sobre o produto $productId.", tokens);
                },
                onProductInfo: ({
                  required String itemId,
                  required String productId,
                }) async {
                  ShowSnackbar.show(
                      context, "Sobre o produto $productId.", tokens);
                },
              ),
              itemId: '1',
            ),
            CdsProductModel(
                productId: '2',
                price: CdsProductPriceModel(
                  fullPrice: fullPrice.toDouble(),
                  salePrice: salePrice.toDouble(),
                ),
                quantity: quantity,
                size: size,
                title: title,
                shippingDate: 3,
                status: status,
                imageUrl: imageUrl,
                isGiftProduct: isGiftProduct,
                checkbox: CdsProductCheckboxModel(
                  isEnabled: isEnabled,
                  value: isSelected,
                  onChanged: (newValue) {},
                ),
                ctas: CdsProductCtasModel(
                  onSave: (
                      {required String itemId,
                      required String productId}) async {
                    ShowSnackbar.show(context,
                        "Produto $productId salvo com sucesso!", tokens);
                  },
                  onRemove: ({required String itemId}) async {
                    ShowSnackbar.show(
                        context, "Produto $itemId removido.", tokens);
                  },
                  onWarning: ({required String productId}) async {
                    ShowSnackbar.show(
                        context, "Aviso sobre o produto $productId.", tokens);
                  },
                  onProductInfo: ({
                    required String itemId,
                    required String productId,
                  }) async {
                    ShowSnackbar.show(
                        context, "Sobre o produto $productId.", tokens);
                  },
                ),
                itemId: '2'),
            CdsProductModel(
              productId: '2',
              price: CdsProductPriceModel(
                fullPrice: fullPrice.toDouble(),
                salePrice: salePrice.toDouble(),
              ),
              quantity: quantity,
              size: size,
              title: title,
              shippingDate: 3,
              status: status,
              itemId: '3',
              imageUrl: imageUrl,
              isGiftProduct: isGiftProduct,
              checkbox: CdsProductCheckboxModel(
                isEnabled: isEnabled,
                value: isSelected,
                onChanged: (newValue) {},
              ),
              ctas: CdsProductCtasModel(
                onSave: (
                    {required String itemId, required String productId}) async {
                  ShowSnackbar.show(
                      context, "Produto $productId salvo com sucesso!", tokens);
                },
                onRemove: ({required String itemId}) async {
                  ShowSnackbar.show(
                      context, "Produto $itemId removido.", tokens);
                },
                onWarning: ({required String productId}) async {
                  ShowSnackbar.show(
                      context, "Aviso sobre o produto $productId.", tokens);
                },
                onProductInfo: ({
                  required String itemId,
                  required String productId,
                }) async {
                  ShowSnackbar.show(
                      context, "Sobre o produto $productId.", tokens);
                },
              ),
            ),
          ];

          int maxInstallments = context.knobs
              .sliderInt(label: "Máximo de parcelas", initial: 1, max: 30);

          String deliveryValue =
              context.knobs.text(label: 'Valor da entrega', initial: '');
          String discountValue = context.knobs
              .text(label: 'Valor do desconto', initial: 'R\$ 10,00');

          final CdsOrderResumeConfig orderResumeConfig = CdsOrderResumeConfig(
            maxInstallments: maxInstallments,
            productLength: ((int.tryParse(quantity) ?? 1) * 3),
            deliveryValue: deliveryValue,
            totalProducts: "R\$ 410,00",
            totalValue: "R\$ 400,00",
            discountValue: discountValue,
          );

          int totalForFreeDelivery = context.knobs.sliderInt(
              label: "Total pra frete grátis", initial: 750, max: 1000);

          final freeDeliveryConfig = CdsFreeDeliveryConfig(
            amountSpent: 400,
            amountNeededForFreeDelivery: totalForFreeDelivery,
            buttonText: "Explorar produtos",
            onPressButton: () {},
            title: const Text('Title'),
            subtitle: const Text(
                'Válido somente para “frete normal” para regiões Sul, Sudeste e Centro-oeste.'),
          );

          CdsBrandInformationConfig brandInformationConfig =
              const CdsBrandInformationConfig(
            imageUrl:
                "https://upload.wikimedia.org/wikipedia/commons/a/a3/Image-not-found.png?20210521171500",
            title: "Título com até duas linhas",
            description: "Título com até três linhas",
          );

          CdsProductSmallCtas wishlistCtas = CdsProductSmallCtas(
              onAddToBag: (_) async {}, onRemoveFromWishlist: (_) async {});

          CdsWishlistProductsConfig wishlistConfig = CdsWishlistProductsConfig(
            productListLength: 5,
            title: "Seus desejos salvos",
            description: "Leve agora o que você deixou para depois",
            productsList: [
              CdsProductSmallModel.fromCdsProductModel(
                  products[0], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(
                  products[1], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(
                  products[2], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(
                  products[0], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(
                  products[1], wishlistCtas),
            ],
            onTapLoadMoreProducts: () {},
            onChangeCategories: (categoryName) {},
            categories: [
              const CategoriesOptions(
                selected: false,
                name: "Vestido",
                quantity: 9,
              ),
              const CategoriesOptions(
                selected: false,
                name: "Calça",
                quantity: 1,
              ),
              const CategoriesOptions(
                selected: false,
                name: "Macacão",
                quantity: 8,
              ),
              const CategoriesOptions(
                selected: false,
                name: "Blusa",
                quantity: 2,
              ),
              const CategoriesOptions(
                selected: false,
                name: "Saia",
                quantity: 1,
              ),
              const CategoriesOptions(
                selected: false,
                name: "Calcinha",
                quantity: 4,
              ),
            ],
          );

          final showOptionsDeliveryAndPickup = context.knobs.boolean(
              label: "Mostrar opções de retirada e entrega", initial: false);

          final pickupDeliveryOptionsTitle = context.knobs.text(
            label: "Título Delivery / Pickup",
            initial: "Como você deseja receber?",
          );
          final fastDeliveryAbailable = context.knobs.boolean(
            label: 'Entrega rápida disponível',
            initial: false,
          );
          final deliveryOptionSelected = context.knobs.boolean(
              label: "Checkbox Delivery Option habilitado", initial: false);

          final deliveryDate1 = context.knobs.text(
            label: "Data de \"Receba até\"",
            initial: "20/01",
          );

          final deliveryDate2 = context.knobs.text(
            label: "Data de \"Receba até\"",
            initial: "25/01",
          );

          final deliveryDate3 = context.knobs.text(
            label: "Data de \"Receba até\"",
            initial: "29/01",
          );

          final deliveryDates = [
            deliveryDate1,
            deliveryDate2,
            deliveryDate3,
          ];

          final CdsDeliveryOptionInformationConfig deliveryOptionsConfig =
              CdsDeliveryOptionInformationConfig(
            hasSelectedAddress: false,
            isFavorite: false,
            formattedAddress: '',
            selected: deliveryOptionSelected,
            fastDeliveryAvailable: fastDeliveryAbailable,
            totalValue: 100,
            date: deliveryDates,
            hyperLinkCallback: () {},
            onSelect: (bool? b) {},
            packages: [],
          );

          final pickupStoreOptionSelected = context.knobs.boolean(
              label: "Checkbox Pickup Option habilitado", initial: false);

          final address = context.knobs.text(
            label: "Endereço",
            initial:
                "Nome da Rua - 1234 - Bairro 00.123-456 | Nome da cidade - MG",
          );

          final pickupStoreName = context.knobs.text(
            label: "Nome da loja",
            initial: "Loja FARM Rio Sul ",
          );

          final showAvailableStores = context.knobs
              .boolean(label: "Mostrar loja selecionada", initial: false);

          final String discountPercent = context.knobs.text(
              label: 'Desconto (%) na retirada do produto', initial: '10');

          final String pickupStoreDate = context.knobs
              .text(label: 'Data de retirada na loja', initial: "22/01");

          final CdsPickupStoreOptionInformationConfig pickupStoreOptionConfig =
              CdsPickupStoreOptionInformationConfig(
            changeSelectedStore: (CdsStore store) {},
            deliveryValuOtherItems: deliveryValue,
            date: [pickupStoreDate],
            discount: discountPercent,
            showAvailableStores: showAvailableStores,
            selected: pickupStoreOptionSelected,
            availableStores: () {},
            deliveryDetailsCallback: () {},
            changeStoreCallback: () {},
            onSelect: () {},
            selectedStore: CdsStore(
              address: address,
              storeName: pickupStoreName,
            ),
          );

          final CdsPickupDeliveryInformationConfig pickupDeliveryConfig =
              CdsPickupDeliveryInformationConfig(
            deliveryOptionConfig: deliveryOptionsConfig,
            pickupStoreConfig: pickupStoreOptionConfig,
            title: pickupDeliveryOptionsTitle,
          );

          String coupon =
              context.knobs.text(label: 'Cupom de desconto', initial: '');
          int couponsAvailables = context.knobs.sliderInt(
              label: "Número de cupons disponiveis", initial: 2, max: 10);

          final couponConfig = CdsCouponConfig(
            coupon: coupon,
            onTapApllyOrChangeCoupon: () {},
            onTapRemoveCoupon: () {},
            availableCoupons: couponsAvailables,
          );

          CdsGiftsConfig giftsConfig = CdsGiftsConfig(
              title: "Você ganhou um brinde!",
              description: "Escolha aqui qual você mais gostou:",
              onSelected: (value, giftSelected) {},
              giftsList: [
                CdsGiftCardConfig(
                  name: "Garrafinha Glub Glub",
                  size: size,
                  quantity: 1,
                  isSelected: true,
                  imageUrl: imageUrl,
                  id: '',
                  seller: '',
                ),
                CdsGiftCardConfig(
                  name: "Garrafinha Glob Glob",
                  size: size,
                  quantity: 1,
                  isSelected: false,
                  imageUrl: imageUrl,
                  id: '',
                  seller: '',
                ),
              ]);

          final sellerCodeConfig = CdsSellerCodeConfig(
            onTapApllyOrChangeSellerCode: () {},
            onTapRemoveSellerCode: () {},
            // genericBenefits: "10% OFF e Frete Grátis",
            // rules: "Em compras acima de R\$ XXX,00",
            selectedSellerCode: applySellerCode
                ? const SelectedSellerCode(
                    sellerCode: 'C123 - Nome da vendedora completo',
                    // benefits: 'Insira o benefício aqui',
                    // underText: 'Texto Legal',
                  )
                : null,
          );

          final productListConfig = CdsProductListConfig(
            products: products,
            totalSelected:
                products.where((e) => e.checkbox?.value ?? false).length,
            onSelectAllProducts: (value) {},
            isAllProductsSelected: isSelected,
            onRemoveProduct: (_) async => false,
            onSaveProduct: (_, __) async => false,
          );

          final giftPackagingConfig = GiftPackagingConfig(
              productsAvailables: 1, onTap: () {}, productsSelected: 1);
          return ScaffoldMessenger(
            child: CdsBackpackTemplate(
              tokens: tokens,
              productListConfig: productListConfig,
              onShareTap: () {},
              onEditShipping: () {},
              orderResumeConfig: orderResumeConfig,
              freeDeliveryConfig: freeDeliveryConfig,
              brandInformationConfigs: [
                brandInformationConfig,
                brandInformationConfig
              ],
              wishlistConfig: wishlistConfig,
              pickupDeliveryConfig:
                  showOptionsDeliveryAndPickup ? pickupDeliveryConfig : null,
              giftsConfigs: showGifts ? giftsConfig : null,
              couponConfig: couponConfig,
              sellerCodeConfig: sellerCodeConfig,
              giftPackagingConfig: giftPackagingConfig,
              setShowAddressCard: (bool) {},
              scrollController: ScrollController(),
              showAddresses: () {},
              targetKey: GlobalKey(),
              scrollViewKey: GlobalKey(),
              scrollToTarget: () {},
            ),
          );
        },
      )
    ];
  }
}
