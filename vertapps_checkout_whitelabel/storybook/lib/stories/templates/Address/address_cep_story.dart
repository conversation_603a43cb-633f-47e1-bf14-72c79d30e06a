import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/address/cds_address_cep_template.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class AddressCepStory {
  List<Story> call(CheckoutDStokens tokens) {
    final controller = TextEditingController();
    final streetController =
        TextEditingController(text: 'avenida das américas');
    final numberController = TextEditingController();
    final neighborhoodController =
        TextEditingController(text: 'barra da tijuca');
    final complementController = TextEditingController();
    final addressIdentificationController = TextEditingController();
    final recipientController = TextEditingController();

    return [
      Story(
        name: 'Templates / Address / Cep',
        builder: (context) {
          final cepValid = context.knobs.options(
            label: 'Cep valido',
            initial: false,
            options: const [
              Option(label: 'Verdade<PERSON>', value: true),
              Option(label: 'Falso', value: false),
            ],
          );

          return CdsAddressCepTemplate(
            tokens: tokens,
            cepController: controller,
            isLoading: false,
            onPressedButton: () {},
            onBackButton: () {},
            isCepValid: cepValid,
            isDisabled: true,
            city: 'Rio de janeiro - RJ',
            streetController: streetController,
            numberController: numberController,
            neighborhoodController: neighborhoodController,
            complementController: complementController,
            addressIdentificationController: addressIdentificationController,
            recipientController: recipientController,
            firstCheckbox: true,
            secondCheckbox: true,
            ontapFirstCheckbox: (value) {},
            ontapSecondCheckbox: (value) {},
            validateInput: () {},
            saveAddressEnabled: false,
          );
        },
      )
    ];
  }
}
