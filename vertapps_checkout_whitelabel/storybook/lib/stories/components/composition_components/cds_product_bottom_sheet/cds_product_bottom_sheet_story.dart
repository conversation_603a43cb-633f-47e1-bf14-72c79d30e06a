import 'package:flutter/material.dart';
import 'package:storybook/stories/components/composition_components/cds_product_item/utils/show_snackbar.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/composition_components.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';

class CdsProductBottomSheetStory {
  List<Story> call(CheckoutDStokens tokens) {
    return [
      Story(
        name: 'Components / Composition Components / Product Bottom Sheet',
        builder: (context) {
          final title = context.knobs.text(
            label: "Nome do produto",
            initial: 'Vestido Longo Manga Sonho De Mar',
          );

          final fullPrice = context.knobs
              .slider(label: "Preço cheio", initial: 10000, max: 1500000);

          final salePrice = context.knobs.slider(
              label: "Preço com desconto", initial: 10000, max: 1500000);

          final noAvailableItems = context.knobs.boolean(
            label: 'Nenhum tamanho disponível',
            initial: false,
          );

          final isLoading = context.knobs.boolean(
            label: 'Mostrar carregamento',
            initial: false,
          );

          final options = [
            Option(label: 'PP', value: !noAvailableItems && false),
            Option(label: 'P', value: !noAvailableItems && true),
            Option(label: 'M', value: !noAvailableItems && false),
            Option(label: 'GG', value: !noAvailableItems && true),
            Option(label: 'XGG', value: !noAvailableItems && false),
          ];

          final List<CdsSkuProductItemModel> items =
              List.generate(options.length, (index) {
            final option = options[index];
            return CdsSkuProductItemModel(
              id: (index + 1).toString(),
              sellerId: '1',
              size: option.label,
              price: fullPrice,
              priceOnSale: salePrice,
              minInstallmentValue: 200,
              maxInstallments: 6,
              isAvailable: option.value,
                availableQuantity: 1
            );
          });

          return Center(
            child: SizedBox(
              width: 400,
              child: CdsProductBottomSheet(
                isSizeAvailable: true,
                onSizeSelected: (_) {},
                initialSelectedSize: 'P',
                tokens: tokens,
                productScrollController: ScrollController(),
                cdsProductContentParams: CdsProductContentParams(
                  quantity: 1,
                  onProductAdd: () {},
                  onProductDelete: () {},
                  onProductRemove: () {},
                ),
                cdsBottomContentParams: CdsBottomContentParams(
                  onSaveForLater: () {
                    ShowSnackbar.show(
                        context, "Produto salvo para depois", tokens);
                  },
                  onNavigateToProductPage: () {
                    ShowSnackbar.show(
                        context, "Navegar para tela de produto", tokens);
                  },
                  onRemindMe: () {
                    ShowSnackbar.show(
                        context, "Me alertar sobre o produto", tokens);
                  },
                  onProductRemove: () {
                    ShowSnackbar.show(context, "Produto removido", tokens);
                  },
                ),
                isLoading: isLoading,
                product: CdsSkuProductModel(
                    id: '123',
                    name: title,
                    images: [
                      'https://lojafarm.vteximg.com.br/arquivos/ids/3555019/342335_51509_2-MACAQUINHO-BALONE-FOLHAGEM-DE-INVERNO.jpg?',
                      'https://lojafarm.vteximg.com.br/arquivos/ids/3555030/342335_51509_3-MACAQUINHO-BALONE-FOLHAGEM-DE-INVERNO.jpg?'
                    ],
                    skuProductItems: items),
                isSizeAvailable: false,
              ),
            ),
          );
        },
      )
    ];
  }
}
