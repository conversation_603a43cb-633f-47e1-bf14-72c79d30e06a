{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "adyen_checkout", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/adyen_checkout-1.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "app_settings", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appsflyer_sdk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/appsflyer_sdk-6.16.21/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_insider", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_insider-3.18.2+nh/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_review", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_review-2.0.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "pay_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/pay_ios-1.0.11/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "recaptcha_enterprise_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "android": [{"name": "adyen_checkout", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/adyen_checkout-1.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "app_settings", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appsflyer_sdk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/appsflyer_sdk-6.16.21/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_insider", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_insider-3.18.2+nh/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_review", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_review-2.0.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "pay_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/pay_android-2.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "recaptcha_enterprise_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.9/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "macos": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_review", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_review-2.0.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "linux": [{"name": "app_links_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/", "native_build": false, "dependencies": ["gtk"], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "gtk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "windows": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "web": [{"name": "app_links_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+16/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.3/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.10/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_performance_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+16/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_inappwebview_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}, {"name": "video_player_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/", "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "dependencies": ["package_info_plus"], "dev_dependency": false}]}, "dependencyGraph": [{"name": "adyen_checkout", "dependencies": ["pay"]}, {"name": "app_links", "dependencies": ["app_links_linux", "app_links_web"]}, {"name": "app_links_linux", "dependencies": ["gtk"]}, {"name": "app_links_web", "dependencies": []}, {"name": "app_settings", "dependencies": []}, {"name": "appsflyer_sdk", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_performance", "dependencies": ["firebase_core", "firebase_performance_web"]}, {"name": "firebase_performance_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_inappwebview_windows", "dependencies": []}, {"name": "flutter_insider", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "gtk", "dependencies": []}, {"name": "in_app_review", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "pay", "dependencies": ["pay_android", "pay_ios"]}, {"name": "pay_android", "dependencies": []}, {"name": "pay_ios", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "recaptcha_enterprise_flutter", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}], "date_created": "2025-07-29 10:12:54.787633", "version": "3.32.7", "swift_package_manager_enabled": {"ios": false, "macos": false}}