{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.59", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "adyen_checkout", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/adyen_checkout-1.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "app_links", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "app_links_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_links_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_links_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_settings", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "appsflyer_sdk", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/appsflyer_sdk-6.16.21", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "archive", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "auto_injector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/auto_injector-2.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "azzas_analytics", "rootUri": "../../azzas_analytics", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "azzas_app_commons", "rootUri": "../../azzas_app_commons", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "azzas_cms", "rootUri": "../../azzas_cms", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "azzas_core", "rootUri": "../../azzas_core", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "azzas_ui", "rootUri": "../../azzas_ui", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "bloc", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cached_network_image", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "date_picker_plus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/date_picker_plus-4.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dbus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dio", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/dio-5.7.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_cache_interceptor", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/dio_cache_interceptor-3.5.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "dio_web_adapter", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "fake_async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "firebase_analytics", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.6.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_auth", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_crashlytics", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_crashlytics_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.8.10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.10", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_performance", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_performance_dio", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_dio-0.7.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "firebase_performance_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_platform_interface-0.1.5+10", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_performance_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///home/<USER>/snap/flutter/common/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_bloc", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_cache_manager", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_circular_text", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_circular_text-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_html", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_inappwebview", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_insider", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_insider-3.18.2+nh", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_localizations", "rootUri": "file:///home/<USER>/snap/flutter/common/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_modular", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_modular-6.3.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_secure_storage", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_macos", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_slidable", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///home/<USER>/snap/flutter/common/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///home/<USER>/snap/flutter/common/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "geocoding", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "geocoding_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "geocoding_ios", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "geocoding_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "geolocator", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator-13.0.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_apple", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "get", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/get-4.7.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "get_storage", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/get_storage-2.1.1", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "google_identity_services_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_ios", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "gtk", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "in_app_review", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/in_app_review-2.0.10", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "in_app_review_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "js", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "jwt_decoder", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "list_counter", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "local_auth", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "local_auth_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth_darwin", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "local_auth_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "logger", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "lottie", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/lottie-3.1.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "mask_text_input_formatter", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/mask_text_input_formatter-2.9.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "matcher", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "modular_core", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/modular_core-3.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "nested", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_info_plus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pay", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pay-2.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pay_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pay_android-2.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pay_ios", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pay_ios-1.0.11", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pay_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pay_platform_interface-1.0.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "permission_handler", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pretty_dio_logger", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pretty_dio_logger-1.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "recaptcha_enterprise_flutter", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.1", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "result_dart", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/result_dart-1.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "rxdart", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "share_plus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "share_plus_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "simple_gesture_detector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///home/<USER>/snap/flutter/common/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "super_tooltip", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/super_tooltip-2.1.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "synchronized", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "table_calendar", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "term_glyph", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_graphics_codec", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_math", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.9", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "video_player_avfoundation", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "video_player_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.4.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "video_player_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "visibility_detector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/visibility_detector-0.4.0+2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "vm_service", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wakelock_plus", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "win32", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "win32_registry", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vertapps_checkout_whitelabel", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.2"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///home/<USER>/snap/flutter/common/flutter", "flutterVersion": "3.32.7", "pubCache": "file:///home/<USER>/.pub-cache"}