import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/cds_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/enum/cds_sheet_type.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class SplitHelperBottomSheet extends StatelessWidget {
  final CheckoutDStokens tokens;
  final String orderId;

  const SplitHelperBottomSheet({
    super.key,
    required this.tokens,
    required this.orderId,
  });
  @override
  Widget build(BuildContext context) {
    return CdsSheet(
      tokens: tokens,
      title: 'Por que meu pedido foi dividido?',
      onClose: () {
        Navigator.of(context).pop();
      },
      type: CdsSheetType.bottomSheet,
      slotContent: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Os produtos do seu pedido vem de diferentes lojas e centros de distribuição, por isso tiveram que ser separado em pacotes diferentes.\n\nComo acompanhar os pacotes?\n\nPara acompanhamento, os pacotes serão exibidos como pedidos separados na página de Meus Pedidos do seu perfil.\n\nVocê continuará recebendo e-mails com atualização do status de cada pacote separadamente.\n\nO final do número do pedido identifica o pacote mostrado. Por exemplo, o pacote 1 terá o final “-01”, o pacote 2 como “-02”, e assim por diante.",
            style: tokens.typography.bodyStyles.description.copyWith(
              color: tokens.colors.content.pure,
            ),
          ),
          SizedBox(height: tokens.layout.spacing.md),
          Image.asset(
            tokens.images.splitHelperImage ?? 'assets/split_order_helper.png',
            package: 'vertapps_checkout_whitelabel',
            width: 300,
            height: 100,
            fit: BoxFit.cover,
          ),
          SizedBox(height: tokens.layout.spacing.xl),
          CdsButton.primary(
            onPressed: () {
              Modular.to.pop();
            },
            tokens: tokens,
            buttonText: 'Entendi',
            expanded: true,
            size: CdsButtonSize.large,
          ),
          SizedBox(height: tokens.layout.spacing.md),
        ],
      ),
    );
  }
}
