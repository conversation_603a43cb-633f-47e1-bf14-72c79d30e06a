import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';

class CdsBillingAddress extends CdsBaseStatefulTemplate {
  const CdsBillingAddress({
    super.key,
    required this.useAddress,
    required this.updateAddress,
    required super.tokens,
  });
  final bool useAddress;
  final Function(bool value) updateAddress;

  @override
  State<CdsBillingAddress> createState() => _CdsBillingAddressState();
}

class _CdsBillingAddressState extends State<CdsBillingAddress>
    with TickerProviderStateMixin {
  final _paymentCubit = Modular.get<PaymentCubit>();
  bool isButtonDisabled = true;
  Address? addressFromCep;
  String searchCepButton = 'Digite um CEP válido';
  AddressInfoParams? selectedBillingAddress;

  @override
  void initState() {
    super.initState();
    if (_paymentCubit.state.creditCardInfo.currentSelectedBillingAddress !=
        null) {
      selectedBillingAddress = _paymentCubit
          .state.creditCardInfo.currentSelectedBillingAddress!
          .toAddressInfoParams();
    }
  }

  Future<void> _validateCep(String cep) async {
    try {
      if (TextHelper.isCEP(cep)) {
        final address = await _paymentCubit.getAddressFromCep(cep);

        return setState(() {
          addressFromCep = address;
          searchCepButton = 'Inserir endereço da fatura';
        });
      }

      return setState(() {
        searchCepButton = 'Digite um CEP válido';
        addressFromCep = null;
        isButtonDisabled = true;
      });
    } catch (e) {
      return setState(() {
        searchCepButton = 'Digite um CEP válido';
        addressFromCep = null;
        isButtonDisabled = true;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    addressFromCep = null;
  }

  void _setNewBillingAddress({required Address address}) {
    _paymentCubit.addBillingAddressToCard(address);

    Modular.to.pop();
  }

  void _showCreateNewAddressBottomSheet() {
    setState(() {
      addressFromCep = null;
    });
    showAzzasBottomSheet(
      context: context,
      vsync: this,
      builder: (_) {
        return BlocBuilder<PaymentCubit, PaymentState>(
          bloc: _paymentCubit,
          builder: (context, state) {
            return AzzasCreateNewBillingAddressBottomSheet(
              titleStyle: widget.tokens.typography.titleStyles.title4,
              isLoading: state.isLoadingAddress,
              validateCep: (cep) => _validateCep(cep),
              addressFromCep: addressFromCep?.toAddressInfoParams(),
              buttonTitle: searchCepButton,
              locationIcon: Icon(widget.tokens.icons.locationPin),
              onFieldsSubmited: (newAdress) =>
                  _setNewBillingAddress(address: newAdress.toAddress()),
            );
          },
        );
      },
    );
  }

  void _onUpdateAddress() {
    final selectedAddress = selectedBillingAddress?.toAddress();

    if (selectedAddress != null) {
      _paymentCubit.setSelectedBillingAddress(selectedAddress);
      return Modular.to.pop();
    }
  }

  void _showBillingAddressBottomSheet() {
    showAzzasBottomSheet(
      context: context,
      builder: (_) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return BlocBuilder<PaymentCubit, PaymentState>(
              bloc: _paymentCubit,
              builder: (context, state) {
                final billingAddresses =
                    state.creditCardInfo.availableBillingAddresses;

                return AzzasBillingAddressesBottomSheet(
                  titleStyle: widget.tokens.typography.titleStyles.title4,
                  addresses: billingAddresses != null &&
                          billingAddresses.isNotEmpty
                      ? billingAddresses
                          .map((e) => e.toAddressInfoParams(
                                isSelected: selectedBillingAddress?.street ==
                                        e.street &&
                                    selectedBillingAddress?.number == e.number,
                              ))
                          .toList()
                      : [],
                  onCloseTap: () {
                    Modular.to.pop();
                  },
                  onTapCreateNewAddress: () {
                    _showCreateNewAddressBottomSheet();
                  },
                  onAddressUpdated: _onUpdateAddress,
                  onSelectAddress: (address) {
                    setModalState(() {
                      selectedBillingAddress = address;
                    });
                  },
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PaymentCubit, PaymentState>(
      bloc: _paymentCubit,
      builder: (context, state) {
        final address = state.creditCardInfo.currentSelectedBillingAddress;

        return SizedBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Endereço da fatura',
                style: widget.tokens.typography.bodyStyles.description,
              ),
              SizedBox(
                height: widget.tokens.layout.spacing.md,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (address != null) ...[
                    Flexible(
                      flex: 3,
                      child: Column(
                        children: [
                          if (address.addressName != null) ...[
                            Text(
                              address.addressName ?? '',
                              style:
                                  widget.tokens.typography.bodyStyles.caption,
                            ),
                            SizedBox(
                              height: widget.tokens.layout.spacing.xs,
                            ),
                          ],
                          Text(
                            address.formattedFullAddress,
                            style: widget.tokens.typography.bodyStyles.caption
                                .copyWith(
                              color: widget.tokens.colors.content.content03,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: widget.tokens.layout.spacing.md,
                    ),
                  ],
                  AzzasLinkButton(
                    size: ButtonSize.small,
                    style: const AzzasButtonStyle(
                      border: Border(),
                    ),
                    onPressed: () {
                      _showBillingAddressBottomSheet();
                    },
                    trailing: Icon(widget.tokens.icons.arrowRight),
                    child: Text(
                      'Alterar',
                      style: widget.tokens.typography.bodyStyles.caption,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: widget.tokens.layout.spacing.md,
              ),
              Row(
                children: [
                  CdsCheckbox(
                    tokens: widget.tokens,
                    onChanged: (value) {
                      widget.updateAddress(value);
                    },
                    value: widget.useAddress,
                  ),
                  SizedBox(
                    width: widget.tokens.layout.spacing.md,
                  ),
                  Text(
                    'Usar esse endereço na fatura',
                    style: widget.tokens.typography.bodyStyles.description,
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }
}
