import 'dart:io';

import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/core_components/cds_bottom_sheet/cds_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/credit_card.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/credit_card_list.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/list_item_radio.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class SelectFavoritePaymentBottomSheet extends StatefulWidget {
  const SelectFavoritePaymentBottomSheet({
    required this.tokens,
    required this.selectedFavorite,
    required this.enablePixInstallment,
    this.closeIcon,
    this.titleStyle,
    this.bodyStyle,
    this.descriptionStyle,
    this.borderRadius,
    this.showDragBar = true,
    this.padding,
    this.onCloseTap,
    this.buttonStyle,
    this.isLoading = false,
    this.cards,
    this.favoriteCardNumber,
    super.key,
  });
  final IconData? closeIcon;
  final TextStyle? titleStyle;
  final TextStyle? buttonStyle;
  final TextStyle? bodyStyle;
  final TextStyle? descriptionStyle;
  final BorderRadius? borderRadius;
  final bool showDragBar;
  final EdgeInsets? padding;
  final Function()? onCloseTap;
  final bool isLoading;
  final CheckoutDStokens tokens;
  final List<CreditCardModel>? cards;
  final Function(PaymentType, CreditCardInfoData?) selectedFavorite;
  final String? favoriteCardNumber;
  final bool enablePixInstallment;

  @override
  State<SelectFavoritePaymentBottomSheet> createState() =>
      _SelectFavoritePaymentBottomSheetState();
}

class _SelectFavoritePaymentBottomSheetState
    extends State<SelectFavoritePaymentBottomSheet> {
  final appConfig = Modular.get<AppConfig>();
  CreditCardInfoData? selectedCard;
  PaymentType? paymentSelected;
  String? effectiveFavoriteCardNumber;

  @override
  void initState() {
    effectiveFavoriteCardNumber = setEffectiveFavoriteCardNumber();
    super.initState();
  }

  String? setEffectiveFavoriteCardNumber() {
    return selectedCard?.cardNumber ?? widget.favoriteCardNumber;
  }

  @override
  Widget build(BuildContext context) {
    return CdsBottomSheet(
      tokens: widget.tokens,
      showDragBar: widget.showDragBar,
      closeIcon: widget.tokens.icons.close,
      onCloseTap: widget.onCloseTap ?? () => Modular.to.pop(),
      borderRadius: widget.borderRadius ?? BorderRadius.zero,
      padding:
          widget.padding ?? EdgeInsets.all(widget.tokens.layout.spacing.lg),
      content: SizedBox.shrink(),
      scrollContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selecione um favorito',
            style: widget.titleStyle ??
                widget.tokens.typography.subtitleStyles.subtitle2,
          ),
          const SizedBox(height: 24),
          Text(
            'A forma de pagamento escolhida como favorita virá pré-selecionada, agilizando suas próximas compras.',
            style: widget.bodyStyle ??
                widget.tokens.typography.bodyStyles.description.copyWith(
                  color: widget.tokens.colors.content.content03,
                ),
          ),
          if (widget.cards != null) ...[
            const SizedBox(height: 16),
            CreditCardPaymentOption(
              onCreateCard: () {},
              creditCardFlow: CreditCardFlow.selectFavoriteCard,
              onNewCreditCardSelected: (cardInfo) {
                setState(() {
                  selectedCard = cardInfo;
                  effectiveFavoriteCardNumber =
                      setEffectiveFavoriteCardNumber();
                  paymentSelected = PaymentType.creditCard;
                });
              },
              hasPaymentAndCardSelected:
                  paymentSelected == PaymentType.creditCard &&
                      selectedCard != null,
              isNewCreditCardSelected: false,
              favoriteCardNumber: effectiveFavoriteCardNumber,
              tokens: widget.tokens,
              title: 'Cartão de crédito',
              subtitle: 'Em até 10x sem juros',
              icon: widget.tokens.icons.creditCard,
              items: widget.cards ?? [],
              selectedCard: selectedCard,
              secondCardSelected: null,
              hasOnlyCards: true,
              cvvController: TextEditingController(),
            ),
          ],
          if (widget.enablePixInstallment) ...[
            const SizedBox(height: 16),
            ListItemRadio(
              onTap: () {
                setState(() {
                  paymentSelected = PaymentType.pixInstallment;
                });
              },
              title: 'Pix parcelado',
              subtitle: 'Em até 4x sem juros',
              leftIcon: Icon(
                widget.tokens.icons.pix,
              ),
              isChecked: paymentSelected == PaymentType.pixInstallment,
              tokens: widget.tokens,
            ),
            const SizedBox(height: 16),
          ],
          ListItemRadio(
            onTap: () {
              setState(() {
                paymentSelected = PaymentType.pix;
              });
            },
            title: 'Pix',
            leftIcon: Icon(
              widget.tokens.icons.pix,
            ),
            isChecked: paymentSelected == PaymentType.pix,
            tokens: widget.tokens,
            cumstomTitle: Row(
              children: [
                Text(
                  'Pix',
                  style: widget.tokens.typography.bodyStyles.paragraph,
                  overflow: TextOverflow.ellipsis,
                ),
                // const SizedBox(
                //   width: 8,
                // ),
                // CdsChipsHighlight(
                //   variation: CdsChipsHihglightVariation.success,
                //   title: '',
                //   chipsSize: CdsChipsSize.small,
                //   tokens: widget.tokens,
                // ),
              ],
            ),
          ),
          if (Platform.isIOS && appConfig.applePayConfig != null)
            ListItemRadio(
              title: 'Apple pay',
              leftIcon: Icon(
                widget.tokens.icons.applePay,
                size: 16,
              ),
              onTap: () {
                setState(() {
                  paymentSelected = PaymentType.applePay;
                });
              },
              isChecked: paymentSelected == PaymentType.applePay,
              tokens: widget.tokens,
            ),
          SizedBox(height: widget.tokens.layout.spacing.md),
          CdsButton.primary(
            tokens: widget.tokens,
            onPressed: () {
              widget.selectedFavorite(paymentSelected!, selectedCard);
            },
            buttonText: 'Selecione uma opção',
            size: CdsButtonSize.large,
            expanded: true,
            isDisabled: paymentSelected == null,
          )
        ],
      ),
    );
  }
}

class CheckoutInstallmentInfoParams {
  CheckoutInstallmentInfoParams({
    required this.installmentText,
    required this.isChecked,
    required this.count,
    required this.value,
    this.description,
  });

  final String installmentText;
  final bool isChecked;
  final int count;
  final num value;
  final String? description;
}
