import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/core_components/cds_textfield/enum/cds_text_field_mask.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/animated_credit_card.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/billing_address.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/select_installments_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/show_checkout_bottom_sheet.dart';

class CardInfoData {
  final String cardNumber;
  final String cardHolderName;
  final String cardExpireDate;
  final String cardSecurityCode;
  final String cardHolderDocument;

  CardInfoData({
    required this.cardNumber,
    required this.cardHolderName,
    required this.cardHolderDocument,
    required this.cardExpireDate,
    required this.cardSecurityCode,
  });
}

class CdsPaymentCardInfoPage extends CdsBaseStatefulTemplate {
  const CdsPaymentCardInfoPage({
    super.key,
    required super.tokens,
    required this.args,
  });

  final CdsPaymentArgs? args;
  @override
  State<CdsPaymentCardInfoPage> createState() => _CdsPaymentCardInfoPageState();
}

class _CdsPaymentCardInfoPageState extends State<CdsPaymentCardInfoPage>
    with TickerProviderStateMixin {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _paymentCubit = Modular.get<PaymentCubit>();
  final eventDispatcher = Modular.get<EventDispatcher>();
  final creditCardNumber = TextEditingController();
  final creditCardHolderName = TextEditingController();
  final creditCardExpireDate = TextEditingController();
  final creditCardSecurityCode = TextEditingController();
  final userDocument = TextEditingController();
  final creditCardNickName = TextEditingController();
  final ValueNotifier<CardInfoData> cardInfoData =
      ValueNotifier<CardInfoData>(CardInfoData(
    cardNumber: '',
    cardHolderName: '',
    cardExpireDate: '',
    cardSecurityCode: '',
    cardHolderDocument: '',
  ));
  InstallmentOrderForm? selectedInstallment;

  //TODO: Talvez seja melhor mover esses estados para um cubit próprio dessa tela posteriormente
  bool useAddress = true;
  bool disableButton = true;
  bool isLoading = false;
  bool useAddressError = false;
  final formKey = GlobalKey<FormState>();
  bool isCreditCardFieldsValid = false;
  bool saveCard = true;
  bool saveAsFavoriteCard = false;

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'card_payment_info');
    _paymentCubit.setAddressFromOrderForm();
    _getFavoritePayment();
    _getLocalFields();
    super.initState();
  }

  void _getLocalFields() {
    final card = _paymentCubit.state.creditCardInfo;
    if (card.cardNumber != null && card.cardAccountId == null) {
      creditCardNumber.text = card.cardNumber ?? '';
      creditCardHolderName.text = card.cardHolderName ?? '';
      creditCardExpireDate.text = card.expirationDate ?? '';
      creditCardSecurityCode.text = card.cvv ?? '';
      userDocument.text = card.cardHolderDocument ?? '';
      onChangedInput();
    }
  }

  void _getFavoritePayment() {
    final hasFavorite = _orderFormCubit.state.favoriteOption != null;
    setState(() {
      saveAsFavoriteCard = !hasFavorite;
    });
  }

  void validateFields() {
    useAddressError = !useAddress;

    bool areCreditCardFieldsFilled = creditCardNumber.text.isNotEmpty &&
        creditCardHolderName.text.isNotEmpty &&
        creditCardExpireDate.text.isNotEmpty &&
        creditCardSecurityCode.text.isNotEmpty &&
        userDocument.text.isNotEmpty;
    setState(() {
      isCreditCardFieldsValid = areCreditCardFieldsFilled &&
          (formKey.currentState?.validate() ?? false);
    });

    if (areCreditCardFieldsFilled &&
        !useAddressError &&
        selectedInstallment != null) {
      if (formKey.currentState?.validate() ?? false) {
        setState(() {
          disableButton = false;
        });
      } else {
        setState(() {
          disableButton = true;
        });
      }
    } else {
      setState(() {
        disableButton = true;
      });
    }
  }

  void onChangedInput() {
    cardInfoData.value = CardInfoData(
      cardNumber: creditCardNumber.text,
      cardHolderName: creditCardHolderName.text,
      cardExpireDate: creditCardExpireDate.text,
      cardSecurityCode: creditCardSecurityCode.text,
      cardHolderDocument: userDocument.text,
    );
    validateFields();
  }

  void _showInstallmentsBottomSheet() {
    showCheckoutBottomSheet(
      context: context,
      builder: (_) {
        final paymentSystemIdFromCardNumber =
            CreditCardHelper.getPaymentSystemIdByCreditCardNumber(
                cardNumber: cardInfoData.value.cardNumber);

        final availableInstallments = _paymentCubit
            .getPaymentInstallmentsForCreditCard(paymentSystemIdFromCardNumber);

        return StatefulBuilder(builder: (context, setState) {
          return SelectInstallmentsBottomSheet(
            tokens: widget.tokens,
            selectInstallmentText: selectedInstallment == null
                ? "Selecione um parcelamento"
                : "Selecionar este parcelamento",
            onCloseTap: () async {
              Modular.to.pop();
            },
            showDragBar: true,
            borderRadius: widget.tokens.shapes.borderRadius.md,
            installmentsSubtitleTextStyle:
                widget.tokens.typography.bodyStyles.description,
            onSelectInstallment: (installment) {
              final selectedInstallment =
                  availableInstallments.firstWhereOrNull(
                (element) =>
                    element.count == installment.count &&
                    element.value == installment.value,
              );

              if (selectedInstallment == null) {
                return;
              }
              setState(() {
                this.selectedInstallment = selectedInstallment;
              });
            },
            installments: availableInstallments
                .map((installment) =>
                    installment.toCheckoutInstallmentInfoParams(
                      isChecked: installment.equalsTo(
                        selectedInstallment,
                      ),
                    ))
                .toList(),
            isDisabled: selectedInstallment == null,
            titleStyle: widget.tokens.typography.titleStyles.title4,
            currentInstallment: selectedInstallment?.label(),
            onButtonPressed: () async {
              AzzasAnalyticsEvents.logSelectContent(
                contentType:
                    'pagamento:parcelamento:${selectedInstallment?.count}',
              );
              validateFields();
              Modular.to.pop();
            },
          );
        });
      },
      vsync: this,
    );
  }

  Future<void> _navigateToOrderReview() async {
    if (!formKey.currentState!.validate() ||
        selectedInstallment == null ||
        !isCreditCardFieldsValid) {
      return;
    }
    final paymentSytemId =
        CreditCardHelper.getPaymentSystemIdByCreditCardNumber(
      cardNumber: cardInfoData.value.cardNumber,
    );

    final creditCardEnum =
        CreditCardHelper.getTypeByPaymentSystem(paymentSytemId);

    final paymentSystemName = CreditCardHelper.getNameByType(creditCardEnum);

    final newCreditCardInfoData = CreditCardInfoData(
      cardNumber: cardInfoData.value.cardNumber,
      cardHolderName: cardInfoData.value.cardHolderName,
      cardHolderDocument: cardInfoData.value.cardHolderDocument,
      expirationDate: cardInfoData.value.cardExpireDate,
      cvv: cardInfoData.value.cardSecurityCode,
      currentSelectedBillingAddress:
          _paymentCubit.state.creditCardInfo.currentSelectedBillingAddress,
      isNew: true,
      selectedPaymentSystemId: paymentSytemId,
      selectedPaymentSystemName: paymentSystemName,
      saveAsFavoriteCard: saveAsFavoriteCard,
      saveCard: saveCard,
    );
    try {
      setState(() {
        isLoading = true;
      });
      await _paymentCubit.selectCreditCardPayment(
        creditCardInfo: newCreditCardInfoData,
        installment: selectedInstallment!,
      );

      await eventDispatcher.logAddPaymentInfo(paymentType: AzzasAnalyticsPaymentType.creditCard);
      Modular.to.popAndPushNamed(
        '/checkout/order-review',
        result: false,
        arguments: CdsOrderReviewArgs(
          goToBackPack: (bool? showAddressCard) {
            widget.args?.goToBackPack.call(showAddressCard);
          },
          onTapViewBalance: () {
            widget.args?.onTapViewBalance.call();
          },
        ),
      );
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: BlocBuilder<PaymentCubit, PaymentState>(
          bloc: _paymentCubit,
          builder: (context, state) {
            final areCreditCardFieldsFilled =
                creditCardNumber.text.isNotEmpty &&
                    creditCardHolderName.text.isNotEmpty &&
                    creditCardExpireDate.text.isNotEmpty &&
                    creditCardSecurityCode.text.isNotEmpty &&
                    userDocument.text.isNotEmpty;

            final selectInstallmentsText =
                areCreditCardFieldsFilled && selectedInstallment != null
                    ? '${selectedInstallment!.label(
                        showMultiplication: true,
                      )} ${selectedInstallment!.description}'
                    : 'Selecione uma opção';

            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (didPop, result) async {
                if (didPop) {
                  return;
                }
                final navigator = Navigator.of(context);
                _paymentCubit.clearSelectedPayment();
                navigator.pop(true);
              },
              child: Column(
                children: [
                  CdsTopBar(
                    tokens: widget.tokens,
                    config: CdsTopBarConfig(
                      type: CdsTopBarType.small,
                      title: 'Novo cartão',
                      pinned: true,
                      titleTextStyle:
                          widget.tokens.typography.subtitleStyles.subtitle2,
                      onBackButton: () {
                        Modular.to.pop(true);
                      },
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.tokens.layout.spacing.md,
                    ),
                    child: Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: widget.tokens.layout.spacing.xs),
                          Text(
                            'Adicionar novo cartão',
                            style: widget
                                .tokens.typography.subtitleStyles.subtitle1,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          Text(
                            'Preencha as informações para prosseguir',
                            style: widget
                                .tokens.typography.bodyStyles.description
                                .copyWith(
                              color: widget.tokens.colors.content.content02,
                            ),
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.xxl,
                          ),
                          ValueListenableBuilder(
                            valueListenable: cardInfoData,
                            builder: (context, value, child) {
                              return CdsCreditCardAnimatedCard(
                                cardCvv: cardInfoData.value.cardSecurityCode,
                                cardExpirationDate:
                                    cardInfoData.value.cardExpireDate,
                                cardHolderName:
                                    cardInfoData.value.cardHolderName,
                                cardNumber: cardInfoData.value.cardNumber,
                                tokens: widget.tokens,
                              );
                            },
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.lg,
                          ),
                          CdsTextField(
                            controller: creditCardNumber,
                            label: 'Número do cartão',
                            enableSupportingText: false,
                            validator: (value) {
                              if (value != null) {
                                if (value.isEmpty) {
                                  return "Digite um número de cartão válido";
                                }
                                final isValid =
                                    CreditCardHelper.validateCardByflag(
                                        cardNumber: value);
                                return !isValid
                                    ? "Digite um número de cartão válido"
                                    : null;
                              }
                              return null;
                            },
                            keyboardType:
                                const TextInputType.numberWithOptions(),
                            inputFormatter:
                                CdsInputMask.creditCardNumber.formatter,
                            onChanged: (value) {
                              onChangedInput();
                            },
                            tokens: widget.tokens,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.sm,
                          ),
                          CdsTextField(
                            controller: creditCardHolderName,
                            label: 'Nome do titular',
                            enableSupportingText: false,
                            validator: (value) => (value?.isEmpty ?? true)
                                ? "Digite um nome válido"
                                : null,
                            onChanged: (value) {
                              onChangedInput();
                            },
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            tokens: widget.tokens,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Flexible(
                                child: CdsTextField(
                                  controller: creditCardExpireDate,
                                  label: 'Data de validade',
                                  enableSupportingText: false,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(),
                                  inputFormatter:
                                      CdsInputMask.dateDDMM.formatter,
                                  validator: (value) {
                                    if (value != null && value.length >= 5) {
                                      final isNotValidDate =
                                          CreditCardHelper.validateExpiryDate(
                                              value);
                                      return isNotValidDate
                                          ? "Digite uma data válida"
                                          : null;
                                    }
                                    return "Digite uma data válida";
                                  },
                                  autoValidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  onChanged: (value) {
                                    onChangedInput();
                                  },
                                  tokens: widget.tokens,
                                ),
                              ),
                              SizedBox(
                                width: widget.tokens.layout.spacing.md,
                              ),
                              Flexible(
                                child: CdsTextField(
                                  controller: creditCardSecurityCode,
                                  label: 'CVV',
                                  enableSupportingText: false,
                                  autoValidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  inputFormatter: CdsInputMask.cvv.formatter,
                                  validator: (value) => value == null ||
                                          value.isEmpty ||
                                          (value.length != 3 &&
                                              value.length != 4)
                                      ? "Digite um código válido"
                                      : null,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(),
                                  onChanged: (value) {
                                    onChangedInput();
                                  },
                                  tokens: widget.tokens,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          CdsTextField(
                            controller: userDocument,
                            inputFormatter: CdsInputMask.cpf.formatter,
                            label: 'CPF',
                            enableSupportingText: false,
                            keyboardType:
                                const TextInputType.numberWithOptions(),
                            validator: (value) => StringHelper.isCpf(value!)
                                ? null
                                : "Digite um CPF válido",
                            autoValidateMode:
                                AutovalidateMode.onUserInteraction,
                            onChanged: (value) {
                              validateFields();
                            },
                            tokens: widget.tokens,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          CdsTextField(
                            controller: creditCardNickName,
                            enableSupportingText: false,
                            label: 'Apelido do cartão (opcional)',
                            tokens: widget.tokens,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          Text(
                            'Parcelamento:',
                            style: widget.tokens.typography.bodyStyles.caption,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          CdsButton.secondary(
                            onPressed: () {
                              _showInstallmentsBottomSheet();
                            },
                            isDisabled: !isCreditCardFieldsValid,
                            buttonText: selectInstallmentsText,
                            tokens: widget.tokens,
                            icon: widget.tokens.icons.arrowDown,
                            iconPosition: CdsButtonIconPosition.right,
                            expanded: true,
                            size: CdsButtonSize.large,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          Divider(
                            color:
                                widget.tokens.colors.surface.surfaceContainer,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.xl,
                          ),
                          Row(
                            children: [
                              Text(
                                'Salvar cartão',
                                style: widget
                                    .tokens.typography.bodyStyles.description,
                              ),
                              const Spacer(),
                              CdsSwitcher(
                                tokens: widget.tokens,
                                value: saveCard,
                                onChanged: (value) {
                                  setState(() {
                                    saveCard = value;
                                    if (!value) {
                                      saveAsFavoriteCard = false;
                                    }
                                  });
                                },
                              ),
                            ],
                          ),
                          if (!saveCard) ...[
                            SizedBox(
                              height: widget.tokens.layout.spacing.sm,
                            ),
                            Text(
                              'Ao desmarcar essa opção seus dados não serão salvos. Caso precise editar alguma informação terá que preencher todos os campos novamente.',
                              style: widget.tokens.typography.bodyStyles.caption
                                  .copyWith(
                                color: widget.tokens.colors.content.content03,
                              ),
                            )
                          ],
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          Row(
                            children: [
                              Text(
                                'Definir como pagamento favorito',
                                style: widget
                                    .tokens.typography.bodyStyles.description,
                              ),
                              SizedBox(
                                width: 24,
                                height: 24,
                                child: CdsTooltip(
                                  tokens: widget.tokens,
                                  message:
                                      'O pagamento favorito virá pré-selecionado para agilizar suas próximas compras. Não se preocupe, você sempre pode alterar a forma de pagamento antes de fechar a compra.',
                                  child: Icon(
                                    widget.tokens.icons.info,
                                    size: 24,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              CdsSwitcher(
                                tokens: widget.tokens,
                                value: saveAsFavoriteCard,
                                onChanged: (value) {
                                  setState(() {
                                    saveAsFavoriteCard =
                                        !saveCard ? false : value;
                                  });
                                },
                              ),
                            ],
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.xl,
                          ),
                          Divider(
                            color:
                                widget.tokens.colors.surface.surfaceContainer,
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          CdsBillingAddress(
                            useAddress: useAddress,
                            updateAddress: (value) {
                              setState(() {
                                useAddressError = !value;
                                useAddress = value;
                              });
                              validateFields();
                            },
                            tokens: widget.tokens,
                          ),
                          if (useAddressError) ...[
                            SizedBox(
                              height: widget.tokens.layout.spacing.md,
                            ),
                            Text(
                              'Confirme ou altere o endereço da fatura',
                              style: widget.tokens.typography.bodyStyles.caption
                                  .copyWith(
                                      color: widget.tokens.colors.feedback
                                          .feedbackError.content),
                            ),
                          ],
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                          BlocBuilder<OrderFormCubit, OrderFormState>(
                            bloc: _orderFormCubit,
                            builder: (context, orderFormState) {
                              return CdsButton.primary(
                                tokens: widget.tokens,
                                size: CdsButtonSize.large,
                                expanded: true,
                                isDisabled: disableButton,
                                isLoading: isLoading,
                                onPressed: () {
                                  _navigateToOrderReview();
                                },
                                buttonText: disableButton
                                    ? 'Preencha as informações'
                                    : 'Continuar para revisão',
                              );
                            },
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.md,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
