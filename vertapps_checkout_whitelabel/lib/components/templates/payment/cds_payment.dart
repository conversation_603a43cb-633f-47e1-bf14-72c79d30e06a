import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/widgets/available_credit_info.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/card_favorite.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/credit_card.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/credit_card_list.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/list_item_radio.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class CdsPaymentTemplate extends CdsBaseStatefulTemplate {
  const CdsPaymentTemplate({
    super.key,
    required super.tokens,
    required this.goToOrderReview,
    required this.paymentWarning,
    required this.cvvController,
    required this.selectedCard,
    required this.secondCardSelected,
    required this.selectedInstallmentForCard,
    required this.paymentSelected,
    required this.hasWarning,
    required this.totalCreditBalance,
    required this.totalCashbackBalance,
    required this.availableCreditBalance,
    required this.availableCashbackValue,
    required this.amountToUseCreditBalance,
    required this.amountToUseCashbackBalance,
    required this.payOnlyCreditBalance,
    required this.buttonIsDisabled,
    required this.giftCardsToExpire,
    required this.showExpireWarning,
    required this.showCreditBottomSheet,
    required this.favoriteOption,
    required this.orderForm,
    required this.isLoading,
    required this.showChooseFavoriteBottomSheet,
    required this.updatePaymentSelected,
    required this.showInstallmentsBottomSheet,
    required this.onCreateNewCard,
    required this.onFinishPayment,
    required this.selectedPaymentType,
    required this.isIos,
    this.applePayButton,
    this.onTapBack,
  });

  final Function goToOrderReview;
  final PaymentWarningModel? paymentWarning;
  final TextEditingController cvvController;
  final CreditCardInfoData? selectedCard;
  final CreditCardInfoData? secondCardSelected;
  final String? selectedInstallmentForCard;
  final PaymentType? paymentSelected;
  final bool hasWarning;
  final double totalCreditBalance;
  final double totalCashbackBalance;
  final double availableCreditBalance;
  final double availableCashbackValue;
  final double amountToUseCreditBalance;
  final double amountToUseCashbackBalance;
  final bool payOnlyCreditBalance;
  final bool buttonIsDisabled;
  final List<GiftCardsToExpire> giftCardsToExpire;
  final bool showExpireWarning;
  final Function() showCreditBottomSheet;
  final String? favoriteOption;
  final OrderForm? orderForm;
  final bool isLoading;
  final Function(List<CreditCardModel>? cards, String? favoriteCardNumber)
      showChooseFavoriteBottomSheet;
  final Function(PaymentType? payment, {CreditCardInfoData? card})
      updatePaymentSelected;
  final Function() showInstallmentsBottomSheet;
  final Function() onCreateNewCard;
  final Function() onFinishPayment;
  final SelectedPaymentType? selectedPaymentType;
  final bool isIos;
  final Widget? applePayButton;
  final void Function()? onTapBack;

  @override
  State<CdsPaymentTemplate> createState() => _CdsPaymentTemplateState();
}

class _CdsPaymentTemplateState extends State<CdsPaymentTemplate> {
  @override
  Widget build(BuildContext context) {
    final cards = widget.orderForm?.getAvailableAccounts
        .map((card) => card.toCreditCardModel(context: context))
        .toList();
    double totalPurchase = CurrencyHelper.currencyForDouble(
          widget.orderForm?.getTotalValueFormatted ?? 'R\$0,00',
        ) /
        100;
    final favoritedMethod = PaymentType.values
        .firstWhereOrNull((p) => p.value == widget.favoriteOption);
    CreditCardModel? cardFavorited =
        cards?.firstWhereOrNull((c) => c.cardId == widget.favoriteOption);
    final appleFavorited = widget.favoriteOption == '204';
    final selectedApplePay = widget.paymentSelected == PaymentType.applePay;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CdsTopBar(
          tokens: widget.tokens,
          config: CdsTopBarConfig(
            type: CdsTopBarType.large,
            title: "Pagamento",
            pinned: true,
            hasShadow: true,
            titleTextStyle: widget.tokens.typography.subtitleStyles.subtitle2,
            onBackButton: widget.onTapBack ??
                () {
                  Modular.to.pop();
                },
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: widget.tokens.layout.spacing.sm,
                ),
                widget.paymentWarning?.warningText == null
                    ? const SizedBox.shrink()
                    : Column(
                        children: [
                          CdsFeedback(
                            tokens: widget.tokens,
                            config: CdsFeedbackConfig(
                              text: widget.paymentWarning!.warningText ?? '',
                              type: FeedbackType.values.firstWhereOrNull((e) =>
                                      e.name ==
                                      widget.paymentWarning?.warningType
                                          ?.toLowerCase()) ??
                                  FeedbackType.neutral,
                              hasBorderRadius: false,
                              hasCloseIcon: false,
                              hasLeftIcon: false,
                            ),
                          ),
                          SizedBox(
                            height: widget.tokens.layout.spacing.sm,
                          ),
                        ],
                      ),
                if (widget.totalCreditBalance > 0) ...[
                  AvailableCreditInfo(
                    tokens: widget.tokens,
                    availableCheckingAccountCreditValue: CurrencyHelper.format(
                        amount: widget.availableCreditBalance,
                        dividedBy100: true),
                    availableCashbackCreditValue: CurrencyHelper.format(
                        amount: widget.availableCashbackValue),
                    title: 'Carteira',
                    buttonText: 'Usar saldo',
                    onPressed: widget.showCreditBottomSheet,
                    extraChip:
                        (widget.amountToUseCreditBalance < totalPurchase &&
                                widget.amountToUseCreditBalance > 0 &&
                                widget.paymentSelected == null)
                            ? CdsChipsHighlight(
                                variation: CdsChipsHihglightVariation.neutral,
                                title:
                                    'Escolha uma forma de pagamento para\ncompletar o total',
                                chipsSize: CdsChipsSize.large,
                                tokens: widget.tokens,
                              )
                            : null,
                    showCreditChip: widget.amountToUseCreditBalance > 0 ||
                        widget.showExpireWarning,
                    showCashbackChip: widget.amountToUseCashbackBalance > 0,
                    payOnlyBalance: widget.payOnlyCreditBalance,
                    cashbackChipTitle: '${CurrencyHelper.format(
                      amount: widget.amountToUseCashbackBalance,
                    )} de cashback aplicados',
                    creditChipVariation: widget.showExpireWarning
                        ? CdsChipsHihglightVariation.alert
                        : null,
                    creditChipTitle: widget.showExpireWarning
                        ? 'Atenção! Você tem saldo a expirar em ${DateHelper.dayNumber(
                            widget.giftCardsToExpire.first.expirationDate ?? '',
                          )} dias.'
                        : '${CurrencyHelper.format(
                            amount: widget.amountToUseCreditBalance,
                            dividedBy100: true,
                          )} de créditos aplicados',
                  ),
                  SizedBox(
                    height: widget.tokens.layout.spacing.sm,
                  ),
                ],
                CdsDivider(
                    dividerSize: CdsDividerSize.small,
                    tokens: widget.tokens,
                    isExpanded: true),
                SizedBox(
                  height: widget.tokens.layout.spacing.xl,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: widget.tokens.layout.padding.lg),
                      child: Column(
                        children: [
                          if (favoritedMethod == null &&
                              cardFavorited == null &&
                              appleFavorited == false &&
                              !widget.payOnlyCreditBalance)
                            CdsFeedback(
                              tokens: widget.tokens,
                              config: CdsFeedbackConfig(
                                text:
                                    'Defina uma forma de pagamento como favorita para agilizar suas próximas compras.',
                                title: 'Escolha seu pagamento favorito',
                                hasLeftIcon: false,
                                type: FeedbackType.alert,
                                hasBorderRadius: true,
                                customIcon: GestureDetector(
                                  onTap: () {
                                    if (!widget.isLoading) {
                                      widget.showChooseFavoriteBottomSheet(
                                          cards, cardFavorited?.cardNumber);
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      Text(
                                        'Definir',
                                        style: widget.tokens.typography
                                            .bodyStyles.caption,
                                      ),
                                      Icon(
                                        widget.tokens.icons.arrowRight,
                                        size: 16,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          if (favoritedMethod != null ||
                              cardFavorited != null ||
                              appleFavorited == true) ...[
                            CdsSectionHeading(
                              tokens: widget.tokens,
                              title: 'Pagamento favorito',
                              buttonText: 'Alterar favorito',
                              onTap: () {
                                widget.showChooseFavoriteBottomSheet(
                                    cards, cardFavorited?.cardNumber);
                              },
                            ),
                            if (favoritedMethod == PaymentType.pixInstallment)
                              ListItemRadio(
                                title: 'Pix parcelado',
                                leftIcon: Icon(
                                  widget.tokens.icons.pix,
                                ),
                                subtitle: 'Em até 4x sem juros',
                                onTap: () {
                                  widget.updatePaymentSelected(
                                      PaymentType.pixInstallment);
                                },
                                isChecked: widget.paymentSelected ==
                                        PaymentType.pixInstallment ||
                                    widget.paymentSelected == null,
                                tokens: widget.tokens,
                                isDisabled: widget.isLoading,
                              ),
                            if (favoritedMethod == PaymentType.pix)
                              ListItemRadio(
                                onTap: () {
                                  widget.updatePaymentSelected(PaymentType.pix);
                                },
                                title: '',
                                cumstomTitle: Row(
                                  children: [
                                    Text(
                                      'Pix',
                                      style: widget.tokens.typography.bodyStyles
                                          .paragraph,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                                leftIcon: Icon(
                                  widget.tokens.icons.pix,
                                ),
                                isChecked:
                                    widget.paymentSelected == PaymentType.pix ||
                                        widget.paymentSelected == null,
                                tokens: widget.tokens,
                                isDisabled: widget.isLoading,
                              ),
                            if (appleFavorited == true && widget.isIos)
                              ListItemRadio(
                                title: 'Apple pay',
                                leftIcon: Icon(
                                  widget.tokens.icons.applePay,
                                  size: 16,
                                ),
                                onTap: () {
                                  widget.updatePaymentSelected(
                                      PaymentType.applePay);
                                },
                                isChecked: widget.paymentSelected ==
                                    PaymentType.applePay,
                                tokens: widget.tokens,
                                isDisabled: widget.payOnlyCreditBalance ||
                                    widget.isLoading,
                              ),
                            if (cardFavorited != null)
                              CardFavorite(
                                title: cardFavorited.cardNickName,
                                subtitle: cardFavorited.cardInfo,
                                controller: widget.cvvController,
                                isDisabled: widget.isLoading ||
                                    widget.payOnlyCreditBalance,
                                selectedInstallmentForCard:
                                    widget.selectedInstallmentForCard,
                                onTap: () {
                                  final cardInfo =
                                      cardFavorited.toCreditCardInfoData();
                                  widget.updatePaymentSelected(
                                    PaymentType.creditCard,
                                    card: cardInfo,
                                  );
                                },
                                onTapInstallments: () {
                                  widget.showInstallmentsBottomSheet();
                                },
                                isChecked: widget.selectedCard?.cardAccountId ==
                                        cardFavorited.cardId &&
                                    (widget.paymentSelected == null ||
                                        widget.paymentSelected ==
                                            PaymentType.creditCard),
                                tokens: widget.tokens,
                                leftIcon: Image(
                                  image: cardFavorited.brandIcon,
                                  height: 20,
                                  width: 20,
                                ),
                              ),
                          ],
                        ],
                      ),
                    ),
                    SizedBox(
                      height: widget.tokens.layout.spacing.md,
                    ),
                    CdsDivider(
                        dividerSize: CdsDividerSize.small,
                        tokens: widget.tokens,
                        isExpanded: true),
                    SizedBox(
                      height: widget.tokens.layout.spacing.xl,
                    ),
                    Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: widget.tokens.layout.padding.lg),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              'Outras formas de pagamento',
                              style: widget
                                  .tokens.typography.bodyStyles.paragraph
                                  .copyWith(
                                      color: widget.tokens.colors.content.pure),
                            ),
                            SizedBox(
                              height: widget.tokens.layout.spacing.md,
                            ),
                            if (widget.favoriteOption !=
                                    PaymentType.pix.value &&
                                (widget.orderForm?.acceptsPixPayment ?? false))
                              ListItemRadio(
                                showMargin: false,
                                onTap: () {
                                  widget.updatePaymentSelected(
                                    PaymentType.pix,
                                  );
                                },
                                title: '',
                                cumstomTitle: Row(
                                  children: [
                                    Text(
                                      'Pix',
                                      style: widget.tokens.typography.bodyStyles
                                          .paragraph,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                                leftIcon: Icon(
                                  widget.tokens.icons.pix,
                                ),
                                isChecked:
                                    widget.paymentSelected == PaymentType.pix,
                                tokens: widget.tokens,
                                isDisabled: widget.payOnlyCreditBalance ||
                                    widget.isLoading,
                              ),
                            if (!widget.payOnlyCreditBalance)
                              _CreditCardSection(
                                cards: cards,
                                creditCardFlow: CreditCardFlow.selectCard,
                                onTapNewCard: () {
                                  widget.updatePaymentSelected(
                                    PaymentType.newCard,
                                  );
                                },
                                selectedCard: widget.selectedCard,
                                secondCardSelected: widget.secondCardSelected,
                                paymentSelected: widget.paymentSelected,
                                tokens: widget.tokens,
                                favoriteCardNumber: cardFavorited?.cardNumber,
                                selectedInstallmentForCard:
                                    widget.selectedInstallmentForCard,
                                onShowInstallments:
                                    widget.showInstallmentsBottomSheet,
                                cvvController: widget.cvvController,
                                isLoading: widget.isLoading,
                                onNewCreditCardSelected: (cardInfo) {
                                  if (widget.selectedCard?.cardNumber ==
                                      cardInfo?.cardNumber) {
                                    widget.updatePaymentSelected(
                                      null,
                                      card: null,
                                    );
                                  } else {
                                    widget.updatePaymentSelected(
                                      PaymentType.creditCard,
                                      card: cardInfo,
                                    );
                                  }
                                },
                              ),
                            if (widget.favoriteOption !=
                                    PaymentType.pixInstallment.value &&
                                (widget.orderForm?.acceptsPixInstallments ??
                                    false))
                              ListItemRadio(
                                title: 'Pix parcelado',
                                leftIcon: Icon(
                                  widget.tokens.icons.pix,
                                ),
                                subtitle: 'Em até 4x sem juros',
                                onTap: () {
                                  widget.updatePaymentSelected(
                                      PaymentType.pixInstallment);
                                },
                                isChecked: widget.paymentSelected ==
                                    PaymentType.pixInstallment,
                                tokens: widget.tokens,
                                isDisabled: widget.payOnlyCreditBalance ||
                                    widget.isLoading,
                              ),
                            if (appleFavorited == false && widget.isIos)
                              ListItemRadio(
                                title: 'Apple pay',
                                leftIcon: Icon(
                                  widget.tokens.icons.applePay,
                                  size: 16,
                                ),
                                onTap: () {
                                  widget.updatePaymentSelected(
                                      PaymentType.applePay);
                                },
                                isChecked: widget.paymentSelected ==
                                    PaymentType.applePay,
                                tokens: widget.tokens,
                                isDisabled: widget.payOnlyCreditBalance ||
                                    widget.isLoading,
                              ),
                            // TODO: Add Google pay
                            // if (false)
                            //   ListItemRadio(
                            //     title: 'Google pay',
                            //     leftIcon: Icon(
                            //       widget.tokens.icons.google,
                            //     ),
                            //     onTap: () {
                            //       widget.updatePaymentSelected(
                            //           PaymentType.googlePay);
                            //     },
                            //     isChecked: widget.paymentSelected ==
                            //         PaymentType.googlePay,
                            //     tokens: widget.tokens,
                            //     isDisabled: widget.payOnlyCreditBalance ||
                            //         widget.isLoading,
                            //   ),
                          ],
                        )),
                    if (!selectedApplePay) ...[
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: widget.tokens.layout.padding.lg),
                        child: CdsChipsHighlight(
                          variation: CdsChipsHihglightVariation.neutral,
                          title: 'Não se preocupe, você ainda não será cobrado',
                          chipsSize: CdsChipsSize.large,
                          tokens: widget.tokens,
                          isExpanded: true,
                        ),
                      ),
                      SizedBox(
                        height: widget.tokens.layout.spacing.ul,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
        Container(
          color: widget.tokens.colors.surface.surfaceCard,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: widget.tokens.layout.padding.lg),
            child: Column(
              children: [
                SizedBox(
                  height: widget.tokens.layout.spacing.md,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total do pedido',
                      style: widget.tokens.typography.bodyStyles.paragraph
                          .copyWith(color: widget.tokens.colors.content.pure),
                    ),
                    Text(
                      widget.orderForm?.totalFormatted ?? '',
                      style: widget.tokens.typography.bodyStyles.paragraph
                          .copyWith(color: widget.tokens.colors.content.pure),
                    ),
                  ],
                ),
                SizedBox(
                  height: widget.tokens.layout.spacing.md,
                ),
                selectedApplePay && widget.applePayButton != null
                    ? widget.applePayButton!
                    : CdsButton.primary(
                        tokens: widget.tokens,
                        buttonText: widget.paymentSelected != null ||
                                favoritedMethod != null ||
                                cardFavorited != null
                            ? selectedApplePay
                                ? "Prosseguir para o pagamento"
                                : 'Continuar para a revisão'
                            : 'Escolha uma opção',
                        size: CdsButtonSize.large,
                        onPressed: () {
                          if (widget.paymentSelected == PaymentType.newCard) {
                            widget.onCreateNewCard();
                          } else {
                            widget.onFinishPayment();
                          }
                        },
                        isDisabled: widget.buttonIsDisabled ||
                            widget.selectedPaymentType == null,
                        isLoading: selectedApplePay && widget.isLoading,
                        expanded: true,
                      ),
                SizedBox(
                  height: widget.tokens.layout.spacing.md,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _CreditCardSection extends StatelessWidget {
  const _CreditCardSection({
    required this.onTapNewCard,
    required this.selectedCard,
    required this.tokens,
    required this.paymentSelected,
    required this.onNewCreditCardSelected,
    required this.cards,
    required this.favoriteCardNumber,
    required this.onShowInstallments,
    required this.cvvController,
    required this.secondCardSelected,
    required this.isLoading,
    required this.selectedInstallmentForCard,
    required this.creditCardFlow,
  });

  final List<CreditCardModel>? cards;
  final CreditCardInfoData? selectedCard;
  final CreditCardInfoData? secondCardSelected;
  final CheckoutDStokens tokens;
  final Function() onTapNewCard;
  final PaymentType? paymentSelected;
  final Function(CreditCardInfoData?) onNewCreditCardSelected;
  final String? favoriteCardNumber;
  final VoidCallback onShowInstallments;
  final TextEditingController cvvController;
  final bool isLoading;
  final String? selectedInstallmentForCard;
  final CreditCardFlow creditCardFlow;

  @override
  Widget build(BuildContext context) {
    final hasPaymentAndCardSelected =
        paymentSelected == PaymentType.creditCard && selectedCard != null;

    return cards?.isEmpty ?? true
        ? ListItemRadio(
            title: 'Cartão de crédito',
            leftIcon: Icon(
              tokens.icons.creditCard,
            ),
            subtitle: 'Em até 10x sem juros',
            onTap: onTapNewCard,
            isChecked: paymentSelected == PaymentType.newCard,
            tokens: tokens,
            isDisabled: isLoading,
          )
        : CreditCardPaymentOption(
            cvvController: cvvController,
            onCreateCard: onTapNewCard,
            hasPaymentAndCardSelected: hasPaymentAndCardSelected,
            onNewCreditCardSelected: (cardInfo) {
              onNewCreditCardSelected(cardInfo);
            },
            selectedCard: selectedCard,
            secondCardSelected: secondCardSelected,
            onTapInstallments: onShowInstallments,
            tokens: tokens,
            isNewCreditCardSelected: false,
            title: 'Cartão de crédito',
            subtitle: 'Em até 10x sem juros',
            isLoading: isLoading,
            items: cards ?? [],
            icon: tokens.icons.creditCard,
            enableInsertCardData: true,
            favoriteCardNumber: favoriteCardNumber,
            selectedInstallmentForCard: selectedInstallmentForCard,
            creditCardFlow: creditCardFlow,
          );
  }
}
