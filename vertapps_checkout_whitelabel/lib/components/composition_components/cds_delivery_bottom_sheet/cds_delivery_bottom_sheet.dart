import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/cds_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/enum/cds_sheet_type.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_image_cards/models/cds_image_card_model.dart';

class CdsDeliveryBottomSheet extends CdsBaseStatefulComponent {
  final VoidCallback onClose;
  final List<CdsPackModel> packs;
  final void Function(bool, CdsShippingOptionModel) onShippingOptionSelected;
  final void Function(List<CdsPackModel>) onDeliveryConfirmed;
  final CdsShippingOptionModel? selectedShippingOption;
  final String shippingPrice;

  CdsDeliveryBottomSheet({
    required super.tokens,
    required this.packs,
    required this.onClose,
    required this.onShippingOptionSelected,
    required this.onDeliveryConfirmed,
    required this.shippingPrice,
    this.selectedShippingOption,
  });

  @override
  State<CdsDeliveryBottomSheet> createState() => _CdsDeliveryBottomSheetState();
}

class _CdsDeliveryBottomSheetState extends State<CdsDeliveryBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return CdsSheet(
      onClose: widget.onClose,
      tokens: widget.tokens,
      type: CdsSheetType.bottomSheet,
      title: 'Receber no endereço',
      subtitle:
          'Seu pedido foi divido em ${widget.packs.length} ${widget.packs.length == 1 ? 'pacote' : 'pacotes'}',
      slotContent: CdsPackWidget(
        tokens: widget.tokens,
        packs: widget.packs,
        onShippingOptionSelected: widget.onShippingOptionSelected,
        selectedShippingOption: widget.selectedShippingOption,
        blockInternalScroll: true,
      ),
      buttonText:
          'Confirmar valor do frete (${widget.packs.length}) - ${widget.shippingPrice}',
      onButtonPressed: () {
        widget.onDeliveryConfirmed(widget.packs);
      },
    );
  }
}

class CdsPackWidget extends CdsBaseStatefulComponent {
  final List<CdsPackModel> packs;
  final void Function(bool, CdsShippingOptionModel) onShippingOptionSelected;
  final CdsShippingOptionModel? selectedShippingOption;
  final bool blockInternalScroll;
  final bool fromPickup;

  const CdsPackWidget({
    super.key,
    required super.tokens,
    required this.packs,
    required this.onShippingOptionSelected,
    this.selectedShippingOption,
    this.blockInternalScroll = false,
    this.fromPickup = false,
  });

  @override
  State<CdsPackWidget> createState() => _CdsPackWidgetState();
}

class _CdsPackWidgetState extends State<CdsPackWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListView.separated(
          itemCount: widget.packs.length,
          shrinkWrap: true,
          physics: widget.blockInternalScroll
              ? const NeverScrollableScrollPhysics()
              : null,
          itemBuilder: (context, index) {
            final pack = widget.packs[index];
            final otherOptions = pack.shippingOptions;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pacote ${index + (widget.fromPickup ? 2 : 1)}',
                      style: widget.tokens.typography.bodyStyles.description
                          .copyWith(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      '${pack.imagesUrl.length} ${pack.imagesUrl.length == 1 ? 'produto' : 'produtos'}',
                      style: widget.tokens.typography.bodyStyles.caption
                          .copyWith(
                              color: widget.tokens.colors.content.content03),
                    ),
                  ],
                ),
                SizedBox(height: widget.tokens.layout.spacing.md),
                CdsImageCards(
                  tokens: widget.tokens,
                  imageCards: pack.imagesUrl
                      .map((item) => CdsImageCardModel(
                            imageUrl: item,
                          ))
                      .toList(),
                ),
                SizedBox(height: widget.tokens.layout.spacing.md),
                ListView.separated(
                  itemCount: otherOptions.length,
                  shrinkWrap: true,
                  itemBuilder: (context, shippingOptionIndex) {
                    final option = otherOptions[shippingOptionIndex];
                    final bool isFastest = option.id == pack.fastestOption.id;
                    final bool isCheapest = option.id == pack.cheapestOption.id;

                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        SizedBox(
                          width: widget.tokens.layout.spacing.xxxl,
                          child: Text(
                            option.total > 0
                                ? CurrencyUtils.format(amount: option.total)
                                : 'Grátis',
                            style: widget.tokens.typography.bodyStyles.caption
                                .copyWith(
                              color: widget.tokens.colors.content.content03,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        SizedBox(width: widget.tokens.layout.spacing.lg),
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        option.id,
                                        style: widget.tokens.typography
                                            .bodyStyles.description
                                            .copyWith(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14,
                                        ),
                                      ),
                                      if (isFastest &&
                                          otherOptions.length > 1) ...[
                                        SizedBox(
                                            width: widget
                                                .tokens.layout.spacing.sm),
                                        CdsChipsHighlight(
                                          tokens: widget.tokens,
                                          variation: CdsChipsHihglightVariation
                                              .success,
                                          title: 'Mais rápida',
                                          chipsSize: CdsChipsSize.small,
                                        ),
                                      ],
                                      if (isCheapest &&
                                          otherOptions.length > 2) ...[
                                        SizedBox(
                                            width: widget
                                                .tokens.layout.spacing.sm),
                                        CdsChipsHighlight(
                                          tokens: widget.tokens,
                                          variation: CdsChipsHihglightVariation
                                              .success,
                                          title: 'Mais barata',
                                          chipsSize: CdsChipsSize.small,
                                        ),
                                      ],
                                    ],
                                  ),
                                  SizedBox(
                                      height: widget.tokens.layout.spacing.sm),
                                  Text(
                                    '${option.getShippingEstimatedFormatted}',
                                    style: widget
                                        .tokens.typography.bodyStyles.caption
                                        .copyWith(
                                      color: widget
                                          .tokens.colors.content.content03,
                                    ),
                                  ),
                                ],
                              ),
                              CdsRadioButton(
                                tokens: widget.tokens,
                                value: option.id ==
                                    (widget.selectedShippingOption?.id ??
                                        pack.selectedOption?.id),
                                onChanged: (value) {
                                  widget.onShippingOptionSelected(
                                      value, option);
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) =>
                      SizedBox(height: widget.tokens.layout.spacing.lg),
                ),
              ],
            );
          },
          separatorBuilder: (context, index) =>
              SizedBox(height: widget.tokens.layout.spacing.lg),
        )
      ],
    );
  }
}
