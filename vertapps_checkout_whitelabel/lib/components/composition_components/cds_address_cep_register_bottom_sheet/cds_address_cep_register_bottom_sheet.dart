import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/core_components/cds_textfield/enum/cds_text_field_mask.dart';

class CdsAddressCepRegisterBottomSheet extends CdsBaseStatelessComponent {
  final TextEditingController cepTextEditingController;
  final VoidCallback onClose;
  final VoidCallback onBack;
  final bool isLoading;
  final bool isCepValid;

  CdsAddressCepRegisterBottomSheet({
    required super.tokens,
    required this.cepTextEditingController,
    required this.onClose,
    required this.onBack,
    required this.isLoading,
    required this.isCepValid,
  });
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CdsTopBar(
          tokens: tokens,
          config: CdsTopBarConfig(
            type: CdsTopBarType.small,
            pinned: true,
            onBackButton: onBack,
            padding: EdgeInsets.zero,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: tokens.layout.padding.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: tokens.layout.spacing.xl,
              ),
              Text(
                'Endereço de entrega',
                style: tokens.typography.subtitleStyles.subtitle2
                    .copyWith(color: tokens.colors.content.content01),
              ),
              SizedBox(
                height: tokens.layout.spacing.md,
              ),
              Text(
                'Insira os dados do endereço que você deseja receber seu pedido',
                style: tokens.typography.bodyStyles.description.copyWith(
                  color: tokens.colors.content.content03,
                ),
              ),
              SizedBox(
                height: tokens.layout.spacing.lg,
              ),
            ],
          ),
        ),
        CdsDivider(
          dividerSize: CdsDividerSize.small,
          tokens: tokens,
          isExpanded: true,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: tokens.layout.padding.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: tokens.layout.spacing.lg,
              ),
              CdsTextField(
                label: 'Digite seu CEP',
                controller: cepTextEditingController,
                tokens: tokens,
                mask: CdsInputMask.cep,
                isEnabled: !isCepValid,
                keyboardType: TextInputType.number,
              ),
              SizedBox(
                height: tokens.layout.spacing.xl,
              ),
              CdsPrimaryButton(
                size: CdsButtonSize.large,
                buttonText: 'Digite um CEP válido',
                tokens: tokens,
                isDisabled: !isCepValid && !isLoading,
                onPressed: () {},
                isLoading: isLoading,
                expanded: true,
              ),
              SizedBox(
                height: tokens.layout.spacing.sm,
              ),
            ],
          ),
        )
      ],
    );
  }
}
