import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/register_addres_cep_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/register_address_bottom_sheet.dart';

enum _AddressFlow {
  registerCep,
  completeAddress,
}

class AddressCepPage extends CdsBaseStatefulTemplate {
  const AddressCepPage({
    super.key,
    required super.tokens,
    required this.args,
  });

  final CdsAddressCepArgs? args;

  @override
  State<AddressCepPage> createState() => _AddressCepPageState();
}

class _AddressCepPageState extends State<AddressCepPage> {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  _AddressFlow _addressFlow = _AddressFlow.registerCep;
  QueryCep _address = QueryCep();

  void _onSaveAddressCallback() {
    if (widget.args?.overrideFinishFlow != null) {
      widget.args?.overrideFinishFlow?.call();
    } else {
      Modular.to.popAndPushNamed(
        '/checkout/payment',
        arguments: widget.args?.paymentArgs,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: BlocBuilder<OrderFormCubit, OrderFormState>(
            bloc: _orderFormCubit,
            builder: (context, state) {
              if (_addressFlow == _AddressFlow.registerCep) {
                return RegisterAddresCepBottomSheet(
                  templateType: TemplateType.page,
                  tokens: widget.tokens,
                  onBack: () {
                    Modular.to.pop();
                  },
                  onCepValid: (cep) {
                    setState(() {
                      _address = cep;
                      _addressFlow = _AddressFlow.completeAddress;
                    });
                  },
                );
              }
              if (_addressFlow == _AddressFlow.completeAddress) {
                return RegisterAddressBottomSheet(
                    templateType: TemplateType.page,
                    tokens: widget.tokens,
                    onBack: () {
                      setState(() {
                        _addressFlow = _AddressFlow.registerCep;
                      });
                    },
                    address: _address,
                    onSaveAddressCallback: _onSaveAddressCallback);
              }
              return SizedBox.shrink();
            }),
      ),
    );
  }
}
