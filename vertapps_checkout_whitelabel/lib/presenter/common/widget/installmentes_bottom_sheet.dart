import 'package:flutter/material.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/select_installments_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class InstallmentesBottomSheet extends StatefulWidget {
  final CheckoutDStokens tokens;
  final VoidCallback? onSuccessSelectInstallment;

  const InstallmentesBottomSheet({
    super.key,
    required this.tokens,
    this.onSuccessSelectInstallment,
  });

  @override
  State<InstallmentesBottomSheet> createState() =>
      _InstallmentesBottomSheetState();
}

class _InstallmentesBottomSheetState extends State<InstallmentesBottomSheet> {
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _paymentCubit = Modular.get<PaymentCubit>();
  CreditCardInfoData? get selectedCard => _paymentCubit.getCardSelected();

  InstallmentOrderForm? get selectedInstallmentForCard =>
      _paymentCubit.state.creditCardInfo.selectedInstallment;

  List<InstallmentOrderForm> availableInstallments = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getAvailableInstallments();
    });
  }

  void _getAvailableInstallments() {
    final paymentSytemId = selectedCard?.selectedPaymentSystemId ??
        _paymentCubit.state.creditCardInfo.selectedPaymentSystemId;

    if (paymentSytemId == null) {
      throw Exception(
          "Nenhum paymentSystemId foi identificado para obter as parcelas");
    }

    final availableInstallments =
        _paymentCubit.getPaymentInstallmentsForCreditCard(paymentSytemId);

    setState(() {
      this.availableInstallments = availableInstallments;
    });
  }

  InstallmentOrderForm? _getInstallment(
      InstallmentInfoParams installmentParams) {
    final selectedInstallment = availableInstallments.firstWhereOrNull(
      (element) =>
          element.count == installmentParams.count &&
          element.value == installmentParams.value,
    );

    return selectedInstallment;
  }

  Future<void> _selectInstallment(
      InstallmentInfoParams installmentParams) async {
    try {
      CdsBackdrop.show(context, widget.tokens);

      final installment = _getInstallment(installmentParams);
      final newCard = _paymentCubit.state.creditCardInfo;

      if (installment != null && selectedCard != null) {
        final card = selectedCard!.copyWith(selectedInstallment: installment);
        await _paymentCubit.selectCreditCardPayment(
          creditCardInfo: card,
          installment: installment,
        );
        widget.onSuccessSelectInstallment?.call();
      }

      if (installment != null && newCard.isNew == true) {
        final card = newCard.copyWith(selectedInstallment: installment);
        await _paymentCubit.selectCreditCardPayment(
          creditCardInfo: card,
          installment: installment,
        );
      }
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          tokens: widget.tokens,
          text: 'Erro ao selecionar parcela',
          type: SnackbarType.error,
        );
      }
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderFormCubit, OrderFormState>(
      bloc: _orderFormCubit,
      builder: (context, state) {
        return StatefulBuilder(builder: (context, setState) {
          return SelectInstallmentsBottomSheet(
            tokens: widget.tokens,
            onCloseTap: () {
              Modular.to.pop();
            },
            titleStyle: widget.tokens.typography.subtitleStyles.subtitle2,
            showDragBar: true,
            borderRadius: BorderRadius.all(
              Radius.circular(widget.tokens.layout.spacing.md),
            ),
            installmentsSubtitleTextStyle:
                widget.tokens.typography.bodyStyles.description,
            isDisabled: selectedInstallmentForCard == null,
            onSelectInstallment: _selectInstallment,
            onButtonPressed: () {
              Modular.to.pop();
            },
            installments: availableInstallments
                .map(
                  (installment) => installment.toCheckoutInstallmentInfoParams(
                      isChecked: selectedCard?.selectedInstallment?.count ==
                              installment.count ||
                          _paymentCubit.state.creditCardInfo.selectedInstallment
                                  ?.count ==
                              installment.count),
                )
                .toList(),
          );
        });
      },
    );
  }
}
