import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/templates.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_biometric/widget/ask_biometric_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/order_placed/transition/order_placed_transition_page.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/payment_method_option.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:in_app_review/in_app_review.dart';
import 'dart:async';

class OrderPlacedPage extends StatefulWidget {
  final BrandEnum brand;
  final CdsOrderPlacedArgs? args;

  const OrderPlacedPage({
    super.key,
    required this.brand,
    required this.args,
  });

  @override
  State<OrderPlacedPage> createState() => _OrderPlacedPageState();
}

class _OrderPlacedPageState extends State<OrderPlacedPage> {
  final _ordersCubit = Modular.get<OrdersListCubit>();
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final paymentCubit = Modular.get<PaymentCubit>();
  final checkingAccountCubit = Modular.get<CheckingAccountCubit>();
  final appConfig = Modular.get<AppConfig>();
  bool loadingInit = false;
  late final orderFrom = orderFormCubit.state.orderForm;
  late final orderFormCopy = OrderForm.fromJson(orderFrom?.toJson() ?? {});
  Payment? selectedCard;
  PaymentType? paymentSelected;
  String? favoritePayment;
  bool hasFavoriteAddress = false;
  bool? showFavoriteAddressPaymentBottomSheet;
  final _eventDispatcher = Modular.get<EventDispatcher>();
  String availableCheckingAccountValue = 'R\$ 0,00';
  final _authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() async {
    try {
      setState(() {
        loadingInit = true;
      });
      await NavigationEvents.logPageView(local: 'checkout_order_completed');

      await _ordersCubit.getOrders(
        orderGroup: widget.args?.orderId ?? '',
        getStatus: false,
        finishOrder: () async {
          final currentOrder = _ordersCubit.state.activeOrders.firstOrNull;
          final transactionId = currentOrder?.orderGroup ?? '';
          await _eventDispatcher.logPurchase(transactionId: transactionId);
          setState(() {
            paymentSelected = orderFormCopy.paymentType;
            selectedCard = currentOrder?.paymentCreditCard;
          });
          await orderFormCubit.clearAndCreateOrderForm();
          paymentCubit.clearCreditCardInfo();
        },
      );

      await getFinancialOverview();
      await _getFavoritePayment();
      scheduleMicrotask(requestInAppReview);
      if (_authCubit.state.isBiometricAvailable &&
          _authCubit.state.biometricsToken == null) {
        _showActiveBiometricBottomSheet();
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() {
        loadingInit = false;
      });
    }
  }

  Future<void> _getFavoriteAddress() async {
    final address = orderFormCubit.orderForm.shippingData?.address;
    try {
      final favoriteOption = await orderFormCubit
          .getIsAddressOptionFavorite(address?.addressId ?? '');

      setState(() {
        hasFavoriteAddress = favoriteOption;
      });
    } catch (e) {
      setState(() {
        hasFavoriteAddress = false;
      });
    }
  }

  Future<void> _getFavoritePayment() async {
    await _getFavoriteAddress();
    final response = await orderFormCubit.getFavoritePaymentOption();
    final showFavoritePayment =
        await orderFormCubit.showFavoriteAddressPaymentBottomSheet();

    setState(() {
      favoritePayment = response;
      showFavoriteAddressPaymentBottomSheet =
          (favoritePayment == null || hasFavoriteAddress == false) &&
              showFavoritePayment;
    });
  }

  PaymentMethodOption _getPaymentMethod() {
    switch (paymentSelected) {
      case PaymentType.creditCard:
        return CreditCardInfoParams(
          cardFriendlyName: selectedCard?.lastDigits,
          finalDigits: selectedCard?.creditCardText ?? '',
          isFavorite: favoritePayment == selectedCard?.accountId,
          totalValue: orderFormCopy.totalFormatted,
          expirationDate: '',
          cvv: paymentCubit.state.creditCardInfo.cvv,
          paymentSystemName: selectedCard?.paymentSystemName ?? '',
        );
      case PaymentType.pix:
      case PaymentType.pixInstallment:
        return PixInfoParams(
          totalValue: orderFormCopy.totalFormatted,
          isFavorite: favoritePayment == paymentSelected?.value,
          isPixInstallments: paymentSelected == PaymentType.pixInstallment,
        );

      case PaymentType.applePay:
        return ApplePayInfoParams(
          totalValue: orderFormCopy.totalFormatted,
          isFavorite:
              appConfig.applePayConfig?.paymentSystemId == favoritePayment,
        );
      case PaymentType.giftCard:
        return GiftCardInfoParams(
          isFavorite: false,
          totalValue: orderFormCopy.getTotalValueFormatted,
        );
      default:
        return PixInfoParams(
          totalValue: orderFormCopy.totalFormatted,
          isFavorite: favoritePayment == paymentSelected?.value,
          isPixInstallments: paymentSelected == PaymentType.pixInstallment,
        );
    }
  }

  Future<void> getFinancialOverview() async {
    final financialOverview =
        await checkingAccountCubit.getCheckingAccountFinancialOverviews();
    setState(() {
      availableCheckingAccountValue =
          CurrencyHelper.format(amount: financialOverview.amount / 100);
    });
  }

  void requestInAppReview() async {
    final inAppReview = InAppReview.instance;
    if (await inAppReview.isAvailable()) await inAppReview.requestReview();
  }

  void backHome() {
    Modular.to.popUntil((route) {
      return route.settings.name == '/';
    });
  }

  List<StatusOrder> getDefaultStatus() {
    return [
      StatusOrder(
        id: "ORDER_CONFIRMATION",
        description: "Pedido confirmado",
        date: DateTime.now().toIso8601String(),
      ),
      StatusOrder(id: "PAYMENT_ACCEPT", description: "Pagamento aprovado"),
      StatusOrder(id: "IN_HANDLING", description: "Preparando pedido"),
      StatusOrder(id: "INVOICED_ORDER", description: "Nota fiscal disponível"),
      StatusOrder(
        id: "RECEIVED_BY_CARRIER",
        description: "Entregue para a transportadora",
      ),
      StatusOrder(
        id: "ORDER_OUT_FOR_DELIVERY",
        description: "Pedido saiu para entrega",
      ),
      StatusOrder(id: "ORDER_DELIVERED", description: "Entregue"),
    ];
  }

  void _showActiveBiometricBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return AskBiometricBottomSheet(
          tokens: widget.brand.tokens,
          cubit: _authCubit,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrdersListCubit, OrdersListState>(
      bloc: _ordersCubit,
      builder: (context, state) {
        return loadingInit
            ? OrderPlacedTransitionPage(
                tokens: widget.brand.tokens,
              )
            : Scaffold(
                body: SafeArea(
                  child: CdsOrderPlacedTemplate(
                    onTapViewBag: () {
                      widget.args?.onTapViewBag.call();
                    },
                    tokens: widget.brand.tokens,
                    orderId: widget.args?.orderId ?? '',
                    getCouponApplied: orderFormCopy.getCouponApplied(),
                    selectedCard: selectedCard,
                    onTapViewBalance: () {
                      widget.args?.onTapViewBalance.call();
                    },
                    orderFormCopy: orderFormCopy,
                    userInfo: state.userInfo,
                    isLoading: state.isLoading,
                    backHome: backHome,
                    availableCheckingAccountValue:
                        availableCheckingAccountValue,
                    getPaymentMethod: _getPaymentMethod(),
                    showFavoriteAddressPaymentBottomSheet:
                        showFavoriteAddressPaymentBottomSheet,
                    getSalesPersonNumberCodeApplied:
                        orderFormCopy.getSalesPersonCodeApplied(),
                    activeOrders: state.activeOrders,
                  ),
                ),
              );
      },
    );
  }
}
