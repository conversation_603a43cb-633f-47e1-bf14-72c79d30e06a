import 'dart:io';

import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/login_config.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_biometric/widget/ask_biometric_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/widgets/cds_login_background.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class LoginPage extends CdsBaseStatefulTemplate {
  const LoginPage({
    super.key,
    required super.tokens,
    this.type,
    this.alignment,
    this.overrideFinishFlow,
  });

  final LoginTemplateAlignment? alignment;
  final LoginTemplateType? type;
  final Function()? overrideFinishFlow;

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _loginSocialCubit = Modular.get<LoginSocialCubit>();
  final _authCubit = Modular.get<AuthCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<LoginSocialCubit, LoginSocialState>(
        bloc: _loginSocialCubit,
        builder: (context, state) {
          return CdsLoginBackground(
            type: widget.type ?? LoginTemplateType.fullImage,
            child: (inverse) => CdsLoginTemplate(
              config: LoginTemplateConfig(
                isInverse: inverse,
                alignment: widget.alignment ?? LoginTemplateAlignment.middle,
                type: widget.type ?? LoginTemplateType.fullImage,
                onPressSignUp: () {
                  Modular.to.pushNamed(
                    '/checkout/login_email',
                    arguments: {
                      "register": true,
                      "overrideFinishFlow": widget.overrideFinishFlow,
                    },
                  );
                },
                title: 'Acesse sua conta',
                subtitle:
                    'Entre de forma rápida e segura usando uma das opções abaixo.',
                socialLoginFirstButtonOnPressed: _handleSignInGoogle,
                socialLoginFirstButtonText: 'Entrar com o Google',
                socialLoginSecondButtonOnPressed: _handleSignInApple,
                socialLoginSecondButtonText: 'Entrar com Apple ID',
                socialLoginFirstButtonIcon: widget.tokens.icons.google!,
                socialLoginSecondButtonIcon: widget.tokens.icons.apple!,
                signInFirstButtonText: 'Entrar apenas com email',
                isApple: Platform.isIOS,
                signInFirstButtonOnPressed: () {
                  Modular.to.pushNamed(
                    '/checkout/login_email',
                    arguments: {
                      "overrideFinishFlow": widget.overrideFinishFlow,
                    },
                  );
                },
                signInSecondButtonText: 'Entrar com email e senha',
                signInSecondButtonOnPressed: () {
                  Modular.to.pushNamed(
                    '/checkout/login_email_password',
                    arguments: {
                      "overrideFinishFlow": widget.overrideFinishFlow,
                    },
                  );
                },
                onBackButton: () {
                  Modular.to.pop();
                },
                isSocialLoginLoading: state.isLoading,
              ),
              tokens: widget.tokens,
            ),
          );
        },
      ),
    );
  }

  Future<void> _handleSignInApple() async {
    try {
      final response = await _loginSocialCubit.handleSignInApple();

      if (response != null) {
        final appleUser = FirebaseAuth.instance.currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: appleUser?.email ?? '',
          loginType: LoginType.apple,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          _showSnackBar(
              context: context, message: 'Login efetuado com sucesso');

          if (widget.overrideFinishFlow != null) {
            widget.overrideFinishFlow!();
            return;
          }

          if (_authCubit.state.hasBiometrics) {
            Modular.to.navigate('/');
            return;
          }

          _showBottomSheet(_authCubit, '/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
      print(error);
    }
  }

  Future<void> _handleSignInGoogle() async {
    try {
      final response = await _loginSocialCubit.handleSignInGoogle();

      if (response != null) {
        final googleUser = GoogleSignIn().currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: googleUser?.email ?? '',
          loginType: LoginType.google,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          _showSnackBar(
              context: context, message: 'Login efetuado com sucesso');

          if (widget.overrideFinishFlow != null) {
            widget.overrideFinishFlow!();
            return;
          }

          if (_authCubit.state.hasBiometrics) {
            Modular.to.navigate('/');
            return;
          }

          _showBottomSheet(_authCubit, '/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
    }
  }

  void _showSnackBar({required BuildContext context, required String message}) {
    CdsSnackBar.show(
      context: context,
      tokens: widget.tokens,
      text: message,
      type: SnackbarType.success,
      onPressButton: () {
        CdsSnackBar.hideSnackBar();
      },
    );
  }

  void _showBottomSheet(AuthCubit _authCubit, String? route) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: false,
      builder: (BuildContext context) {
        return AskBiometricBottomSheet(
            tokens: widget.tokens, cubit: _authCubit, route: route);
      },
    );
  }
}
