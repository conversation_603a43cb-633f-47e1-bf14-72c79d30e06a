import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_email_template.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class LoginEmailPage extends CdsBaseStatefulTemplate {
  final bool? createPassword;
  final bool? isRegister;
  final Function()? overrideFinishFlow;
  final String privacyPolicyLink;
  final String? email;

  const LoginEmailPage({
    super.key,
    required super.tokens,
    this.createPassword,
    this.isRegister,
    this.overrideFinishFlow,
    required this.privacyPolicyLink,
    this.email,
  });

  @override
  State<LoginEmailPage> createState() => _LoginEmailPageState();
}

class _LoginEmailPageState extends State<LoginEmailPage> {
  TextEditingController emailController = TextEditingController();
  final loginEmailCubit = Modular.get<LoginEmailCubit>();
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    emailController = TextEditingController(text: widget.email);
    if (emailController.text.isNotEmpty) {
      loginEmailCubit.validateEmail(emailController.text);
    }
    NavigationEvents.logPageView(local: 'login_email');
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    emailController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: BlocBuilder<LoginEmailCubit, LoginEmailState>(
            bloc: loginEmailCubit,
            builder: (context, state) {
              return CdsLoginEmailTemplate(
                tokens: widget.tokens,
                controller: emailController,
                onChanged: (text) {
                  loginEmailCubit.validateEmail(text);
                },
                onPressedButton: () async {
                  _logEvents();
                  await _onTapContinue(
                    loginEmailCubit: loginEmailCubit,
                    authCubit: authCubit,
                  );
                },
                onBackButton: () {
                  Modular.to.pop();
                },
                onTapPrivacyPolicyLink: _openPrivacyPolicy,
                isDisabled: !state.isEmailValid,
                isLoading: state.isLoading,
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _logEvents() async {
    await AccountEvents.logEmailVerification(local: 'login_email', action: "continuar");

    if (widget.createPassword == true) {
      AccountEvents.logSignUpStep(local: 'login_email', step: "e-mail");
      Modular.to.pushNamed(
        '/account/create_password',
        arguments: {
          'email': emailController.text,
          "overrideFinishFlow": widget.overrideFinishFlow,
        },
      );
      return;
    }
  }

  Future<void> _onTapContinue({
    required LoginEmailCubit loginEmailCubit,
    required AuthCubit authCubit,
  }) async {
    if (loginEmailCubit.state.isLoading) return;

    try {
      final result = await loginEmailCubit.sendLogin(emailController.text);
      if (result == null) return;

      _goToLoginCode(token: result, isRegister: widget.isRegister ?? false);
    } catch (e) {
      if (!mounted) {
        return;
      }

      CdsSnackBar.show(
        context: context,
        text: "Erro ao enviar email",
        type: SnackbarType.error,
        tokens: widget.tokens,
        onPressButton: () {
          CdsSnackBar.hideSnackBar();
        },
      );
    }
  }

  void _goToLoginCode({
    required String token,
    required bool isRegister,
  }) {
    Modular.to.pushNamed(
      '/checkout/login_code',
      arguments: {
        'email': emailController.text,
        'token': token,
        'isRegister': isRegister,
        'overrideFinishFlow': widget.overrideFinishFlow,
      },
    );
  }

  void _openPrivacyPolicy() {
    Modular.to.pushNamed(
      '/webview',
      arguments: WebViewParams(
        url: widget.privacyPolicyLink,
      ),
    );
  }
}
