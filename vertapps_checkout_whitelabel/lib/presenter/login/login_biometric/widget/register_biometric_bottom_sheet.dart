import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/cds_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/enum/cds_sheet_type.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class RegisterBiometricBottomSheet extends CdsBaseStatefulComponent {
  final AuthCubit cubit;
  final String? route;

  const RegisterBiometricBottomSheet({
    super.key,
    required super.tokens,
    required this.cubit,
    this.route,
  });

  @override
  State<RegisterBiometricBottomSheet> createState() =>
      _RegisterBiometricBottomSheetState();
}

class _RegisterBiometricBottomSheetState
    extends State<RegisterBiometricBottomSheet> {
  @override
  void initState() {
    super.initState();
    _activateBiometrics();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      bloc: widget.cubit,
      builder: (context, state) {
        return CdsSheet(
          tokens: widget.tokens,
          type: CdsSheetType.bottomSheet,
          title:
              state.biometricsToken.isNullOrEmpty ? '' : 'Biometria cadastrada',
          subtitle: state.biometricsToken.isNullOrEmpty
              ? null
              : 'Você pode desabilitar a biometria a qualquer momento na área de perfil',
          subtitleStyle:
              widget.tokens.typography.bodyStyles.description.copyWith(
            color: widget.tokens.colors.content.content03,
          ),
          onClose: () => widget.route != null
              ? Modular.to.navigate(widget.route!)
              : Modular.to.pop(),
          slotContent: state.biometricsToken.isNullOrEmpty
              ? Padding(
                  padding:
                      EdgeInsets.only(bottom: widget.tokens.layout.spacing.xl),
                  child: CdsSpinner(tokens: widget.tokens),
                )
              : Column(
                  children: [
                    SizedBox(height: widget.tokens.layout.spacing.lg),
                    CdsButton.primary(
                      onPressed: () => widget.route != null
                          ? Modular.to.navigate(widget.route!)
                          : Modular.to.pop(),
                      expanded: true,
                      tokens: widget.tokens,
                      size: CdsButtonSize.large,
                      buttonText: 'Continuar',
                    ),
                  ],
                ),
        );
      },
    );
  }

  Future<void> _activateBiometrics() async {
    await widget.cubit.activateBiometrics();
  }
}
