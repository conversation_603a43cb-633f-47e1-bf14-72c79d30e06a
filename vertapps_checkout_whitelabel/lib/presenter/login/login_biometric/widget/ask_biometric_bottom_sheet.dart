import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/cds_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/enum/cds_sheet_type.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_biometric/widget/register_biometric_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class AskBiometricBottomSheet extends CdsBaseStatefulComponent {
  final AuthCubit cubit;
  final String? route;

  const AskBiometricBottomSheet({
    super.key,
    required super.tokens,
    required this.cubit,
    this.route,
  });

  @override
  State<AskBiometricBottomSheet> createState() =>
      _AskBiometricBottomSheetState();
}

class _AskBiometricBottomSheetState extends State<AskBiometricBottomSheet> {
  void _showBottomSheet(String? route) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (BuildContext context) {
        return RegisterBiometricBottomSheet(
          tokens: widget.tokens,
          cubit: widget.cubit,
          route: route,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return CdsSheet(
      tokens: widget.tokens,
      type: CdsSheetType.bottomSheet,
      title: 'Deseja facilitar seus próximos acessos?',
      subtitle: 'Habilite o acesso à biometria para acessar sua conta.',
      subtitleStyle: widget.tokens.typography.bodyStyles.description.copyWith(
        color: widget.tokens.colors.content.content03,
      ),
      onClose: () => widget.route != null
          ? Modular.to.navigate(widget.route!)
          : Modular.to.pop(),
      slotContent: Column(
        children: [
          SizedBox(height: widget.tokens.layout.spacing.sm),
          Image.asset(
            'assets/fingerprint.png',
            package: 'vertapps_checkout_whitelabel',
          ),
          SizedBox(height: widget.tokens.layout.spacing.xl),
          Row(
            children: [
              Expanded(
                child: CdsButton.secondary(
                  onPressed: () => widget.route != null
                      ? Modular.to.navigate(widget.route!)
                      : Modular.to.pop(),
                  tokens: widget.tokens,
                  size: CdsButtonSize.large,
                  buttonText: 'Agora não',
                ),
              ),
              SizedBox(width: widget.tokens.layout.spacing.sm),
              Expanded(
                child: CdsButton.primary(
                  onPressed: () {
                    Modular.to.pop();
                    _showBottomSheet(widget.route);
                  },
                  tokens: widget.tokens,
                  size: CdsButtonSize.large,
                  buttonText: 'Habilitar biometria',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
