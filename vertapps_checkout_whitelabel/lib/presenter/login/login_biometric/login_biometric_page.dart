import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_biometric_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/login_biometric_config.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/widgets/cds_login_background.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class LoginBiometricPage extends CdsBaseStatefulTemplate {
  const LoginBiometricPage({
    super.key,
    required super.tokens,
    this.type,
    this.alignment,
    this.overrideFinishFlow,
  });

  final LoginTemplateAlignment? alignment;
  final LoginTemplateType? type;
  final Function()? overrideFinishFlow;

  @override
  State<LoginBiometricPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginBiometricPage> {
  final _loginSocialCubit = Modular.get<LoginSocialCubit>();
  final _authCubit = Modular.get<AuthCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<LoginSocialCubit, LoginSocialState>(
        bloc: _loginSocialCubit,
        builder: (context, state) {
          return BlocBuilder<AuthCubit, AuthState>(
            bloc: _authCubit,
            builder: (context, authState) {
              return CdsLoginBackground(
                type: widget.type ?? LoginTemplateType.fullImage,
                child: (inverse) => CdsLoginBiometricTemplate(
                  config: LoginBiometricConfig(
                    isInverse: inverse,
                    alignment:
                        widget.alignment ?? LoginTemplateAlignment.middle,
                    type: widget.type ?? LoginTemplateType.fullImage,
                    onPressSignUp: () async {
                      await _authCubit.deactivateBiometrics();
                      Modular.to.pushNamed(
                        '/checkout/login',
                        arguments: {
                          "overrideFinishFlow": widget.overrideFinishFlow,
                        },
                      );
                    },
                    title: 'Vamos criar seu pedido!',
                    subtitle: 'Faça login para completar sua compra',
                    username: authState.userName ?? '',
                    socialLoginFirstButtonOnPressed: _handleSignInGoogle,
                    socialLoginFirstButtonText: 'Entrar com o Google',
                    socialLoginSecondButtonOnPressed: _handleSignInApple,
                    socialLoginSecondButtonText: 'Entrar com Apple ID',
                    socialLoginFirstButtonIcon: widget.tokens.icons.google!,
                    socialLoginSecondButtonIcon: widget.tokens.icons.apple!,
                    signInFirstButtonText: 'Entrar na conta',
                    signInFirstButtonOnPressed: _biometricLogin,
                    onBackButton: () {
                      Modular.to.pop();
                    },
                    isSocialLoginLoading: state.isLoading,
                    isBiometricLoading: authState.isLoading,
                  ),
                  tokens: widget.tokens,
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<void> _biometricLogin() async {
    try {
      final emailFromBiometrics = _authCubit.state.biometricsTokenEmail;
      final biometricResponse = await _authCubit.loginWithBiometrics();
      if (biometricResponse != null) {
        await _authCubit.loginSuccess(
          tokenResponse: biometricResponse,
          userEmail: emailFromBiometrics,
          loginType: LoginType.biometrics,
        );
        if (_authCubit.state.isLoggedIn) {
          _showSnackBar(
            context: context,
            message: 'Login efetuado com sucesso',
          );
          //TODO: Go to checkout
          return Modular.to.navigate('/');
        }
      }
    } catch (e) {}
  }

  Future<void> _handleSignInApple() async {
    try {
      final response = await _loginSocialCubit.handleSignInApple();

      if (response != null) {
        final appleUser = FirebaseAuth.instance.currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: appleUser?.email ?? '',
          loginType: LoginType.apple,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          _showSnackBar(
              context: context, message: 'Login efetuado com sucesso');
          Modular.to.navigate('/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
      print(error);
    }
  }

  Future<void> _handleSignInGoogle() async {
    try {
      final response = await _loginSocialCubit.handleSignInGoogle();

      if (response != null) {
        final googleUser = GoogleSignIn().currentUser;
        await _authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: googleUser?.email ?? '',
          loginType: LoginType.google,
        );
        _eventDispatcher.logLogin();
        if (_authCubit.state.isLoggedIn && mounted) {
          _showSnackBar(
              context: context, message: 'Login efetuado com sucesso');
          Modular.to.navigate('/');
        }
      }
      _loginSocialCubit.setIsLoading(false);
    } catch (error) {
      _loginSocialCubit.setIsLoading(false);
    }
  }

  void _showSnackBar({required BuildContext context, required String message}) {
    CdsSnackBar.show(
      context: context,
      tokens: widget.tokens,
      text: message,
      type: SnackbarType.success,
      onPressButton: () {
        CdsSnackBar.hideSnackBar();
      },
    );
  }
}
