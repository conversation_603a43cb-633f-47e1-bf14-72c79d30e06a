import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_pickup_bottom_sheet/model/cds_pickup_order_view.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/show_checkout_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/templates.dart';
import 'package:vertapps_checkout_whitelabel/presenter/common/widget/installmentes_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/order_review/widget/go_to_backpack_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/payment/widgets/apple_pay_advanced_component.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/coupon_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/seller_code_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/widgets/error_payment_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/payment_method_option.dart';
import '../../components/components.dart';
import 'package:azzas_core/repositories/checkout/models/apple_pay/apple_pay_model.dart';

class OrderReviewPage extends StatefulWidget {
  final BrandEnum brand;
  final CdsOrderReviewArgs? args;
  const OrderReviewPage({
    super.key,
    required this.brand,
    required this.args,
  });

  @override
  State<OrderReviewPage> createState() => _OrderReviewPageState();
}

class _OrderReviewPageState extends State<OrderReviewPage>
    with TickerProviderStateMixin {
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final backPageCubit = Modular.get<BagPageCubit>();
  final paymentCubit = Modular.get<PaymentCubit>();
  final checkingAccountCubit = Modular.get<CheckingAccountCubit>();
  final _orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final appConfig = Modular.get<AppConfig>();
  final cvvController = TextEditingController();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  String? sellerCodeApplied;
  double totalCreditBalance = 0;
  double totalCashbackBalance = 0;
  double availableCreditBalance = 0;
  double availableCashbackValue = 0;
  double amountToUseCreditBalance = 0;
  double amountToUseCashbackBalance = 0;
  bool showExpireWarning = false;
  bool isButtonLoading = false;
  List<GiftCardsToExpire> giftCardsToExpire = [];
  ApplePayModel? applePay;
  bool buttonIsDisabled = false;
  String? favoritePayment;
  bool payOnlyCreditBalance = false;
  GiftCardCollection? checkingAccount;
  PaymentMethodOption? paymentMethodOption;
  List<CdsPackModel> packages = [];

  PaymentType? get paymentSelected =>
      orderFormCubit.state.orderForm?.paymentType;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      getFinancialOverview();
      cvvController.addListener(updateCvv);

      sellerCodeApplied = orderFormCubit.getSalesPersonCodeApplied();
      payOnlyCreditBalance = paymentCubit.state.selectedPaymentType ==
          SelectedPaymentType.giftCard;

      updateCardSelected();
      loadPackages();
    });
  }

  void updateCardSelected() async {
    final card = paymentCubit.getCardSelected();

    if (card != null) {
      cvvController.text = card.cvv ?? '';
      await resetInstallmentsCardPayment();
    }

    getPaymentMethodOption();
  }

  Future<void> resetInstallmentsCardPayment() async {
    try {
      final selectedCard = paymentCubit.getCardSelected();
      if (paymentSelected == PaymentType.creditCard && selectedCard != null) {
        if (selectedCard.selectedInstallment?.count == 0) {
          final paymentSytemId = selectedCard.selectedPaymentSystemId;

          if (paymentSytemId == null) {
            throw Exception(
                "Nenhum paymentSystemId foi identificado para obter as parcelas");
          }
          final installment = paymentCubit
              .getPaymentInstallmentsForCreditCard(
                paymentSytemId,
              )
              .first;
          CdsBackdrop.show(context, widget.brand.tokens);
          await paymentCubit.selectCreditCardPayment(
            creditCardInfo: selectedCard,
            installment: installment,
          );
        }
      }
    } catch (e) {
      debugPrint('Error resetting installments card payment: $e');
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao resetar parcelas do cartão de crédito",
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
        );
      }
    } finally {
      CdsBackdrop.hide();
    }
  }

  void startApplePay() async {
    applePay = await _paymentApplePay();
  }

  void clearApplePay() {
    if (paymentSelected == PaymentType.applePay && applePay != null) {
      paymentCubit.handleApplePayUserCancel(applePay?.paymentId ?? '');
      setState(() {
        applePay = null;
      });
    }
  }

  Future<void> _getFavoritePayment() async {
    final response = await orderFormCubit.getFavoritePaymentOption();
    setState(() {
      favoritePayment = response;
    });
  }

  void updateCvv() {
    final selectedCard = paymentCubit.getCardSelected();

    if (selectedCard != null) {
      setState(() {
        buttonIsDisabled = cvvController.text.length < 3 ||
            selectedCard.selectedInstallment == null;
      });
      paymentCubit
          .setCreditCardInfo(selectedCard.copyWith(cvv: cvvController.text));
    }
  }

  void _showSellerCodeBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return SellerCodeBottomSheet(
              tokens: widget.brand.tokens,
              onRemoveSellerCode: _removeSellerCode,
              onAddSellerCode: () async {
                await resetInstallmentsCardPayment();
              });
        },
      );

      clearApplePay();
    });
  }

  Future<void> _removeCoupon() async {
    try {
      await _orderCheckoutHandler.removeCoupon();
      await resetInstallmentsCardPayment();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Cupom removido com sucesso",
          type: SnackbarType.success,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
        clearApplePay();
      }
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao remover cupom",
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  Future<void> _removeSellerCode() async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      await _orderCheckoutHandler.removeSellerCode();
      await resetInstallmentsCardPayment();
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Código de vendedora removido com sucesso",
          type: SnackbarType.success,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
        clearApplePay();
      }
    } catch (e) {
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao remover código de vendedora",
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  void _showBottomSheetAddCoupon() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return CouponBottomSheet(
              tokens: widget.brand.tokens,
              onCouponRemoved: _removeCoupon,
              onCouponAdded: () async {
                await resetInstallmentsCardPayment();
              });
        },
      );

      clearApplePay();
    });
  }

  void redirectToPayment() {
    Modular.to.popAndPushNamed(
      '/checkout/payment',
      arguments: CdsPaymentArgs(
        goToBackPack: (bool? showAddressCard) {
          widget.args?.goToBackPack.call(showAddressCard);
        },
        onTapViewBalance: () {
          widget.args?.onTapViewBalance.call();
        },
      ),
    );
    if (paymentSelected == PaymentType.applePay) {
      paymentCubit.handleApplePayUserCancel(applePay?.paymentId ?? '');
    }
  }

  void getFinancialOverview() async {
    final financialOverview =
        await checkingAccountCubit.getCheckingAccountFinancialOverviews();
    final expirationDetails = await checkingAccountCubit.getExpirationDetails();
    final checkingAccountResponse =
        checkingAccountCubit.getOrderFormCheckingAccount<GiftCardCollection>();
    setState(() {
      totalCreditBalance = financialOverview.amount.toDouble();
      availableCreditBalance = totalCreditBalance;
      totalCashbackBalance = 0;
      availableCashbackValue = totalCashbackBalance;
      giftCardsToExpire = expirationDetails.balances;
      checkingAccount = checkingAccountResponse;
      if (giftCardsToExpire.isNotEmpty) {
        showExpireWarning = true;
      }
      if (checkingAccountResponse?.items.any((e) => (e.value ?? 0) > 0) ==
          true) {
        amountToUseCreditBalance = orderFormCubit.orderForm.getGiftCardValues();
        availableCreditBalance = totalCreditBalance - amountToUseCreditBalance;
        showExpireWarning = false;
      }
    });
    await _getFavoritePayment();
  }

  Future<void> _pixTransaction({
    required OrderForm orderForm,
    required String transactionId,
    required String merchantName,
    required String orderGroup,
  }) async {
    final pixTransactionUseCase = Modular.get<PixTransactionUseCase>();
    if (orderForm.firstSelectedPayment == null) return;

    final payment = orderForm.firstSelectedPayment!;

    final transaction = TransactionPaymentTransaction(
      id: transactionId,
      merchantName: merchantName,
    );

    final pix = TransactionPaymentPix(
      paymentSystem: payment.paymentSystem,
      value: payment.value,
      paymentSystemName: payment.paymentSystem,
      referenceValue: payment.referenceValue,
      installmentValue: payment.merchantSellerPayments.first.installmentValue,
      id: payment.merchantSellerPayments.first.id,
      transaction: transaction,
    );
    var giftCards = paymentCubit.getGiftcardsTransaction(transaction);

    final params = TransactionPayment(pix: pix, giftCard: giftCards);

    await pixTransactionUseCase.call(
      transactionId: transactionId,
      transactionPaymentSelect: params,
      orderId: orderGroup,
    );
  }

  Future<GenerateCodePix> _generateCodePixSecondCall({
    required OrderForm orderForm,
    required String orderGroup,
  }) async {
    final generateCodePixSecondCallUseCase =
        Modular.get<GenerateCodePixSecondCallUseCase>();

    return await generateCodePixSecondCallUseCase.call(
      orderGroupId: orderGroup,
    );
  }

  void _showErrorPaymentBottomSheet(PaymentErrorType paymentErrorType) async {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return ErrorPaymentBottomSheet(
          paymentErrorType: paymentErrorType,
          tokens: widget.brand.tokens,
        );
      },
    );
  }

  void _goToOrderPlaced(String orderId) {
    Modular.to.pushNamed(
      "/checkout/order-placed",
      arguments: CdsOrderPlacedArgs(
        onTapViewBag: () {
          widget.args?.goToBackPack.call(true);
        },
        onTapViewBalance: () {
          widget.args?.onTapViewBalance.call();
        },
        orderId: orderId,
      ),
    );
  }

  Future<void> _finishPurchase() async {
    setState(() {
      isButtonLoading = true;
    });

    try {
      await orderFormCubit.updateAnalyticsData();
    } catch (e) {
      debugPrint('Error updating analytics data: $e');
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao atualizar dados de analytics",
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }

    if (orderFormCubit.state.orderForm?.orderFormId == null ||
        orderFormCubit.state.orderForm?.paymentData == null) {
      setState(() {
        isButtonLoading = false;
      });
      return;
    }

    paymentCubit.clearCreditCardError();
    try {
      await _eventDispatcher.logOrderReviewed();
    } catch (e) {
      debugPrint('Error logging orderReviewed: $e');
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao emitir evento de order reviewed",
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
    if (paymentCubit.isPixSelected || paymentCubit.isPixInstallmentsSelected) {
      await finishPixPayment();
    } else if (paymentCubit.isApplePaySelected) {
      startApplePay();
    } else if (paymentCubit.isGiftCardSelected && payOnlyCreditBalance) {
      await finishGiftCardPayment();
    } else {
      await finishCreditCardPayment();
    }
    setState(() {
      isButtonLoading = false;
    });
  }

  Future<void> finishGiftCardPayment() async {
    final paymentTransactionUseCase = Modular.get<PaymentTransactionUseCase>();
    try {
      final recaptchaResult =
          await paymentCubit.getRecaptchaChecker('gift_card_transaction');

      var giftCards = paymentCubit.getGiftcardsTransaction(null);
      final params = TransactionPayment(giftCard: giftCards);

      var response = await paymentTransactionUseCase(
        orderFormId: orderFormCubit.orderForm.orderFormId!,
        transactionPaymentSelect: [params],
        recaptchaKey: recaptchaResult.key,
        recaptchaToken: recaptchaResult.token,
      );

      if (response.isNotEmpty) {
        final orderGroup = response.first;

        _goToOrderPlaced(orderGroup);
      }
    } on TooManyOrderRequestsException {
      CdsSnackBar.show(
        context: context,
        text: "Você precisa esperar antes de fazer outro pedido",
        type: SnackbarType.error,
        tokens: widget.brand.tokens,
      );
    } catch (e) {
      if (mounted) {
        _showErrorPaymentBottomSheet(PaymentErrorType.giftCard);
      }
      debugPrint(e.toString());
    }
  }

  Future<CreateOrder> _createOrder({required OrderForm orderForm}) async {
    final createOrderUseCase = Modular.get<CreateOrderUseCase>();
    if (orderForm.orderFormId == null) throw Exception();

    try {
      final recaptchaResult =
          await paymentCubit.getRecaptchaChecker('pix_transaction');
      var response = await createOrderUseCase(
        orderFormId: orderForm.orderFormId!,
        value: int.parse(orderForm.value!.toString()),
        interestValue: 0,
        recaptchaKey: recaptchaResult.key,
        recaptchaToken: recaptchaResult.token,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> finishPixPayment() async {
    String? pixUrl;
    String? orderGroup;

    try {
      final response = await _createOrder(orderForm: orderFormCubit.orderForm);
      final transactionId = response.merchantTransactions!.first.transactionId!;
      final merchantName = response.merchantTransactions!.first.merchantName!;
      orderGroup = response.orderGroup!;

      await _pixTransaction(
        orderForm: orderFormCubit.orderForm,
        orderGroup: orderGroup,
        transactionId: transactionId,
        merchantName: merchantName,
      );

      final token = await _generateCodePixSecondCall(
        orderForm: orderFormCubit.orderForm,
        orderGroup: orderGroup,
      );

      final url = token.paymentAuthorizationAppCollection!.first.appPayload;
      final isPixInstallments =
          orderFormCubit.orderForm.firstSelectedPayment?.paymentSystem ==
              PaymentSystemIds.pixInstallments;

      if (isPixInstallments && url != null) {
        pixUrl = '$url&cd=/cancel';
      } else {
        pixUrl = url ?? '';
      }
    } catch (e) {
      if (mounted) {
        String message = "Erro ao finalizar pagamento";
        if (e is TooManyOrderRequestsException) {
          message = "Você precisa esperar antes de fazer outro pedido";
        }
        CdsSnackBar.show(
          context: context,
          text: message,
          type: SnackbarType.error,
          tokens: widget.brand.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
      debugPrint(e.toString());
    }

    if (pixUrl != null) _openPixWebView(url: pixUrl, orderGroup: orderGroup);
  }

  void _openPixWebView({required String url, required String? orderGroup}) {
    NavigatorDynamic.call(
      "webview",
      arguments: WebViewParams(
        url: url,
        showNavBar: false,
        avoidKeyboard: false,
        onUpdateVisitedHistory: (controller, uri, _) async {
          if (uri?.path == "/pix-finish") {
            await orderFormCubit.updateFavoritePaymentOption('pix');

            Future.delayed(const Duration(milliseconds: 500), () {
              controller.goBack();
              _goToOrderPlaced(orderGroup ?? '');
            });
          }
          if (uri?.path == "/pix-fail" ||
              uri?.path == "/cancel" ||
              uri?.path == '/cancel/pix') {
            Future.delayed(const Duration(milliseconds: 500), () {
              controller.goBack();
              if (mounted) {
                Navigator.pop(context);
                _showErrorPaymentBottomSheet(PaymentErrorType.pix);
              }
            });
          }
        },
      ),
    );
  }

  Future<void> finishCreditCardPayment() async {
    final paymentTransactionUseCase = Modular.get<PaymentTransactionUseCase>();

    final recaptchaResult =
        await paymentCubit.getRecaptchaChecker('credit_card_transaction');

    List<TransactionPayment> params = [];
    late final TransactionPaymentCreditCardFields fields;
    final payment = orderFormCubit.orderForm.firstSelectedPayment;
    var giftCards = paymentCubit.getGiftcardsTransaction(null);

    if (giftCards.isNotEmpty) {
      params.add(TransactionPayment(giftCard: giftCards));
    }

    for (final merchant in orderFormCubit.orderForm.merchantSellerPayments) {
      TransactionPaymentCreditCard? creditCard;
      if (payment != null) {
        if (paymentCubit.state.creditCardInfo.isNew == true) {
          fields = TransactionPaymentCreditCardFields(
            cardNumber: paymentCubit.state.creditCardInfo.cardNumber,
            holderName: paymentCubit.state.creditCardInfo.cardHolderName,
            validationCode: paymentCubit.state.creditCardInfo.cvv,
            dueDate: paymentCubit.state.creditCardInfo.expirationDate,
            address: backPageCubit.state.currentAddress,
            addressId: backPageCubit.state.currentAddress?.addressId,
            document: paymentCubit.state.creditCardInfo.cardHolderDocument,
          );
        } else {
          fields = TransactionPaymentCreditCardFields(
            bin: paymentCubit.state.creditCardInfo.bin,
            accountId: paymentCubit.state.creditCardInfo.cardAccountId,
            addressId: backPageCubit.state.currentAddress?.addressId,
            validationCode: paymentCubit.state.creditCardInfo.cvv,
          );
        }
        creditCard = TransactionPaymentCreditCard(
          accountId: paymentCubit.state.creditCardInfo.cardAccountId,
          bin: paymentCubit.state.creditCardInfo.bin,
          paymentSystem: payment.paymentSystem,
          value: merchant.value,
          referenceValue: merchant.referenceValue,
          interestValue: 0,
          hasDefaultBillingAddress: true,
          installmentsInterestRate: 0,
          fields: fields,
          isBillingAddressDifferent: false,
          installments: merchant.installments == 0 ? 1 : merchant.installments,
          interestRate: 0,
          installmentValue: merchant.installmentValue,
          chooseToUseNewCard: paymentCubit.state.creditCardInfo.isNew,
          currencyCode: 'BRL',
          originalPaymentIndex: 0,
          groupName: 'creditCardPaymentGroup',
        );
      }
      params.add(TransactionPayment(creditCard: creditCard));
    }

    try {
      var response = await paymentTransactionUseCase(
        orderFormId: orderFormCubit.orderForm.orderFormId!,
        transactionPaymentSelect: params,
        recaptchaKey: recaptchaResult.key,
        recaptchaToken: recaptchaResult.token,
      );
      if (paymentCubit.state.creditCardInfo.saveAsFavoriteCard) {
        await orderFormCubit.updateFavoritePaymentOption(response.last);
      }
      if (response.isNotEmpty) {
        final orderGroup = response.first;

        _goToOrderPlaced(orderGroup);
      }

      return;
    } on CreditCardValidationException {
      paymentCubit.setCreditCardError(paymentCubit.state.creditCardInfo);
      _showErrorPaymentBottomSheet(PaymentErrorType.notAuthorizedCreditCard);
    } on TooManyOrderRequestsException {
      CdsSnackBar.show(
        context: context,
        text: "Você precisa esperar antes de fazer outro pedido",
        type: SnackbarType.error,
        tokens: widget.brand.tokens,
      );
    } catch (e) {
      debugPrint(e.toString());
      _showErrorPaymentBottomSheet(PaymentErrorType.notAuthorizedCreditCard);
    }
  }

  void _goToBackPack({bool? showBottomSheet}) {
    if (showBottomSheet == false) {
      widget.args?.goToBackPack.call(true);
      return;
    }
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return GoToBackPackBottomSheet(
            tokens: widget.brand.tokens,
            onPressed: () {
              Navigator.pop(context);
              widget.args?.goToBackPack.call(false);
              clearApplePay();
            },
          );
        },
      );
    });
  }

  void getPaymentMethodOption() {
    final orderForm = orderFormCubit.state.orderForm;
    setState(() {
      switch (paymentSelected) {
        case PaymentType.pix:
          paymentMethodOption = PixInfoParams(
            totalValue: orderForm?.totalFormatted ?? '',
            isPixInstallments: paymentSelected == PaymentType.pixInstallment,
            isFavorite:
                PaymentType.pix.value == orderFormCubit.state.favoriteOption,
          );
          break;
        case PaymentType.creditCard:
          try {
            final cardFromOrderForm = paymentCubit.getCardSelected();

            final cardSelected = orderFormCubit
                .state.orderForm?.getAvailableAccounts
                .firstWhereOrNull(
                    (c) => c.accountId == cardFromOrderForm?.cardAccountId);

            if (cardFromOrderForm != null) {
              paymentMethodOption = CreditCardInfoParams(
                cardFriendlyName: 'Cartão de crédito',
                finalDigits: cardFromOrderForm.finalNumber,
                expirationDate: cardFromOrderForm.expirationDate ?? '',
                totalValue: orderForm?.totalFormatted ?? '',
                isFavorite: (cardFromOrderForm.saveAsFavoriteCard) ||
                    orderFormCubit.state.favoriteOption ==
                        cardFromOrderForm.cardAccountId,
                cvv: paymentCubit.state.creditCardInfo.cvv,
                paymentSystemName: cardSelected?.paymentSystemName ?? '',
              );

              return;
            }

            final newCard = paymentCubit.state.creditCardInfo;

            if (newCard.isNew == true) {
              paymentMethodOption = CreditCardInfoParams(
                cardFriendlyName: 'Cartão de crédito',
                finalDigits: newCard.finalNumber,
                expirationDate: newCard.expirationDate ?? '',
                totalValue: orderForm?.totalFormatted ?? '',
                isFavorite: newCard.saveAsFavoriteCard,
                cvv: newCard.cvv ?? '',
                paymentSystemName: newCard.selectedPaymentSystemName ?? '',
              );

              return;
            }
          } catch (_) {}
          break;
        case PaymentType.applePay:
          paymentMethodOption = ApplePayInfoParams(
            isFavorite: appConfig.applePayConfig?.paymentSystemId ==
                orderFormCubit.state.favoriteOption,
            totalValue: orderForm?.totalFormatted ?? '',
          );
          break;
        case PaymentType.giftCard:
          paymentMethodOption = GiftCardInfoParams(
            isFavorite: false,
            totalValue: orderForm?.totalFormatted ?? '',
          );
          break;
        default:
          paymentMethodOption = PixInfoParams(
            totalValue: orderForm?.totalFormatted ?? '',
            isPixInstallments: paymentSelected == PaymentType.pixInstallment,
            isFavorite: PaymentType.pix.value == favoritePayment,
          );
          break;
      }
    });
  }

  void _showInstallmentsBottomSheet() async {
    return showCheckoutBottomSheet(
      context: context,
      builder: (_) {
        return InstallmentesBottomSheet(
          tokens: widget.brand.tokens,
        );
      },
      vsync: this,
    );
  }

  Future<ApplePayModel?> _paymentApplePay() async {
    try {
      return await paymentCubit.applePayTransaction(
        context,
      );
    } catch (e) {
      final String message;
      if (e is PaymentErrorResponse && e.error is RecaptchaException) {
        final cause = e.error as RecaptchaException;
        message = cause.userFriendlyMessage;
      } else if ((e.toString().contains('CHK0328'))) {
        message = "Aguarde 2 minutos para tentar novamente";
      } else {
        message = 'ocorreu um erro inesperado tente novamente mais tarde';
      }
      CdsSnackBar.show(
        context: context,
        tokens: widget.brand.tokens,
        text: message,
        type: SnackbarType.error,
      );
      return null;
    }
  }

  List<CdsShippingOptionModel> shippingOptions(PackageModel package) {
    return package.deliveryOptions
        .map((op) => CdsShippingOptionModel(
              deliveryChannel: op.deliveryChannel ?? '',
              id: op.id ?? '',
              itemIndex: package.deliveryOptions.indexOf(op),
              pickupStoreInfo: null,
              selectedDeliveryChannel: op.deliveryChannel ?? '',
              selectedSla: op.id ?? '',
              shippingEstimate: int.parse(op.shippingEstimate ?? ''),
              total: (op.price ?? 0) / 100,
            ))
        .toList();
  }

  void loadPackages() {
    final orderFormState = orderFormCubit.state;
    final bagPageState = backPageCubit.state;
    setState(() {
      packages = orderFormState.packages?.map(
            (package) {
              final packageItemIds = package.items.toSet();
              final products = (orderFormState.orderForm?.getItems ?? [])
                  .where((element) => packageItemIds.contains(element.id))
                  .toList();
              return CdsPackModel.create(
                products: products
                    .map((e) => CdsProductShortModel.fromOrderFormProduct(e))
                    .toList(),
                hasSelectedOption: bagPageState.selectedDeliveryOption == null,
                imagesUrl: package.imagesUrl,
                shippingOptions: shippingOptions(package),
              );
            },
          ).toList() ??
          [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
        bloc: backPageCubit,
        builder: (context, bagPageState) {
          return BlocBuilder<OrderFormCubit, OrderFormState>(
            bloc: orderFormCubit,
            builder: (context, orderFormState) {
              return BlocBuilder<PaymentCubit, PaymentState>(
                bloc: paymentCubit,
                builder: (context, paymentState) {
                  final selectedApplePay =
                      paymentSelected == PaymentType.applePay;
                  return Scaffold(
                    floatingActionButtonLocation:
                        FloatingActionButtonLocation.centerFloat,
                    floatingActionButton: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: widget.brand.tokens.layout.padding.lg,
                      ),
                      child: selectedApplePay && applePay != null
                          ? ApplePayAdvancedComponent(
                              paymentCubit: paymentCubit,
                              applePay: applePay,
                              args: CdsPaymentArgs(
                                goToBackPack: (bool? showAddressCard) {
                                  widget.args?.goToBackPack
                                      .call(showAddressCard);
                                },
                                onTapViewBalance: () {
                                  widget.args?.onTapViewBalance.call();
                                },
                              ),
                              paymentResultCancel: () {
                                setState(() {
                                  applePay = null;
                                });
                              },
                            )
                          : CdsPrimaryButton(
                              onPressed: _finishPurchase,
                              size: CdsButtonSize.large,
                              tokens: widget.brand.tokens,
                              buttonText: selectedApplePay
                                  ? 'Prosseguir para o pagamento'
                                  : 'Fechar compra com ${paymentSelected?.getFormattedName()}',
                              expanded: true,
                              isLoading: orderFormState.isLoading ||
                                  paymentState.isLoading ||
                                  isButtonLoading,
                              isDisabled: buttonIsDisabled &&
                                  widget.args?.redirectToPayment == true,
                            ),
                    ),
                    body: SafeArea(
                      child: SingleChildScrollView(
                        child: CdsOrderReviewTemplate(
                          tokens: widget.brand.tokens,
                          onTapBack: () {
                            clearApplePay();
                            Navigator.of(context).pop();
                          },
                          payOnlyCreditBalance: payOnlyCreditBalance,
                          goToBackPack: _goToBackPack,
                          packages: packages,
                          orderForm: orderFormState.orderForm,
                          paymentSelected: paymentSelected,
                          selectedInstallmentForCard:
                              orderFormCubit.orderForm.getCurrentInstallment,
                          showInstallmentsBottomSheet:
                              _showInstallmentsBottomSheet,
                          amountToUseCreditBalance: amountToUseCreditBalance,
                          showCreditBottomSheet: () {
                            if (widget.args?.redirectToPayment ?? false) {
                              redirectToPayment();
                            } else {
                              Navigator.of(context).pop();
                            }
                          },
                          getSalesPersonCodeApplied:
                              orderFormCubit.getSalesPersonCodeApplied(),
                          paymentMethodOption: paymentMethodOption,
                          isLoading: orderFormState.isLoading ||
                              paymentState.isLoading,
                          isButtonLoading: isButtonLoading,
                          isButtonDisabled: buttonIsDisabled &&
                              widget.args?.redirectToPayment == true,
                          finishPurchase: _finishPurchase,
                          removeSellerCode: _removeSellerCode,
                          totalCreditBalance: totalCreditBalance,
                          showSellerCodeBottomSheet: _showSellerCodeBottomSheet,
                          removeCoupon: _removeCoupon,
                          showBottomSheetAddCoupon: _showBottomSheetAddCoupon,
                          cvvController: cvvController,
                          onTapRedirectToPayment: redirectToPayment,
                          onTapViewBalance: () {
                            widget.args?.onTapViewBalance.call();
                          },
                          redirectToPayment:
                              widget.args?.redirectToPayment ?? false,
                          pickupPackage: getPickupPackage(),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          );
        });
  }

  CdsPickupOrderView? getPickupPackage() {
    final storeSelected = backPageCubit.getSelectedCdsStore();
    if (storeSelected == null) return null;

    final storeDetail =
        backPageCubit.getAllPickupOptionsReduce().firstWhereOrNull((store) {
      return store.storeName == storeSelected.storeName;
    });
    if (storeDetail == null) return null;

    return CdsPickupOrderView(
      storeName: storeSelected.storeName,
      addressText: storeSelected.address,
      products: storeDetail.orderFormProducts.map((e) {
        return CdsProductShortModel.fromOrderFormProduct(e);
      }).toList(),
      estimateText: storeDetail.getEstimateFullString(),
    );
  }
}
