import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/app_commons/cubits/warnme/warnme_exp.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/model/cds_warn_me_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';

class WarnmeBottomSheet extends StatefulWidget {
  final CheckoutDStokens tokens;
  final OrderFormItem? initialItem;
  final Product product;
  final void Function() onClose;
  final void Function() onBack;

  const WarnmeBottomSheet({
    Key? key,
    required this.tokens,
    this.initialItem,
    required this.product,
    required this.onClose,
    required this.onBack,
  }) : super(key: key);

  @override
  _WarnmeBottomSheetState createState() => _WarnmeBottomSheetState();
}

class _WarnmeBottomSheetState extends State<WarnmeBottomSheet> {
  final warnMeCubit = Modular.get<WarnMeCubit>();
  final authCubit = Modular.get<AuthCubit>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final eventDispatcher = Modular.get<EventDispatcher>();
  OrderFormItem? _selectedItem;
  bool _isFormValid = false;
  List<CdsSelectorButton> sizeOptions = [];

  @override
  void initState() {
    super.initState();
    nameController.addListener(_validateForm);
    emailController.addListener(_validateForm);

    final initialEmail = authCubit.state.localUserInfo?.personEmail ?? '';
    final initialName = authCubit.state.localUserInfo?.personName ?? '';

    emailController.text = initialEmail;
    nameController.text = initialName;

    if (widget.initialItem != null) {
      _selectItem(widget.initialItem!);
    }
  }

  @override
  void dispose() {
    nameController.removeListener(_validateForm);
    emailController.removeListener(_validateForm);
    nameController.dispose();
    emailController.dispose();
    super.dispose();
  }

  void _validateForm() {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    final isEmailValid = emailRegex.hasMatch(emailController.text);
    final isNameValid = nameController.text.length >= 3;

    setState(() {
      _isFormValid = isEmailValid && isNameValid;
    });
  }

  void _selectItem(OrderFormItem item) {
    setState(() {
      _selectedItem = item;
    });
    _validateForm();
  }

  Future<void> onWarnMe() async {
    FocusScope.of(context).unfocus();
    await warnMeCubit.warnMe(
      name: nameController.text,
      email: emailController.text,
      skuId: _selectedItem?.itemId ?? '',
    );

    eventDispatcher.logNotifyMe(
      size: _selectedItem?.itemSize,
      itemRef: widget.product.productReferenceCode,
      lineItems: _selectedItem?.itemId,
    );

    if (warnMeCubit.state.success) {
      Modular.to.pop();
      CdsSnackBar.show(
        context: context,
        text: "Aviso de disponibilidade enviado com sucesso",
        type: SnackbarType.success,
        tokens: widget.tokens,
        onPressButton: () {
          CdsSnackBar.hideSnackBar();
        },
      );
    }

    if (warnMeCubit.state.error) {
      CdsSnackBar.show(
        context: context,
        text: "Erro ao enviar aviso de disponibilidade",
        type: SnackbarType.error,
        tokens: widget.tokens,
        onPressButton: () {
          CdsSnackBar.hideSnackBar();
        },
      );
    }
  }

  /// Monta a lista de tamanhos indísponíveis
  /// Filtra para evitar tamanhos repetidos
  List<CdsSelectorButton>? getValidSizeOptions() {
    final allUnavailable = widget.product.items
        ?.where((e) => !e.isAvailable)
        .map((e) => CdsSelectorButton(
              tokens: widget.tokens,
              buttonText: e.itemSize,
              onPressed: () => _selectItem(e),
              isDisabled: false,
              isSelected: _selectedItem?.itemSize == e.itemSize,
            ))
        .toList();

    if (allUnavailable == null) return null;

    final seenKeys = <String>{};
    return allUnavailable.where((btn) {
      final key = btn.buttonText;
      if (seenKeys.contains(key)) return false;
      seenKeys.add(key);
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    sizeOptions = getValidSizeOptions() ?? [];
    return BlocBuilder<WarnMeCubit, WarnMeState>(
      bloc: warnMeCubit,
      builder: (context, state) {
        return CdsWarnMeBottomSheet(
            tokens: widget.tokens,
            config: CdsWarnMeBottomSheetConfig(
              nameController: nameController,
              emailController: emailController,
              isButtonDisabled: !_isFormValid,
              onPressedButton: onWarnMe,
              onClose: widget.onClose,
              onBack: widget.onBack,
              isButtonLoading: state.loading,
              hasSelectedSize: _selectedItem != null,
              sizes: sizeOptions,
            ));
      },
    );
  }
}
