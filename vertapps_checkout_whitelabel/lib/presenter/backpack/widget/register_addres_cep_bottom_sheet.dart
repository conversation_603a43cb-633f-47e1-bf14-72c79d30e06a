import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/address/cds_address_cep_template.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';

enum TemplateType {
  page,
  bottomSheet,
}

class RegisterAddresCepBottomSheet extends StatefulWidget {
  final CheckoutDStokens tokens;
  final VoidCallback onBack;
  final void Function(QueryCep) onCepValid;
  final TemplateType templateType;

  const RegisterAddresCepBottomSheet({
    super.key,
    required this.tokens,
    required this.onBack,
    required this.onCepValid,
    required this.templateType,
  });

  @override
  State<RegisterAddresCepBottomSheet> createState() =>
      _RegisterAddresCepBottomSheetState();
}

class _RegisterAddresCepBottomSheetState
    extends State<RegisterAddresCepBottomSheet> with TickerProviderStateMixin {
  final _bagPageCubit = Modular.get<BagPageCubit>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final cepTextEditingController = TextEditingController();
  bool isCepValid = false;
  bool isLoading = false;
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    cepTextEditingController.clear();
    cepTextEditingController.addListener(() {
      _onTextChanged();
    });
  }

  @override
  void dispose() {
    cepTextEditingController.dispose();
    super.dispose();
  }

  void logSearchZipcode(){
    _eventDispatcher.logSearchZipCode(
        _bagPageCubit.state.currentAddress?.postalCode ?? cepTextEditingController.text,
        false,
        'checkout',
        _bagPageCubit.state.packages);
  }

  Future<void> _onTextChanged() async {
    final cep = cepTextEditingController.text;
    final isValid = TextHelper.isCEP(cep);

    if (!mounted || !isValid) {
      if (mounted) {
        setState(() {
          isCepValid = false;
          isLoading = false;
        });
      }
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final address = await Modular.get<QueryCepUseCase>().call(cep: cep);

      if (!mounted) return;

      setState(() {
        isCepValid = true;
        isLoading = false;
      });

      logSearchZipcode();

      widget.onCepValid(address);
      FocusManager.instance.primaryFocus?.unfocus();
    } catch (_) {
      if (!mounted) return;

      CdsSnackBar.show(
        context: context,
        tokens: widget.tokens,
        text: 'CEP inválido ou não encontrado.',
        type: SnackbarType.error,
      );

      setState(() {
        isCepValid = false;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
      bloc: _bagPageCubit,
      builder: (context, bagPageState) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, orderFormState) {
            if (widget.templateType == TemplateType.bottomSheet) {
              return CdsAddressCepRegisterBottomSheet(
                tokens: widget.tokens,
                cepTextEditingController: cepTextEditingController,
                onClose: () => Modular.to.pop(),
                onBack: widget.onBack,
                isLoading: isLoading,
                isCepValid: isCepValid,
              );
            }

            if (widget.templateType == TemplateType.page) {
              return CdsAddressCepTemplate(
                tokens: widget.tokens,
                cepTextEditingController: cepTextEditingController,
                onClose: () => Modular.to.pop(),
                onBack: widget.onBack,
                isLoading: isLoading,
                isCepValid: isCepValid,
              );
            }

            return SizedBox.shrink();
          },
        );
      },
    );
  }
}
