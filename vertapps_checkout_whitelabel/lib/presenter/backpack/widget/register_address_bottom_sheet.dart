import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/address/cds_address_complete_template.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/register_addres_cep_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';

class RegisterAddressBottomSheet extends StatefulWidget {
  final CheckoutDStokens tokens;
  final VoidCallback onBack;
  final QueryCep address;
  final VoidCallback? onSaveAddressCallback;
  final TemplateType templateType;
  const RegisterAddressBottomSheet({
    super.key,
    required this.tokens,
    required this.onBack,
    required this.address,
    this.onSaveAddressCallback,
    required this.templateType,
  });

  @override
  State<RegisterAddressBottomSheet> createState() =>
      _RegisterAddressBottomSheetState();
}

class _RegisterAddressBottomSheetState extends State<RegisterAddressBottomSheet>
    with TickerProviderStateMixin {
  final _bagPageCubit = Modular.get<BagPageCubit>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final cepTextEditingController = TextEditingController();
  final streetTextEditingController = TextEditingController();
  final numberTextEditingController = TextEditingController();
  final districtTextEditingController = TextEditingController();
  final additionalInfoTextEditingController = TextEditingController();
  final addressIdTextEditingController = TextEditingController();
  final recipientTextEditingController = TextEditingController();
  bool isFavorite = false;
  final formKey = GlobalKey<FormState>();

  bool hasStreetError = false;
  bool hasNumberError = false;
  bool hasDistrictError = false;
  bool hasRecipientError = false;
  bool hasAddressIdError = false;

  // Mapeia campos e suas funções de erro
  late final Map<TextEditingController, void Function(bool)> _validationMap = {
    addressIdTextEditingController: (hasError) =>
        setState(() => hasAddressIdError = hasError),
    numberTextEditingController: (hasError) =>
        setState(() => hasNumberError = hasError),
    districtTextEditingController: (hasError) =>
        setState(() => hasDistrictError = hasError),
    recipientTextEditingController: (hasError) =>
        setState(() => hasRecipientError = hasError),
    streetTextEditingController: (hasError) =>
        setState(() => hasStreetError = hasError),
  };

  @override
  void initState() {
    cepTextEditingController.text = widget.address.postalCode ?? '';
    streetTextEditingController.text = widget.address.street ?? '';
    districtTextEditingController.text = widget.address.neighborhood ?? '';

    // Adiciona listeners genéricos
    _validationMap.keys.forEach((controller) {
      controller.addListener(() {
        _validateField(controller);
      });
    });

    super.initState();
  }

  /// Validação genérica
  bool _validateField(TextEditingController controller) {
    final isValid = controller.text.trim().isNotEmpty;
    final updateError = _validationMap[controller];
    if (updateError != null) updateError(!isValid);
    return isValid;
  }

  /// Validação completa do formulário
  bool _validateForm() {
    bool isValid = true;
    for (final controller in _validationMap.keys) {
      if (!_validateField(controller)) {
        isValid = false;
      }
    }
    return isValid;
  }

  Future<void> _onSaveAddress() async {
    try {
      final response = await _orderFormCubit.saveAddress(
        address: AddressManager(
            postalCode: cepTextEditingController.text,
            street: streetTextEditingController.text,
            number: numberTextEditingController.text,
            neighborhood: districtTextEditingController.text,
            complement: additionalInfoTextEditingController.text,
            addressName: addressIdTextEditingController.text,
            receiverName: recipientTextEditingController.text,
            city: widget.address.city,
            state: widget.address.state,
            country: widget.address.country,
            isFavorite: isFavorite ? true : false),
      );
      if (isFavorite && response != null) {
        await _orderFormCubit.favoriteAddress(
          addressId: response.addressId ?? '',
        );
      }
      if (response != null) {
        await _orderFormCubit.updateAddressAndDelivery(
          address: Address.fromAddressManager(response),
        );

        await _orderFormCubit.getManagedAddresses();

        _bagPageCubit.loadAddressInfoFromOrderForm();
      }

      if (widget.onSaveAddressCallback != null) {
        widget.onSaveAddressCallback!();
      } else {
        if (mounted) {
          Modular.to.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao salvar endereço",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
      bloc: _bagPageCubit,
      builder: (context, bagPageState) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, orderFormState) {
            if (widget.templateType == TemplateType.bottomSheet) {
              return CdsAddressRegisterBottomSheet(
                addressRegisterErrorState: AddressRegisterErrorState(
                  street: hasStreetError,
                  number: hasNumberError,
                  district: hasDistrictError,
                  recipient: hasRecipientError,
                  addressId: hasAddressIdError,
                ),
                tokens: widget.tokens,
                formKey: formKey,
                cepTextEditingController: cepTextEditingController,
                streetTextEditingController: streetTextEditingController,
                numberTextEditingController: numberTextEditingController,
                districtTextEditingController: districtTextEditingController,
                additionalInfoTextEditingController:
                    additionalInfoTextEditingController,
                addressIdTextEditingController: addressIdTextEditingController,
                recipientTextEditingController: recipientTextEditingController,
                favoriteAddress: isFavorite,
                onDefineFavoriteAddress: (value) {
                  setState(() {
                    isFavorite = value;
                  });
                },
                onSaveAddress: () {
                  if (_validateForm() == true) {
                    FocusScope.of(context).unfocus();
                    _onSaveAddress();
                  }
                },
                onClose: () => Modular.to.pop(),
                onBack: widget.onBack,
                isLoading: bagPageState.isLoadingLocation ||
                    bagPageState.isLoadingAddress ||
                    orderFormState.isLoadingAddress ||
                    orderFormState.isLoadingDeliveryOptions,
                cityAndState: widget.address.city,
              );
            }

            if (widget.templateType == TemplateType.page) {
              return CdsAddressCompleteTemplate(
                addressRegisterErrorState: AddressCompleteErrorState(
                  street: hasStreetError,
                  number: hasNumberError,
                  district: hasDistrictError,
                  recipient: hasRecipientError,
                  addressId: hasAddressIdError,
                ),
                tokens: widget.tokens,
                formKey: formKey,
                cepTextEditingController: cepTextEditingController,
                streetTextEditingController: streetTextEditingController,
                numberTextEditingController: numberTextEditingController,
                districtTextEditingController: districtTextEditingController,
                additionalInfoTextEditingController:
                    additionalInfoTextEditingController,
                addressIdTextEditingController: addressIdTextEditingController,
                recipientTextEditingController: recipientTextEditingController,
                favoriteAddress: isFavorite,
                onDefineFavoriteAddress: (value) {
                  setState(() {
                    isFavorite = value;
                  });
                },
                onSaveAddress: () {
                  if (_validateForm() == true) {
                    FocusScope.of(context).unfocus();
                    _onSaveAddress();
                  }
                },
                onClose: () => Modular.to.pop(),
                onBack: widget.onBack,
                isLoading: bagPageState.isLoadingLocation ||
                    bagPageState.isLoadingAddress ||
                    orderFormState.isLoadingAddress ||
                    orderFormState.isLoadingDeliveryOptions,
                cityAndState: widget.address.city,
              );
            }

            return SizedBox.shrink();
          },
        );
      },
    );
  }
}
