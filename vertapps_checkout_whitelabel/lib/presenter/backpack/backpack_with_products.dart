import 'dart:async';
import 'dart:convert';
import 'package:azzas_analytics/events/checkout/payment_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/backpack/backpack.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/show_checkout_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/config/product_list_config.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/config/wishlist_config.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/address_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/cep_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/coupon_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/delivery_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/gift_package_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/html_renderer.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/product_info_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/seller_code_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class BackpackWithProducts extends StatefulWidget {
  final CheckoutDStokens tokens;
  final CdsPaymentArgs? paymentArgs;
  final bool hasCloseButton;

  const BackpackWithProducts({
    super.key,
    required this.tokens,
    this.paymentArgs,
    this.hasCloseButton = false,
  });

  @override
  BackpackWithProductsState createState() => BackpackWithProductsState();
}

class BackpackWithProductsState extends State<BackpackWithProducts>
    with TickerProviderStateMixin {
  final _bagPageCubit = Modular.get<BagPageCubit>();
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _paymentCubit = Modular.get<PaymentCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  final _wishlistCommonCubit = Modular.get<WishlistCommonCubit>();
  final _orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final _promotionsCubit = Modular.get<PromotionsCubit>();
  final _authCubit = Modular.get<AuthCubit>();
  final _unselectProductsManagerCubit =
      Modular.get<UnselectProductsManagerCubit>();
  final scrollController = ScrollController();
  final targetKey = GlobalKey();
  final scrollViewKey = GlobalKey();
  @override
  void initState() {
    super.initState();
    _bagPageCubit.getWishlistProducts();
    _bagPageCubit.loadBagComponents();
    _eventDispatcher.logViewCart();
    Future.delayed(Duration.zero, () {
      _promotionsCubit.loadApplicablePromotions();
      _loadAddressInfo();
    });
    _promotionsCubit.listenOrderFormChanges();
  }

  Future<void> _loadAddressFromMasterData() async {
    //[_orderFormCubit.state.addresses] verifica se a lista de endereços está vazia ou nula
    //se estiver vazia ou nula, chama o método para buscar os endereços no master data
    try {
      await _orderFormCubit.getManagedAddresses();
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao buscar endereços",
          tokens: widget.tokens,
          type: SnackbarType.error,
        );
      }
    }
  }

  Future<void> _addFavoriteAddressToOrderForm(
      AddressManager favoriteAddress) async {
    try {
      await _orderFormCubit.updateAddressAndDelivery(
        address: Address.fromAddressManager(favoriteAddress),
      );
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Endereço favorito selecionado",
          tokens: widget.tokens,
          type: SnackbarType.success,
        );
      }
    } catch (_) {}
  }

  Future<void> _loadAddressInfo() async {
    try {
      final userIsNotLoggedIn = _authCubit.state.isLoggedIn == false;

      final isAddressesIsNotEmpty = _orderFormCubit.state.addresses?.isNotEmpty;

      if (userIsNotLoggedIn || isAddressesIsNotEmpty == true) {
        return;
      }

      CdsBackdrop.show(context, widget.tokens);
      await _loadAddressFromMasterData();

      //[currentShippingAddress] endereço selecionado no orderForm
      final currentShippingAddress =
          _orderFormCubit.state.orderForm?.shippingData?.address;

      //[availableAddresses] endereços disponíveis no master data
      final availableAddresses = _orderFormCubit.state.addresses;

      //[isCurrentAddressAvailable] verifica se o endereço selecionado no orderForm existe no master data
      final isCurrentAddressAvailable = availableAddresses?.any(
            (address) => address.addressId == currentShippingAddress?.addressId,
          ) ??
          false;

      //[favoriteAddress] endereço favorito no master data
      final favoriteAddress = availableAddresses?.firstWhereOrNull(
        (address) => address.isFavorite ?? false,
      );

      //se existe um endereço favorito no master data e o endereço selecionado no orderForm não existir
      // ou não estiver disponível, atualiza o endereço selecionado no orderForm com o endereço favorito
      if (favoriteAddress != null &&
          (currentShippingAddress == null || !isCurrentAddressAvailable)) {
        await _addFavoriteAddressToOrderForm(favoriteAddress);
      }
      await _orderFormCubit.updateAddressAndDelivery();
      _bagPageCubit.loadAddressInfoFromOrderForm();
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao buscar endereços",
          tokens: widget.tokens,
          type: SnackbarType.error,
        );
      }
    } finally {
      CdsBackdrop.hide();
    }
  }

  Future<void> _removeCoupon() async {
    try {
      CdsBackdrop.show(context, widget.tokens);
      await _orderCheckoutHandler.removeCoupon();
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Cupom removido com sucesso",
          type: SnackbarType.success,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    } catch (e) {
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao remover cupom",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  void _showBottomSheetAddCoupon() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return CouponBottomSheet(
            tokens: widget.tokens,
            onCouponRemoved: _removeCoupon,
          );
        },
      );
    });
  }

  void _showCepBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return CepBottomSheet(tokens: widget.tokens);
        },
      );
    });
  }

  void _showDeliveryBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return DeliveryBottomSheet(
            tokens: widget.tokens,
            packs: packages(_orderFormCubit.state, _bagPageCubit.state),
          );
        },
      );
    });
  }

  void _showAdressesBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return AddressBottomSheet(tokens: widget.tokens);
        },
      );
    });
  }

  void scrollToTarget() async {
    final targetContext = targetKey.currentContext;
    final scrollViewContext = scrollViewKey.currentContext;

    if (targetContext != null && scrollViewContext != null) {
      final box = targetContext.findRenderObject() as RenderBox;
      final scrollBox = scrollViewContext.findRenderObject() as RenderBox;

      final offset = box.localToGlobal(Offset.zero, ancestor: scrollBox).dy;

      await scrollController.animateTo(
        scrollController.offset + offset,
        duration: const Duration(milliseconds: 1500),
        curve: Curves.easeInOut,
      );
      setShowAddressCard(false);
    }
  }

  void _showGiftPackagingBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return GiftPackageBottomSheet(tokens: widget.tokens);
        },
      );
    });
  }

  void _showSellerCodeBottomSheet() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return SellerCodeBottomSheet(
            tokens: widget.tokens,
            onRemoveSellerCode: _removeSellerCode,
          );
        },
      );
    });
  }

  Future<void> _removeSellerCode() async {
    try {
      CdsBackdrop.show(context, widget.tokens);
      await _orderCheckoutHandler.removeSellerCode();
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Código de vendedora removido com sucesso",
          type: SnackbarType.success,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    } catch (e) {
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao remover código de vendedora",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  void logRemoveFromCart({String? productId, String? itemId}) {
    _eventDispatcher.logRemoveFromCart(
      product: _bagPageCubit.findProductByItemId(itemId ?? '') ,
      itemId: itemId,
      region: 'checkout'
    );
  }

  Future<bool> _removeProduct(
    OrderFormProduct product,
  ) async {
    try {
      CdsBackdrop.show(context, widget.tokens);
      if (product.itemId == null || product.itemId == "") {
        throw Exception();
      }
      if (_unselectProductsManagerCubit.productIsSelected(product.itemId!)) {
        await _unselectProductsManagerCubit.deleteProduct(product.itemId!);
      } else {
        await _orderCheckoutHandler.updateQuantityItem(
            itemId: product.itemId!, quantity: 0);
        _bagPageCubit.loadAddressInfoFromOrderForm();
      }
       logRemoveFromCart(
         productId: product.productId,
         itemId: product.itemId
       );
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Produto removido com sucesso",
          type: SnackbarType.success,
          tokens: widget.tokens,
          buttonText: "Desfazer",
          showButton: true,
          showCloseIcon: false,
          buttonPosition: SnackabarButtonPosition.side,
          onPressButton: () {
            reAddProductToBag(product);
          },
        );
      }

      return true;
    } catch (e) {
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao remover produto",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {},
        );
      }

      return false;
    }
  }

  void reAddProductToBag(OrderFormProduct product) async {
    await _orderCheckoutHandler.addItems(items: [
      OrderAddItem(
          id: product.itemId!,
          quantity: product.quantity ?? 1,
          seller: product.seller!)
    ]);
  }

  void logAddToWishlist(
      {OrderFormProduct? orderFormProduct, Product? product, int index = 0}) {
    if (product != null) {
      _eventDispatcher.logAddToWishlist(
          index: index, product: product, region: 'checkout');
    }
    if (orderFormProduct != null) {
      _eventDispatcher.logAddToWishlist(
          index: index, orderFormProduct: orderFormProduct, region: 'checkout');
    }
  }

  Future<bool> _addProductWishList(
      String productId, String itemId, Product? product) async {
    if (!_unselectProductsManagerCubit.productIsSelected(itemId)) {
      final orderFormProduct = _orderFormCubit.orderForm.items
          ?.firstWhereOrNull((product) => product.productId == productId);
      final orderFormProductIndex =
          _orderFormCubit.orderForm.indexProductInBag(orderFormProduct!);
      logAddToWishlist(
          orderFormProduct: orderFormProduct, index: orderFormProductIndex);
    } else {
      final productIndex = _unselectProductsManagerCubit
          .state.unselectedProducts
          .indexWhere((product) => product.productId == productId);
      final index =
          productIndex + (_orderFormCubit.orderForm.items?.length ?? 0);
      logAddToWishlist(product: product, index: index);
    }

    try {
      CdsBackdrop.show(context, widget.tokens);
      await _wishlistCommonCubit.addProduct(productId);

      if (_unselectProductsManagerCubit.productIsSelected(itemId)) {
        await _unselectProductsManagerCubit.deleteProduct(itemId);
      } else {
        await _orderCheckoutHandler.updateQuantityItem(
            itemId: itemId, quantity: 0);
        _bagPageCubit.loadAddressInfoFromOrderForm();
      }

      _bagPageCubit.getWishlistProducts();
      CdsBackdrop.hide();

      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Produto adicionado aos desejos",
          type: SnackbarType.success,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
      return true;
    } catch (e) {
      print(e);
      CdsBackdrop.hide();
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          text: "Erro ao adicionar produto aos desejos",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
      return false;
    }
  }

  Future<void> _showBottomSheetInfoProduct({
    required String itemId,
    required String productId,
    required bool isUnselectedProduct,
  }) async {
    try {
      final product = _bagPageCubit.findProductByItemId(itemId);
      final index = _bagPageCubit.getIndexProduct(
          itemId: itemId, isUnselectedProduct: isUnselectedProduct);

      if (product == null) return;
      if (!mounted) return;

      _eventDispatcher.logSelectItem(
          itemListName: 'checkout', product: product, index: index, region: 'checkout');

      showCheckoutBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return ProductInfoBottomSheet(
            tokens: widget.tokens,
            product: product,
            itemId: itemId,
            isUnselectedProduct: isUnselectedProduct,
            index: index,
            onSaveForLater: (_) async {
              await _addProductWishList(productId, itemId, product);
              if (mounted) {
                Navigator.pop(context);
              }
            },
          );
        },
      );
    } catch (_) {
      if (mounted) {
        Navigator.pop(context);
        CdsSnackBar.show(
          context: context,
          text: "Erro ao buscar informações do produto",
          type: SnackbarType.error,
          tokens: widget.tokens,
          onPressButton: () {
            CdsSnackBar.hideSnackBar();
          },
        );
      }
    }
  }

  Future<void> _selectRecurrentCreditCardAsPayment(
      CreditCardInfoData selectedCard) async {
    try {
      final installments = InstallmentOrderForm(
        count: 1,
        value: _orderFormCubit.orderForm.value?.toInt(),
      );
      await _paymentCubit.selectCreditCardPayment(
        creditCardInfo: selectedCard,
        installment: installments,
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<bool> selectPayment(
      PaymentType paymentSelected, CreditCardModel? cardFavorited) async {
    try {
      switch (paymentSelected) {
        case PaymentType.pix:
          await _paymentCubit.selectPixPayment();
          _eventDispatcher.logAddPaymentInfo(
              paymentType: AzzasAnalyticsPaymentType.pix);
          return true;
        case PaymentType.creditCard:
          if (cardFavorited == null) {
            return false;
          } else {
            await _selectRecurrentCreditCardAsPayment(
                cardFavorited.toCreditCardInfoData());
            _eventDispatcher.logAddPaymentInfo(
                paymentType: AzzasAnalyticsPaymentType.creditCard);
            return true;
          }
        case PaymentType.applePay:
          _paymentCubit.clearCreditCardError();
          await _paymentCubit.selectApplePay();
          _eventDispatcher.logAddPaymentInfo(
              paymentType: AzzasAnalyticsPaymentType.applePay);
          return true;
        default:
          return false;
      }
    } catch (e) {
      return false;
    }
  }

  Future<bool> selectFavoritePayment() async {
    final cards = _orderFormCubit.orderForm.getAvailableAccounts
        .map((card) => card.toCreditCardModel(context: context))
        .toList();

    final favoriteOption = await _orderFormCubit.getFavoritePaymentOption();

    if (favoriteOption == null) {
      return false;
    }

    CreditCardModel? cardFavorited =
        cards.firstWhereOrNull((c) => c.cardId == favoriteOption);

    final favoritedMethod = cardFavorited != null
        ? PaymentType.creditCard
        : PaymentType.values.firstWhereOrNull((p) => p.value == favoriteOption);

    if (favoritedMethod == null) {
      return false;
    }

    return await selectPayment(favoritedMethod, cardFavorited);
  }

  bool _clientProfileIsComplete() {
    final orderFormCubit = Modular.get<OrderFormCubit>();
    final clientProfileData = orderFormCubit.orderForm.clientProfileData;
    return clientProfileData?.isComplete() ?? false;
  }

  void _handleUserIncompleteData() {
    Modular.to.pushNamed("/checkout/complete_user_data", arguments: {
      'isRegister': false,
      "overrideFinishFlow": () => _handleLoggedInUser(),
    }).then((_) => _eventDispatcher.logViewCart());
  }

  void _navigateToDeliveryAddressFilled() {
    Modular.to
        .pushNamed(
          '/checkout/address-cep',
          arguments: CdsAddressCepArgs(
            overrideFinishFlow: () => _goToPayment(),
            paymentArgs: widget.paymentArgs,
            currentAddress: _bagPageCubit.state.currentAddress,
          ),
        )
        .then((_) => _eventDispatcher.logViewCart());
  }

  void _proceedToNextSteps() async {
    final addressNumber =
        _orderFormCubit.orderForm.shippingData?.address?.number;
    final addressReceiver =
        _orderFormCubit.orderForm.shippingData?.address?.receiverName;
    if (addressNumber == null || addressReceiver == null) {
      _navigateToDeliveryAddressFilled();

      return;
    }
    _goToPayment();
  }

  void _goToPayment() async {
    final hasFavoritePayment = await selectFavoritePayment();
    final payments = _orderFormCubit.orderForm.paymentData?.payments;

    final firstSelectedPayment = _orderFormCubit.orderForm.firstSelectedPayment;

    final hasInstallments = firstSelectedPayment?.installments != null &&
        firstSelectedPayment!.installments > 0;
    final hasAccountId = firstSelectedPayment?.accountId != null;
    final hasBin = firstSelectedPayment?.bin != null;
    final hasIncompleteData = (!hasInstallments || !hasBin || !hasAccountId) &&
        _orderFormCubit.orderForm.isCreditCardSelected;

    if ((payments == null || payments.isEmpty || hasIncompleteData) &&
        !hasFavoritePayment) {
      Modular.to.pushNamed(
        '/checkout/payment',
        arguments: widget.paymentArgs,
      );
    } else {
      await _eventDispatcher.logAddPaymentInfo(
        preFilled: true,
      );
      Modular.to.pushNamed(
        '/checkout/order-review',
        arguments: CdsOrderReviewArgs(
          goToBackPack: (bool? showAddressCard) {
            widget.paymentArgs?.goToBackPack.call(showAddressCard);
          },
          onTapViewBalance: () {
            widget.paymentArgs?.onTapViewBalance.call();
          },
          redirectToPayment: true,
        ),
      );
    }
  }

  void _handleLoggedInUser() {
    if (!_clientProfileIsComplete()) {
      _handleUserIncompleteData();
      return;
    } else {
      _proceedToNextSteps();
      return;
    }
  }

  void setShowAddressCard(bool value) {
    if (_bagPageCubit.state.showAddressCard) {
      _showAdressesBottomSheet();
    }
    _bagPageCubit.setShowAddressCard(value);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UnselectProductsManagerCubit,
            UnselectProductsManagerState>(
        bloc: _unselectProductsManagerCubit,
        builder: (context, unselectProductsState) {
          return BlocBuilder<OrderFormCubit, OrderFormState>(
            bloc: _orderFormCubit,
            builder: (context, orderFormState) {
              return BlocBuilder<BagPageCubit, BagPageState>(
                bloc: _bagPageCubit,
                builder: (context, bagPageState) {
                  final isAllProductsUnselected =
                      (unselectProductsState.unselectedProducts.length != 0) &&
                          (orderFormState.orderForm?.getAddedItems.isEmpty ??
                              true);
                  return BlocBuilder<PromotionsCubit, PromotionsState>(
                      bloc: _promotionsCubit,
                      builder: (context, promotionsState) {
                        final firstPromotion =
                            promotionsState.promotions.isNotEmpty
                                ? promotionsState.promotions.firstWhere(
                                    (p) => p.isActive == true,
                                  )
                                : null;
                        return Scaffold(
                          extendBody: true,
                          backgroundColor: Colors.transparent,
                          floatingActionButtonLocation:
                              FloatingActionButtonLocation.centerFloat,
                          floatingActionButton: _BuyButton(
                            isAllProductsUnselected: isAllProductsUnselected,
                            tokens: widget.tokens,
                            isLoading: orderFormState.isLoading ||
                                orderFormState.isLoadingAddress,
                            orderFormState: orderFormState,
                            onBuyButton: () {
                              if (!_authCubit.state.isLoggedIn) {
                                Modular.to.pushNamed(
                                  '/checkout/login',
                                  arguments: () {
                                    _handleLoggedInUser();
                                  },
                                );
                              } else {
                                _handleLoggedInUser();
                              }
                              _eventDispatcher.logBeginCheckout();
                            },
                          ),
                          body: SafeArea(
                            child: CdsBackpackTemplate(
                              isAllProductsUnselected: isAllProductsUnselected,
                              scrollViewKey: scrollViewKey,
                              targetKey: targetKey,
                              scrollController: scrollController,
                              showAddressCard: bagPageState.showAddressCard,
                              showAddresses: _showAdressesBottomSheet,
                              hasCloseButton: widget.hasCloseButton,
                              scrollToTarget: scrollToTarget,
                              productListConfig:
                                  ProductListConfigFactory.create(
                                bagPageCubit: _bagPageCubit,
                                onProductInfo: ({
                                  required String productId,
                                  required String itemId,
                                  bool isUnselectedProduct = false,
                                }) async {
                                  return await _showBottomSheetInfoProduct(
                                    productId: productId,
                                    itemId: itemId,
                                    isUnselectedProduct: isUnselectedProduct,
                                  );
                                },
                                orderFormCubit: _orderFormCubit,
                                unselectProductsManagerCubit:
                                    _unselectProductsManagerCubit,
                                onAddProductWishList:
                                    (productId, itemId) async {
                                  return await _addProductWishList(
                                      productId,
                                      itemId,
                                      bagPageState.bagProducts.firstWhere(
                                          (product) =>
                                              product.productId == productId));
                                },
                                onRemoveProduct: (itemId) async {
                                  return await _removeProduct(itemId);
                                },
                                context: context,
                                tokens: widget.tokens,
                                vsync: this,
                              ),
                              brandInformationConfigs: [
                                ...bagPageState
                                        .highlightOptionalCmsComponent?.items
                                        .map((e) => CdsBrandInformationConfig(
                                            title: e.title,
                                            description: e.subtitle,
                                            imageUrl: e
                                                .image?.data.attributes.url)) ??
                                    []
                              ],
                              couponConfig: couponWidget(),
                              sellerCodeConfig:
                                  sellerCodeWidget(orderFormState),
                              wishlistConfig: WishlistConfigFactory.create(
                                bagPageCubit: _bagPageCubit,
                                context: context,
                                tokens: widget.tokens,
                                vsync: this,
                                eventDispatcher: _eventDispatcher,
                              ),
                              onShareTap: () {},
                              setShowAddressCard: setShowAddressCard,
                              onEditShipping:
                                  orderFormState.addresses != null &&
                                          _authCubit.state.isLoggedIn
                                      ? _showAdressesBottomSheet
                                      : _showCepBottomSheet,
                              giftPackagingConfig:
                                  giftPackagingWidget(orderFormState),
                              giftsConfigs: orderFormState.orderForm
                                          ?.selectableGifts?.isNotEmpty ==
                                      true
                                  ? selectableGiftWidget(orderFormState)
                                  : null,
                              freeDeliveryConfig: CdsFreeDeliveryConfig(
                                title: firstPromotion?.title != null
                                    ? HtmlRenderer(firstPromotion!.title!)
                                    : null,
                                subtitle: firstPromotion?.subtitle != null
                                    ? HtmlRenderer(firstPromotion!.subtitle!)
                                    : null,
                                feedbackText: firstPromotion?.feedbackText,
                                feedbackSubtitle:
                                    firstPromotion?.feedbackSubtitle,
                                buttonText: firstPromotion?.buttonText ?? '',
                                isActive: firstPromotion?.isActive ?? false,
                                showProgressBar:
                                    firstPromotion?.showProgress ?? false,
                                leading: firstPromotion?.iconUrl != null
                                    ? Image.network(firstPromotion!.iconUrl!)
                                    : null,
                                amountNeededForFreeDelivery: promotionsState
                                        .promotionsApplicability.isNotEmpty
                                    ? promotionsState.promotionsApplicability
                                        .first.totalValueToActivate
                                        .toInt()
                                    : 0,
                                amountSpent: promotionsState
                                        .promotionsApplicability.isNotEmpty
                                    ? promotionsState.promotionsApplicability
                                        .first.remainingValueToActivate
                                        .toInt()
                                    : 0,
                                onPressButton: () => NavigatorDynamic.call(
                                    promotionsState.promotions.first
                                            .buttonNavigateTo ??
                                        ''),
                              ),
                              pickupDeliveryConfig:
                                  bagPageState.packages?.isNotEmpty == true
                                      ? pickupDeliveryWidget(bagPageState)
                                      : null,
                              orderResumeConfig: CdsOrderResumeConfig(
                                maxInstallments: orderFormState
                                    .orderForm?.maxInstallmentsInt,
                                productLength: orderFormState
                                        .orderForm?.getAddedItems.length ??
                                    0,
                                totalProducts: orderFormState
                                        .orderForm?.getTotalItemsFormatted ??
                                    '',
                                totalValue:
                                    orderFormState.orderForm?.totalFormatted ??
                                        '',
                                deliveryValue:
                                    orderFormState.orderForm?.shippingPrice ??
                                        '',
                                discountValue:
                                    (orderFormState.orderForm?.hasDiscount ??
                                            false)
                                        ? (orderFormState
                                                .orderForm?.discountPrice ??
                                            '')
                                        : '',
                                totalCreditsValue: ((orderFormState
                                                .orderForm
                                                ?.paymentData
                                                ?.giftCards
                                                ?.isNotEmpty ??
                                            false) &&
                                        (orderFormState.orderForm
                                                ?.getGiftCardValues() !=
                                            0))
                                    ? CurrencyHelper.format(
                                        amount: orderFormState.orderForm
                                                ?.getGiftCardValues() ??
                                            0,
                                        dividedBy100: true,
                                      )
                                    : null,
                              ),
                              tokens: widget.tokens,
                            ),
                          ),
                        );
                      });
                },
              );
            },
          );
        });
  }

  /// Cria e retorna uma instância de [CdsCouponConfig] que gerencia a funcionalidade
  /// de cupons na mochila. Isso inclui:
  /// - Exibição do cupom atualmente aplicado
  /// - Gerenciamento da adição/alteração de cupons através de um bottom sheet
  /// - Controle da remoção de cupons
  CdsCouponConfig couponWidget() {
    return CdsCouponConfig(
      coupon: _orderFormCubit.getCouponApplied(),
      onTapApllyOrChangeCoupon: _showBottomSheetAddCoupon,
      onTapRemoveCoupon: () async {
        await _removeCoupon();
      },
    );
  }

  /// Cria e retorna uma instância de [CdsSellerCodeConfig] que gerencia a funcionalidade
  /// de código de vendedor na mochila. Isso inclui:
  /// - Exibição do código de vendedora aplicado
  /// - Gerenciamento da adição/remoção de códigos através de um bottom sheet
  CdsSellerCodeConfig sellerCodeWidget(OrderFormState orderFormState) {
    return CdsSellerCodeConfig(
      onTapApllyOrChangeSellerCode: _showSellerCodeBottomSheet,
      onTapRemoveSellerCode: _removeSellerCode,
      selectedSellerCode: _orderFormCubit.salesPersonCodeAppliedFormatted !=
              null
          ? SelectedSellerCode(
              sellerCode: _orderFormCubit.salesPersonCodeAppliedFormatted ?? '',
            )
          : null,
    );
  }

  /// Cria e retorna uma instância de [GiftPackagingConfig] que gerencia a funcionalidade
  /// de adicionar embalagem de presente nos produtos na mochila. Isso inclui:
  /// - Exibição da quantidade de produtos disponíveis para embalar ou produtos selecionados
  /// - Gerenciamento da adição/remoção de embalagem através de um bottom sheet
  GiftPackagingConfig giftPackagingWidget(OrderFormState orderFormState) {
    int getPackagingItems() {
      int packagingItems = 0;
      if (orderFormState.orderForm?.items == null) return 0;
      for (var item in orderFormState.orderForm!.items!) {
        if (item.bundleItems != null &&
            item.bundleItems!.isNotEmpty &&
            item.bundleItems!.length > 0) {
          packagingItems++;
        }
      }

      return packagingItems;
    }

    return GiftPackagingConfig(
      onTap: _showGiftPackagingBottomSheet,
      productsAvailables: orderFormState.orderForm?.getItemsSendToGift.length,
      productsSelected: getPackagingItems(),
    );
  }

  CdsPickupDeliveryInformationConfig pickupDeliveryWidget(
    BagPageState bagPageState,
  ) {
    return CdsPickupDeliveryInformationConfig(
      title: 'Como você deseja receber?',
      pickupStoreConfig: pickupOptionConfig(bagPageState),
      deliveryOptionConfig: deliveryOptionConfig(bagPageState),
    );
  }

  void changeStoreAction() {
    showCheckoutBottomSheet(
      context: context,
      vsync: this,
      builder: (_) {
        return CdsPickupBottomSheet(
          tokens: widget.tokens,
          params: CdsPickupParams(
            onClose: () {
              Navigator.pop(context);
            },
            totalItemBag: _orderFormCubit.orderForm.items?.length ?? 0,
            onStoreSelected: (String storeName) async {
              CdsBackdrop.show(context, widget.tokens);
              await _bagPageCubit.setPickupStoreByName(storeName: storeName);
              CdsBackdrop.hide();
              if (mounted) {
                Navigator.pop(context);
                deliveryDetailsAction();
              }
            },
            options: _bagPageCubit.getAllPickupOptionsReduce(),
          ),
        );
      },
    );
  }

  List<CdsPackModel> packages(
    OrderFormState orderFormState,
    BagPageState bagPageState,
  ) {
    return orderFormState.packages?.map(
          (package) {
            return CdsPackModel.create(
              products: [],
              hasSelectedOption: bagPageState.selectedDeliveryOption != null,
              imagesUrl: package.imagesUrl,
              shippingOptions: shippingOptions(package),
            );
          },
        ).toList() ??
        [];
  }

  List<CdsShippingOptionModel> shippingOptions(PackageModel package) {
    return package.deliveryOptions
        .map((op) => CdsShippingOptionModel(
              deliveryChannel: op.deliveryChannel ?? '',
              id: op.id ?? '',
              itemIndex: package.deliveryOptions.indexOf(op),
              pickupStoreInfo: null,
              selectedDeliveryChannel: op.deliveryChannel ?? '',
              selectedSla: op.id ?? '',
              shippingEstimate: int.parse(op.shippingEstimate ?? ''),
              total: (op.price ?? 0) / 100,
            ))
        .toList();
  }

  void deliveryDetailsAction() {
    final pickupSelected =
        _bagPageCubit.getAllPickupOptionsReduce().firstWhereOrNull((option) {
      return option.storeName == _bagPageCubit.getSelectedCdsStore()?.storeName;
    });

    if (pickupSelected == null) return;

    showCheckoutBottomSheet(
      context: context,
      vsync: this,
      builder: (_) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: _orderFormCubit,
          builder: (context, orderFormState) {
            CdsShippingOptionModel? deliverySelected;

            if (_bagPageCubit.state.selectedDeliveryOption != null) {
              final deliveryOptionSelected =
                  _bagPageCubit.state.packages?.first.deliveryOptionSelected;
              deliverySelected = CdsShippingOptionModel(
                id: deliveryOptionSelected?.id ?? '',
                deliveryChannel: deliveryOptionSelected?.deliveryChannel ?? '',
                total: (deliveryOptionSelected?.price ?? 0) / 100,
                shippingEstimate:
                    int.parse(deliveryOptionSelected?.shippingEstimate ?? ''),
                itemIndex: 0,
                selectedSla: deliveryOptionSelected?.id ?? '',
                selectedDeliveryChannel:
                    deliveryOptionSelected?.deliveryChannel ?? '',
                pickupStoreInfo: null,
              );
            }

            return CdsPickupConfirmBottomSheet(
              tokens: widget.tokens,
              params: CdsConfirmPickupParams(
                onClose: () {
                  Navigator.pop(context);
                },
                onPickupConfirmed: (receiverName) async {
                  CdsBackdrop.show(context, widget.tokens);
                  await _orderFormCubit.updateReceiverName(
                      receiverName: receiverName);
                  CdsBackdrop.hide();
                  Navigator.pop(context);
                },
                onDeliveryConfirmed: () {
                  Navigator.pop(context);
                },
                selectedShippingOption:
                    orderFormState.selectedDeliveryOption != null
                        ? deliverySelected
                        : null,
                packs: packages(_orderFormCubit.state, _bagPageCubit.state),
                totalItemBag: _orderFormCubit.orderForm.items?.length ?? 0,
                onDeliveryAddressPressed: () {
                  Navigator.pop(context);
                  _showAdressesBottomSheet();
                },
                onShippingOptionSelected: (value, option) async {
                  try {
                    final packages = _orderFormCubit.state.packages;

                    if (value) {
                      if (packages == null) return;
                      final deliveryOption = AzzasCommonDeliveryOption(
                        id: option.itemIndex.toString(),
                        deliveryPrice:
                            CurrencyHelper.format(amount: option.total),
                        deliveryTitle: option.selectedSla,
                        deliveryTime:
                            option.getShippingEstimatedFormatted ?? '',
                      );

                      CdsBackdrop.show(context, widget.tokens);
                      await _bagPageCubit.updateSelectedAddress(deliveryOption);
                      _orderFormCubit.updatePackages(packages, deliveryOption);
                    }
                  } catch (e) {
                    debugPrint('Error updating delivery option: $e');
                    if (mounted) {
                      CdsSnackBar.show(
                        context: context,
                        text: "Erro ao alterar opção de entrega",
                        type: SnackbarType.error,
                        tokens: widget.tokens,
                      );
                    }
                  } finally {
                    CdsBackdrop.hide();
                  }
                },
                onStoreSwitch: () {
                  Navigator.pop(context);
                  changeStoreAction();
                },
                receiverName: _orderFormCubit
                        .state.orderForm?.shippingData?.address?.receiverName ??
                    "",
                option: pickupSelected,
              ),
            );
          },
        );
      },
    );
  }

  CdsPickupStoreOptionInformationConfig pickupOptionConfig(
    BagPageState bagPageState,
  ) {
    final optionSelected =
        _bagPageCubit.getAllPickupOptionsReduce().firstWhereOrNull((option) {
      return option.storeName == _bagPageCubit.getSelectedCdsStore()?.storeName;
    });

    return CdsPickupStoreOptionInformationConfig(
      selected: bagPageState.shippingType == ShippingType.pickup,
      shouldDisplay: _bagPageCubit.hasPickupOptions(),
      date: (_orderFormCubit.state.packages ?? []).map((package) {
        return package.deliveryOptionSelected.shippingEstimatedFormatted ?? "";
      }).toList(),
      changeStoreCallback: changeStoreAction,
      deliveryDetailsCallback: deliveryDetailsAction,
      estimate: optionSelected?.getEstimateFullString() ?? '',
      singlePackage: optionSelected?.products.length ==
          _orderFormCubit.orderForm.items?.length,
      onSelect: () async {
        CdsBackdrop.show(context, widget.tokens);
        final shippingData = _bagPageCubit.getShippingOptionsData();
        final store = shippingData?.getStoreWithMostProducts();
        if (store == null) return;
        await _bagPageCubit.setPickupStoreBySlas(store: store);
        CdsBackdrop.hide();
      },
      selectedStore: _bagPageCubit.getSelectedCdsStore(),
      availableStores: () {},
      showAvailableStores: true,
      discount: "",
      deliveryValuOtherItems: _orderFormCubit.orderForm.shippingPrice,
      changeSelectedStore: (store) {},
    );
  }

  CdsDeliveryOptionInformationConfig deliveryOptionConfig(
    BagPageState bagPageState,
  ) {
    final currentBagAddress = _bagPageCubit.state.currentAddress;
    final currentOrderFormAddress =
        _orderFormCubit.state.orderForm?.shippingData?.address;
    final isSelected = bagPageState.shippingType == ShippingType.delivery;

    return CdsDeliveryOptionInformationConfig(
      selected: isSelected,
      onSelect: (value) async {
        _eventDispatcher.logAddShippingInfo();
        CdsBackdrop.show(context, widget.tokens);
        await _bagPageCubit.resetToDeliveryOnly();
        CdsBackdrop.hide();
      },
      fastDeliveryAvailable:
          (bagPageState.packages?.first.deliveryOptions.length ?? 0) > 1,
      totalValue: _orderFormCubit.orderForm.shippingValue,
      date: [
        bagPageState.packages?.first.deliveryOptionSelected
                .shippingEstimatedFormatted ??
            ''
      ],
      packages: bagPageState.packages,
      hyperLinkCallback: isSelected
          ? _showDeliveryBottomSheet
          : _bagPageCubit.resetToDeliveryOnly,
      hasSelectedAddress: currentOrderFormAddress != null,
      formattedAddress: (currentOrderFormAddress?.isComplete ?? false)
          ? currentOrderFormAddress?.formattedCompleteAddress ?? ''
          : currentOrderFormAddress?.formattedAddresIncomplete ?? '',
      isFavorite:
          currentBagAddress?.addressId == currentOrderFormAddress?.addressId
              ? (currentBagAddress?.isFavorite ?? false)
              : (currentOrderFormAddress?.isFavorite ?? false),
    );
  }

  /// Cria e retorna uma instância de [CdsGiftsConfig] que gerencia a funcionalidade
  /// de adicionar brinde na mochila. Isso inclui:
  /// - Exibição da quantidade de produtos disponíveis para embalar ou produtos selecionados
  /// - Gerenciamento da adição/remoção de embalagem através de um bottom sheet
  CdsGiftsConfig selectableGiftWidget(OrderFormState orderFormState) {
    return CdsGiftsConfig(
      giftsList: orderFormState.orderForm?.selectableGifts
              ?.map((giftItem) => CdsGiftCardConfig(
                    id: giftItem.id,
                    imageUrl: giftItem.getImage,
                    isSelected: giftItem.isSelected,
                    name: giftItem.getName,
                    quantity: giftItem.quantity,
                    size: giftItem.getSize,
                    seller: giftItem.seller,
                  ))
              .toList() ??
          [],
      title: 'Você ganhou um brinde!',
      description: 'Escolha aqui qual você mais gostou:',
      onSelected: (value, cdsGiftCard) {
        if (value) {
          _orderFormCubit.addSelectableGiftToBag(
            id: cdsGiftCard.id,
            seller: cdsGiftCard.seller,
          );
        }
      },
    );
  }
}

class _BuyButton extends CdsBaseStatelessComponent {
  final VoidCallback onBuyButton;
  final OrderFormState orderFormState;
  final bool isLoading;
  final bool isAllProductsUnselected;
  const _BuyButton({
    required super.tokens,
    required this.onBuyButton,
    required this.orderFormState,
    required this.isLoading,
    required this.isAllProductsUnselected,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: tokens.layout.padding.lg,
      ),
      child: CdsPrimaryButton(
        tokens: tokens,
        onPressed: onBuyButton,
        //[orderFormState.orderForm?.getUnavailableItems.isNotEmpty == true] verifica se tem produtos indisponíveis
        //[orderFormState.orderForm?.getAddedItems.isEmpty == true] verifica se tem produtos adicionados
        isDisabled:
            orderFormState.orderForm?.getUnavailableItems.isNotEmpty == true ||
                orderFormState.orderForm?.getAddedItems.isEmpty == true,
        expanded: true,
        size: CdsButtonSize.medium,
        buttonText: isAllProductsUnselected
            ? "Nenhum produto selecionado"
            : 'Fechar compra (${orderFormState.orderForm?.items?.length ?? 0}) • ${orderFormState.orderForm?.totalFormatted}',
        isLoading: isLoading,
      ),
    );
  }
}
